import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import fs from 'fs';
import path from 'path';
import archiver from 'archiver';

// Upload images for a client
export const uploadImages = async (
  studioId: string,
  clientId: string,
  files: Express.Multer.File[],
  descriptions?: string[]
) => {
    // Verify client belongs to studio
    const client = await prisma.client.findFirst({
      where: { id: clientId, studioId },
    });

    if (!client) {
      throw new ApiError(404, 'Client not found');
    }

    const uploadedImages = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const description = descriptions?.[i] || '';

      // Simple path construction using filename
      const relativePath = `uploads/images/${file.filename}`;

      const image = await prisma.image.create({
        data: {
          filename: file.filename,
          originalName: file.originalname,
          path: relativePath, // Optimized version for display
          size: file.size,
          mimeType: file.mimetype,
          description,
          clientId,
          studioId,
        },
      });

      uploadedImages.push(image);
    }

    return uploadedImages;
};

// Get images for a client (studio view)
export const getClientImages = async (studioId: string, clientId: string) => {
    // Verify client belongs to studio
    const client = await prisma.client.findFirst({
      where: { id: clientId, studioId },
    });

    if (!client) {
      throw new ApiError(404, 'Client not found');
    }

    return await prisma.image.findMany({
      where: { clientId, studioId },
      orderBy: { uploadedAt: 'desc' },
    });
};

// Get images for client portal
export const getMyImages = async (clientId: string) => {
  return await prisma.image.findMany({
    where: { clientId },
    orderBy: { uploadedAt: 'desc' },
  });
};

// Delete image
export const deleteImage = async (imageId: string, studioId: string) => {
    const image = await prisma.image.findFirst({
      where: { id: imageId, studioId },
    });

    if (!image) {
      throw new ApiError(404, 'Image not found');
    }

    // Delete file from filesystem
    try {
      if (fs.existsSync(image.path)) {
        fs.unlinkSync(image.path);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }

    // Delete from database
    await prisma.image.delete({
      where: { id: imageId },
    });

    return { message: 'Image deleted successfully' };
};

// Update image description
export const updateImage = async (imageId: string, studioId: string, data: {
  description?: string;
  tags?: string;
}) => {
  const image = await prisma.image.findFirst({
    where: { id: imageId, studioId },
  });

  if (!image) {
    throw new ApiError(404, 'Image not found');
  }

  return await prisma.image.update({
    where: { id: imageId },
    data,
  });
};

// Create ZIP file for client download
export const createClientZip = async (clientId: string): Promise<string> => {
    const images = await prisma.image.findMany({
      where: { clientId },
    });

    if (images.length === 0) {
      throw new ApiError(404, 'No images found for this client');
    }

    const client = await prisma.client.findUnique({
      where: { id: clientId },
    });

    const zipPath = path.join(process.cwd(), 'uploads', 'temp', `${client?.name}_${Date.now()}.zip`);
    
    // Ensure temp directory exists
    const tempDir = path.dirname(zipPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        resolve(zipPath);
      });

      archive.on('error', (err) => {
        reject(err);
      });

      archive.pipe(output);

      // Add images to zip
      images.forEach((image) => {
        if (fs.existsSync(image.path)) {
          archive.file(image.path, { name: image.originalName });
        }
      });

      archive.finalize();
    });
  }

// Get image statistics
export const getImageStats = async (studioId: string) => {
    const totalImages = await prisma.image.count({
      where: { studioId },
    });

    const recentImages = await prisma.image.count({
      where: {
        studioId,
        uploadedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
    });

    const totalSize = await prisma.image.aggregate({
      where: { studioId },
      _sum: { size: true },
    });

    const imagesByClient = await prisma.image.groupBy({
      by: ['clientId'],
      where: { studioId },
      _count: { id: true },
    });

    return {
      totalImages,
      recentImages,
      totalSize: totalSize._sum.size || 0,
      clientsWithImages: imagesByClient.length,
    };
};

// Get recent uploads for dashboard
export const getRecentUploads = async (studioId: string, limit: number = 10) => {
  return await prisma.image.findMany({
    where: { studioId },
    include: {
      client: {
        select: {
          name: true,
          uniqueId: true,
        },
      },
    },
    orderBy: { uploadedAt: 'desc' },
    take: limit,
  });
};
