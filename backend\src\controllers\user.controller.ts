import { Request, Response, NextFunction } from 'express';
import * as userService from '../services/user.service';
import { ApiError } from '../utils/apiError';
import {
  loginSchema,
  registerStudioSchema,
  updateStudioStatusSchema,
  updateStudioSchema,
  createStudioSchema,
} from '../validations/user.validation';

// Admin login
export const loginAdmin = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = loginSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      const { email, password } = req.body;
      const result = await userService.loginAdmin(email, password);

      res.status(200).json({
        success: true,
        message: 'Admin login successful',
        data: result,
      });
    } catch (error) {
      next(error);
    }
};

// Studio registration
export const registerStudio = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = registerStudioSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      const result = await userService.registerStudio(req.body);

      res.status(201).json({
        success: true,
        message: 'Studio registered successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
};

// Studio login
export const loginStudio = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = loginSchema.validate(req.body);
      if (error) {
        throw new ApiError(400, error.details[0].message);
      }

      const { email, password } = req.body;
      const result = await userService.loginStudio(email, password);

      res.status(200).json({
        success: true,
        message: 'Studio login successful',
        data: result,
      });
    } catch (error) {
      next(error);
    }
};

// Get all studios (Admin only)
export const getAllStudios = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const studios = await userService.getAllStudios();

      res.status(200).json({
        success: true,
        message: 'Studios retrieved successfully',
        data: studios,
      });
    } catch (error) {
      next(error);
    }
  }

// Update studio status (Admin only) - Functional
export const updateStudioStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { error } = updateStudioStatusSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const { studioId } = req.params;
    const { status } = req.body;

    const result = await userService.updateStudioStatus(studioId, status);

      res.status(200).json({
        success: true,
        message: `Studio ${status.toLowerCase()} successfully`,
        data: result,
      });
    } catch (error) {
      next(error);
    }
};

// Get all users (Admin only)
export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const users = await userService.getAllUsers();

      res.status(200).json({
        success: true,
        message: 'Users retrieved successfully',
        data: users,
      });
    } catch (error) {
      next(error);
    }
};

// Get studio users (Studio only)
export const getStudioUsers = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'User not authenticated');
      }

      const users = await userService.getStudioUsers(req.user.id);

      res.status(200).json({
        success: true,
        message: 'Studio users retrieved successfully',
        data: users,
      });
    } catch (error) {
      next(error);
    }
};

// Get studio by ID (Admin only)
export const getStudioById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { studioId } = req.params;
      const studio = await userService.getStudioById(studioId);

      res.status(200).json({
        success: true,
        message: 'Studio retrieved successfully',
        data: studio,
      });
    } catch (error) {
      next(error);
    }
  }

// Create studio (Admin only) - Functional
export const createStudio = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { error } = createStudioSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const studio = await userService.createStudio(req.body);

      res.status(201).json({
        success: true,
        message: 'Studio created successfully',
        data: studio,
      });
    } catch (error) {
      next(error);
    }
};

// Update studio (Admin only) - Functional
export const updateStudio = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { error } = updateStudioSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const { studioId } = req.params;
    const studio = await userService.updateStudio(studioId, req.body);

      res.status(200).json({
        success: true,
        message: 'Studio updated successfully',
        data: studio,
      });
    } catch (error) {
      next(error);
    }
};

// Delete studio (Admin only) - Functional
export const deleteStudio = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { studioId } = req.params;
      const result = await userService.deleteStudio(studioId);

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      next(error);
    }
};
