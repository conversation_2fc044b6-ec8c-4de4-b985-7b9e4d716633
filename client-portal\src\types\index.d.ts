export interface Client {
  id: string;
  uniqueId: string;
  name: string;
  email?: string;
  studioName: string;
}

export interface Image {
  id: string;
  filename: string;
  originalName: string;
  path: string;
  size: number;
  mimeType: string;
  description?: string;
  tags?: string;
  uploadedAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
}

export interface LoginRequest {
  uniqueId: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  client: Client;
}
