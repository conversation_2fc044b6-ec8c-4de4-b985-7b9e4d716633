'use client';

import { useEffect, useState } from 'react';
import { Users, Camera, Calendar, CheckCircle, Upload, HardDrive, TrendingUp, Plus } from 'lucide-react';
import { settingsService, clientService } from '@/lib/services';
import { authService } from '@/lib/auth';
import { DashboardStats, RecentActivity } from '@/types';
import toast from 'react-hot-toast';
import Link from 'next/link';

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activity, setActivity] = useState<RecentActivity | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const currentUser = authService.getCurrentUser();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsData, activityData] = await Promise.all([
        settingsService.getDashboardStats(),
        settingsService.getRecentActivity(),
      ]);
      setStats(statsData);
      setActivity(activityData);
    } catch (error) {
      toast.error('Failed to fetch dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Studio Dashboard</h1>
          <p className="text-gray-600">Welcome back, {currentUser?.name}!</p>
        </div>
        <Link href="/users" className="btn-primary flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Add Client</span>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Clients</p>
              <p className="text-xl font-bold text-gray-900">{stats?.totalClients || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Active Clients</p>
              <p className="text-xl font-bold text-gray-900">{stats?.activeClients || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Camera className="h-5 w-5 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Images</p>
              <p className="text-xl font-bold text-gray-900">{stats?.totalImages || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <TrendingUp className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Recent Clients</p>
              <p className="text-xl font-bold text-gray-900">{stats?.recentClients || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-indigo-100 p-3 rounded-full">
              <Upload className="h-5 w-5 text-indigo-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Recent Images</p>
              <p className="text-xl font-bold text-gray-900">{stats?.recentImages || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-full">
              <HardDrive className="h-5 w-5 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Storage Used</p>
              <p className="text-xl font-bold text-gray-900">{formatFileSize(stats?.storageUsed || 0)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Clients */}
        <div className="card">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Clients</h2>
          </div>
          <div className="p-6">
            {activity?.recentClients && activity.recentClients.length > 0 ? (
              <div className="space-y-4">
                {activity.recentClients.slice(0, 5).map((client) => (
                  <div key={client.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-primary-100 p-2 rounded-full">
                        <Users className="h-4 w-4 text-primary-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{client.name}</p>
                        <p className="text-xs text-gray-500">ID: {client.uniqueId}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {new Date(client.createdAt).toLocaleDateString()}
                      </p>
                      {client.lastLogin && (
                        <p className="text-xs text-green-600">
                          Last login: {new Date(client.lastLogin).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No clients yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Images */}
        <div className="card">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Uploads</h2>
          </div>
          <div className="p-6">
            {activity?.recentImages && activity.recentImages.length > 0 ? (
              <div className="space-y-4">
                {activity.recentImages.slice(0, 5).map((image) => (
                  <div key={image.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-purple-100 p-2 rounded-full">
                        <Camera className="h-4 w-4 text-purple-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{image.originalName}</p>
                        <p className="text-xs text-gray-500">
                          Client: {image.client?.name} ({image.client?.uniqueId})
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {new Date(image.uploadedAt).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatFileSize(image.size)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Camera className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No images uploaded yet</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
