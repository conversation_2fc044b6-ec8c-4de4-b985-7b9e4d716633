import sharp from 'sharp';
import path from 'path';
import fs from 'fs/promises';
import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';

export interface WatermarkOptions {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  opacity?: number; // 0-100
  scale?: number; // 0-1, relative to image size
  margin?: number; // pixels from edge
}

const DEFAULT_WATERMARK_OPTIONS: WatermarkOptions = {
  position: 'bottom-right',
  opacity: 70,
  scale: 0.15, // 15% of image width
  margin: 20
};

/**
 * Apply watermark to an image using studio's logo
 */
export const applyWatermark = async (
  imagePath: string,
  studioId: string,
  options: WatermarkOptions = {}
): Promise<string> => {
  try {
    // Get studio settings to find logo
    const studioSettings = await prisma.studioSettings.findUnique({
      where: { studioId },
    });

    if (!studioSettings?.logoUrl) {
      console.log(`No logo found for studio ${studioId}, skipping watermark`);
      return imagePath; // Return original path if no logo
    }

    // Extract logo filename from URL
    const logoFilename = studioSettings.logoUrl.split('/').pop();
    if (!logoFilename) {
      throw new ApiError(400, 'Invalid logo URL');
    }

    const logoPath = path.join(process.cwd(), 'uploads', 'images', logoFilename);
    
    // Check if logo file exists
    try {
      await fs.access(logoPath);
    } catch {
      console.log(`Logo file not found: ${logoPath}, skipping watermark`);
      return imagePath; // Return original path if logo file doesn't exist
    }

    // Merge options with defaults
    const config = { ...DEFAULT_WATERMARK_OPTIONS, ...options };

    // Get image metadata
    const imageMetadata = await sharp(imagePath).metadata();
    if (!imageMetadata.width || !imageMetadata.height) {
      throw new ApiError(400, 'Could not read image dimensions');
    }

    // Calculate watermark size
    const watermarkWidth = Math.round(imageMetadata.width * config.scale!);
    
    // Prepare watermark logo
    const watermarkBuffer = await sharp(logoPath)
      .resize(watermarkWidth, null, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .png() // Convert to PNG for transparency support
      .toBuffer();

    // Get watermark dimensions
    const watermarkMetadata = await sharp(watermarkBuffer).metadata();
    if (!watermarkMetadata.width || !watermarkMetadata.height) {
      throw new ApiError(400, 'Could not read watermark dimensions');
    }

    // Calculate position
    const position = calculateWatermarkPosition(
      imageMetadata.width,
      imageMetadata.height,
      watermarkMetadata.width,
      watermarkMetadata.height,
      config.position!,
      config.margin!
    );

    // Generate output filename
    const inputDir = path.dirname(imagePath);
    const inputName = path.basename(imagePath, path.extname(imagePath));
    const inputExt = path.extname(imagePath);
    const outputPath = path.join(inputDir, `${inputName}_watermarked${inputExt}`);

    // Apply watermark
    await sharp(imagePath)
      .composite([
        {
          input: await sharp(watermarkBuffer)
            .png({
              palette: true,
              compressionLevel: 9
            })
            .toBuffer(),
          top: position.top,
          left: position.left,
          blend: 'over'
        }
      ])
      .jpeg({
        quality: 90,
        progressive: true
      })
      .toFile(outputPath);

    console.log(`✨ Watermark applied: ${outputPath}`);
    return outputPath;

  } catch (error) {
    console.error('Error applying watermark:', error);
    // Return original path if watermarking fails
    return imagePath;
  }
};

/**
 * Calculate watermark position based on configuration
 */
const calculateWatermarkPosition = (
  imageWidth: number,
  imageHeight: number,
  watermarkWidth: number,
  watermarkHeight: number,
  position: string,
  margin: number
): { top: number; left: number } => {
  let top: number;
  let left: number;

  switch (position) {
    case 'top-left':
      top = margin;
      left = margin;
      break;
    case 'top-right':
      top = margin;
      left = imageWidth - watermarkWidth - margin;
      break;
    case 'bottom-left':
      top = imageHeight - watermarkHeight - margin;
      left = margin;
      break;
    case 'bottom-right':
      top = imageHeight - watermarkHeight - margin;
      left = imageWidth - watermarkWidth - margin;
      break;
    case 'center':
      top = Math.round((imageHeight - watermarkHeight) / 2);
      left = Math.round((imageWidth - watermarkWidth) / 2);
      break;
    default:
      // Default to bottom-right
      top = imageHeight - watermarkHeight - margin;
      left = imageWidth - watermarkWidth - margin;
  }

  // Ensure position is within bounds
  top = Math.max(0, Math.min(top, imageHeight - watermarkHeight));
  left = Math.max(0, Math.min(left, imageWidth - watermarkWidth));

  return { top, left };
};

/**
 * Apply watermark to multiple images
 */
export const applyWatermarkBatch = async (
  imagePaths: string[],
  studioId: string,
  options: WatermarkOptions = {}
): Promise<string[]> => {
  const watermarkedPaths: string[] = [];
  
  for (const imagePath of imagePaths) {
    try {
      const watermarkedPath = await applyWatermark(imagePath, studioId, options);
      watermarkedPaths.push(watermarkedPath);
    } catch (error) {
      console.error(`Failed to watermark ${imagePath}:`, error);
      watermarkedPaths.push(imagePath); // Use original if watermarking fails
    }
  }
  
  return watermarkedPaths;
};

/**
 * Check if studio has watermark enabled and logo available
 */
export const isWatermarkAvailable = async (studioId: string): Promise<boolean> => {
  try {
    const studioSettings = await prisma.studioSettings.findUnique({
      where: { studioId },
    });

    if (!studioSettings?.logoUrl) {
      return false;
    }

    // Extract logo filename and check if file exists
    const logoFilename = studioSettings.logoUrl.split('/').pop();
    if (!logoFilename) {
      return false;
    }

    const logoPath = path.join(process.cwd(), 'uploads', 'images', logoFilename);
    
    try {
      await fs.access(logoPath);
      return true;
    } catch {
      return false;
    }
  } catch {
    return false;
  }
};
