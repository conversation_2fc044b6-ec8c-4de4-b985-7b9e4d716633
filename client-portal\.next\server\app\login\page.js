/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RyUzQSU1Q215LXByb2plY3QlNUNwaG90by1jYXAtbmV3JTVDY2xpZW50LXBvcnRhbCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUclM0ElNUNteS1wcm9qZWN0JTVDcGhvdG8tY2FwLW5ldyU1Q2NsaWVudC1wb3J0YWwlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RyUzQSU1Q215LXByb2plY3QlNUNwaG90by1jYXAtbmV3JTVDY2xpZW50LXBvcnRhbCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNhcC1jbGllbnQtcG9ydGFsLz80MjIzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRzpcXFxcbXktcHJvamVjdFxcXFxwaG90by1jYXAtbmV3XFxcXGNsaWVudC1wb3J0YWxcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RyUzQSU1Q215LXByb2plY3QlNUNwaG90by1jYXAtbmV3JTVDY2xpZW50LXBvcnRhbCU1Q3NyYyU1Q2FwcCU1Q2xvZ2luJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY2FwLWNsaWVudC1wb3J0YWwvPzMxNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxteS1wcm9qZWN0XFxcXHBob3RvLWNhcC1uZXdcXFxcY2xpZW50LXBvcnRhbFxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Eye,EyeOff,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Eye,EyeOff,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Eye,EyeOff,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Eye,EyeOff,LogIn!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _lib_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services */ \"(ssr)/./src/lib/services.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LoginPage() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { register, handleSubmit, setValue, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if already authenticated\n        if (_lib_auth__WEBPACK_IMPORTED_MODULE_4__.authService.isAuthenticated()) {\n            router.push(\"/gallery\");\n            return;\n        }\n        // Pre-fill client ID from URL if available\n        const clientId = searchParams.get(\"id\");\n        if (clientId) {\n            setValue(\"uniqueId\", clientId);\n        }\n    }, [\n        router,\n        searchParams,\n        setValue\n    ]);\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        console.log(\"Login attempt:\", data);\n        try {\n            const response = await _lib_services__WEBPACK_IMPORTED_MODULE_3__.authService.login(data);\n            console.log(\"Login response:\", response);\n            _lib_auth__WEBPACK_IMPORTED_MODULE_4__.authService.setAuth(response.token, response.client);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(`Welcome, ${response.client.name}!`);\n            router.push(\"/gallery\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            console.error(\"Error response:\", error.response);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(error.response?.data?.message || \"Login failed\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-8 w-8 text-primary-600\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-bold text-gray-900\",\n                            children: \"Access Your Gallery\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Enter your credentials to view your photos\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit(onSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"uniqueId\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Client ID\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ...register(\"uniqueId\", {\n                                                required: \"Client ID is required\"\n                                            }),\n                                            type: \"text\",\n                                            className: \"input-field\",\n                                            placeholder: \"Enter your client ID\",\n                                            autoComplete: \"username\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.uniqueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.uniqueId.message\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"password\", {\n                                                        required: \"Password is required\"\n                                                    }),\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    className: \"input-field pr-10\",\n                                                    placeholder: \"Enter your password\",\n                                                    autoComplete: \"current-password\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600\",\n                                            children: errors.password.message\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading,\n                                className: \"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute left-0 inset-y-0 flex items-center pl-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary-500 group-hover:text-primary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    isLoading ? \"Signing in...\" : \"Sign in\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"Don't have your credentials? Contact your photographer.\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-800 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Tip:\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            \" If you have a QR code from your photographer, scan it with your phone camera to automatically fill in your details.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDaUI7QUFDbkI7QUFDZ0I7QUFDRjtBQUNmO0FBRUw7QUFFckIsU0FBU1k7SUFDdEIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdkLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2UsY0FBY0MsZ0JBQWdCLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNaUIsU0FBU2YsMERBQVNBO0lBQ3hCLE1BQU1nQixlQUFlZixnRUFBZUE7SUFFcEMsTUFBTSxFQUNKZ0IsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLFFBQVEsRUFDUkMsV0FBVyxFQUFFQyxNQUFNLEVBQUUsRUFDdEIsR0FBR25CLHdEQUFPQTtJQUVYSCxnREFBU0EsQ0FBQztRQUNSLGlDQUFpQztRQUNqQyxJQUFJUSxrREFBV0EsQ0FBQ2UsZUFBZSxJQUFJO1lBQ2pDUCxPQUFPUSxJQUFJLENBQUM7WUFDWjtRQUNGO1FBRUEsMkNBQTJDO1FBQzNDLE1BQU1DLFdBQVdSLGFBQWFTLEdBQUcsQ0FBQztRQUNsQyxJQUFJRCxVQUFVO1lBQ1pMLFNBQVMsWUFBWUs7UUFDdkI7SUFDRixHQUFHO1FBQUNUO1FBQVFDO1FBQWNHO0tBQVM7SUFFbkMsTUFBTU8sV0FBVyxPQUFPQztRQUN0QmYsYUFBYTtRQUNiZ0IsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQkY7UUFDOUIsSUFBSTtZQUNGLE1BQU1HLFdBQVcsTUFBTXRCLHNEQUFPQSxDQUFDdUIsS0FBSyxDQUFDSjtZQUNyQ0MsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQkM7WUFDL0J2QixrREFBV0EsQ0FBQ3lCLE9BQU8sQ0FBQ0YsU0FBU0csS0FBSyxFQUFFSCxTQUFTSSxNQUFNO1lBQ25EekIsdURBQUtBLENBQUMwQixPQUFPLENBQUMsQ0FBQyxTQUFTLEVBQUVMLFNBQVNJLE1BQU0sQ0FBQ0UsSUFBSSxDQUFDLENBQUMsQ0FBQztZQUNqRHJCLE9BQU9RLElBQUksQ0FBQztRQUNkLEVBQUUsT0FBT2MsT0FBWTtZQUNuQlQsUUFBUVMsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUJULFFBQVFTLEtBQUssQ0FBQyxtQkFBbUJBLE1BQU1QLFFBQVE7WUFDL0NyQix1REFBS0EsQ0FBQzRCLEtBQUssQ0FBQ0EsTUFBTVAsUUFBUSxFQUFFSCxNQUFNVyxXQUFXO1FBQy9DLFNBQVU7WUFDUjFCLGFBQWE7UUFDZjtJQUNGO0lBRUEscUJBQ0UsOERBQUMyQjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ25DLG1HQUFNQTtnQ0FBQ21DLFdBQVU7Ozs7Ozs7Ozs7O3NDQUVwQiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXdDOzs7Ozs7c0NBR3RELDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBNkI7Ozs7Ozs7Ozs7Ozs4QkFLNUMsOERBQUNHO29CQUFLSCxXQUFVO29CQUFpQmQsVUFBVVIsYUFBYVE7O3NDQUN0RCw4REFBQ2E7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNLOzRDQUFNQyxTQUFROzRDQUFXTCxXQUFVO3NEQUErQzs7Ozs7O3NEQUduRiw4REFBQ007NENBQ0UsR0FBRzdCLFNBQVMsWUFBWTtnREFDdkI4QixVQUFVOzRDQUNaLEVBQUU7NENBQ0ZDLE1BQUs7NENBQ0xSLFdBQVU7NENBQ1ZTLGFBQVk7NENBQ1pDLGNBQWE7Ozs7Ozt3Q0FFZDdCLE9BQU84QixRQUFRLGtCQUNkLDhEQUFDVDs0Q0FBRUYsV0FBVTtzREFBNkJuQixPQUFPOEIsUUFBUSxDQUFDYixPQUFPOzs7Ozs7Ozs7Ozs7OENBSXJFLDhEQUFDQzs7c0RBQ0MsOERBQUNLOzRDQUFNQyxTQUFROzRDQUFXTCxXQUFVO3NEQUErQzs7Ozs7O3NEQUduRiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTtvREFDRSxHQUFHN0IsU0FBUyxZQUFZO3dEQUN2QjhCLFVBQVU7b0RBQ1osRUFBRTtvREFDRkMsTUFBTW5DLGVBQWUsU0FBUztvREFDOUIyQixXQUFVO29EQUNWUyxhQUFZO29EQUNaQyxjQUFhOzs7Ozs7OERBRWYsOERBQUNFO29EQUNDSixNQUFLO29EQUNMSyxTQUFTLElBQU12QyxnQkFBZ0IsQ0FBQ0Q7b0RBQ2hDMkIsV0FBVTs4REFFVDNCLDZCQUNDLDhEQUFDVCxtR0FBTUE7d0RBQUNvQyxXQUFVOzs7Ozs2RUFFbEIsOERBQUNyQyxtR0FBR0E7d0RBQUNxQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FJcEJuQixPQUFPaUMsUUFBUSxrQkFDZCw4REFBQ1o7NENBQUVGLFdBQVU7c0RBQTZCbkIsT0FBT2lDLFFBQVEsQ0FBQ2hCLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLdkUsOERBQUNDO3NDQUNDLDRFQUFDYTtnQ0FDQ0osTUFBSztnQ0FDTE8sVUFBVTVDO2dDQUNWNkIsV0FBVTs7a0RBRVYsOERBQUNnQjt3Q0FBS2hCLFdBQVU7a0RBQ2QsNEVBQUNsQyxvR0FBS0E7NENBQUNrQyxXQUFVOzs7Ozs7Ozs7OztvQ0FFbEI3QixZQUFZLGtCQUFrQjs7Ozs7Ozs7Ozs7O3NDQUluQyw4REFBQzRCOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRTtnQ0FBRUYsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU96Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFFRixXQUFVOzswQ0FDWCw4REFBQ2lCOzBDQUFPOzs7Ozs7NEJBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY2FwLWNsaWVudC1wb3J0YWwvLi9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4P2ZjNjMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgeyB1c2VGb3JtIH0gZnJvbSAncmVhY3QtaG9vay1mb3JtJztcclxuaW1wb3J0IHsgRXllLCBFeWVPZmYsIENhbWVyYSwgTG9nSW4gfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5pbXBvcnQgeyBhdXRoU2VydmljZSBhcyBhdXRoQXBpIH0gZnJvbSAnQC9saWIvc2VydmljZXMnO1xyXG5pbXBvcnQgeyBhdXRoU2VydmljZSB9IGZyb20gJ0AvbGliL2F1dGgnO1xyXG5pbXBvcnQgeyBMb2dpblJlcXVlc3QgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHRvYXN0IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2dpblBhZ2UoKSB7XHJcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2hvd1Bhc3N3b3JkLCBzZXRTaG93UGFzc3dvcmRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xyXG5cclxuICBjb25zdCB7XHJcbiAgICByZWdpc3RlcixcclxuICAgIGhhbmRsZVN1Ym1pdCxcclxuICAgIHNldFZhbHVlLFxyXG4gICAgZm9ybVN0YXRlOiB7IGVycm9ycyB9LFxyXG4gIH0gPSB1c2VGb3JtPExvZ2luUmVxdWVzdD4oKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIENoZWNrIGlmIGFscmVhZHkgYXV0aGVudGljYXRlZFxyXG4gICAgaWYgKGF1dGhTZXJ2aWNlLmlzQXV0aGVudGljYXRlZCgpKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvZ2FsbGVyeScpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUHJlLWZpbGwgY2xpZW50IElEIGZyb20gVVJMIGlmIGF2YWlsYWJsZVxyXG4gICAgY29uc3QgY2xpZW50SWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpZCcpO1xyXG4gICAgaWYgKGNsaWVudElkKSB7XHJcbiAgICAgIHNldFZhbHVlKCd1bmlxdWVJZCcsIGNsaWVudElkKTtcclxuICAgIH1cclxuICB9LCBbcm91dGVyLCBzZWFyY2hQYXJhbXMsIHNldFZhbHVlXSk7XHJcblxyXG4gIGNvbnN0IG9uU3VibWl0ID0gYXN5bmMgKGRhdGE6IExvZ2luUmVxdWVzdCkgPT4ge1xyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgY29uc29sZS5sb2coJ0xvZ2luIGF0dGVtcHQ6JywgZGF0YSk7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhBcGkubG9naW4oZGF0YSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdMb2dpbiByZXNwb25zZTonLCByZXNwb25zZSk7XHJcbiAgICAgIGF1dGhTZXJ2aWNlLnNldEF1dGgocmVzcG9uc2UudG9rZW4sIHJlc3BvbnNlLmNsaWVudCk7XHJcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoYFdlbGNvbWUsICR7cmVzcG9uc2UuY2xpZW50Lm5hbWV9IWApO1xyXG4gICAgICByb3V0ZXIucHVzaCgnL2dhbGxlcnknKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignTG9naW4gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXNwb25zZTonLCBlcnJvci5yZXNwb25zZSk7XHJcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdMb2dpbiBmYWlsZWQnKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTEyIHB4LTQgc206cHgtNiBsZzpweC04XCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWQgdy1mdWxsIHNwYWNlLXktOFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBoLTE2IHctMTYgYmctcHJpbWFyeS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxDYW1lcmEgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LXByaW1hcnktNjAwXCIgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cIm10LTYgdGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cclxuICAgICAgICAgICAgQWNjZXNzIFlvdXIgR2FsbGVyeVxyXG4gICAgICAgICAgPC9oMj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgIEVudGVyIHlvdXIgY3JlZGVudGlhbHMgdG8gdmlldyB5b3VyIHBob3Rvc1xyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8Zm9ybSBjbGFzc05hbWU9XCJtdC04IHNwYWNlLXktNlwiIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJ1bmlxdWVJZFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBDbGllbnQgSURcclxuICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCd1bmlxdWVJZCcsIHtcclxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6ICdDbGllbnQgSUQgaXMgcmVxdWlyZWQnLFxyXG4gICAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgY2xpZW50IElEXCJcclxuICAgICAgICAgICAgICAgIGF1dG9Db21wbGV0ZT1cInVzZXJuYW1lXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIHtlcnJvcnMudW5pcXVlSWQgJiYgKFxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMudW5pcXVlSWQubWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwicGFzc3dvcmRcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgUGFzc3dvcmRcclxuICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ3Bhc3N3b3JkJywge1xyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiAnUGFzc3dvcmQgaXMgcmVxdWlyZWQnLFxyXG4gICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd1Bhc3N3b3JkID8gJ3RleHQnIDogJ3Bhc3N3b3JkJ31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGQgcHItMTBcIlxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgcGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJjdXJyZW50LXBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UGFzc3dvcmQoIXNob3dQYXNzd29yZCl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCByaWdodC0wIHByLTMgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICB7c2hvd1Bhc3N3b3JkID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxFeWVPZmYgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyYXktNDAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICB7ZXJyb3JzLnBhc3N3b3JkICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnBhc3N3b3JkLm1lc3NhZ2V9PC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgdy1mdWxsIGZsZXgganVzdGlmeS1jZW50ZXIgcHktMyBweC00IGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLWxnIHRleHQtd2hpdGUgYmctcHJpbWFyeS02MDAgaG92ZXI6YmctcHJpbWFyeS03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIGluc2V0LXktMCBmbGV4IGl0ZW1zLWNlbnRlciBwbC0zXCI+XHJcbiAgICAgICAgICAgICAgICA8TG9nSW4gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXByaW1hcnktNTAwIGdyb3VwLWhvdmVyOnRleHQtcHJpbWFyeS00MDBcIiAvPlxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gJ1NpZ25pbmcgaW4uLi4nIDogJ1NpZ24gaW4nfVxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgRG9uJ3QgaGF2ZSB5b3VyIGNyZWRlbnRpYWxzPyBDb250YWN0IHlvdXIgcGhvdG9ncmFwaGVyLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Zvcm0+XHJcblxyXG4gICAgICAgIHsvKiBRUiBDb2RlIFNjYW5uZXIgSW5mbyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS04MDAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPHN0cm9uZz5UaXA6PC9zdHJvbmc+IElmIHlvdSBoYXZlIGEgUVIgY29kZSBmcm9tIHlvdXIgcGhvdG9ncmFwaGVyLFxyXG4gICAgICAgICAgICBzY2FuIGl0IHdpdGggeW91ciBwaG9uZSBjYW1lcmEgdG8gYXV0b21hdGljYWxseSBmaWxsIGluIHlvdXIgZGV0YWlscy5cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsInVzZUZvcm0iLCJFeWUiLCJFeWVPZmYiLCJDYW1lcmEiLCJMb2dJbiIsImF1dGhTZXJ2aWNlIiwiYXV0aEFwaSIsInRvYXN0IiwiTG9naW5QYWdlIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwic2hvd1Bhc3N3b3JkIiwic2V0U2hvd1Bhc3N3b3JkIiwicm91dGVyIiwic2VhcmNoUGFyYW1zIiwicmVnaXN0ZXIiLCJoYW5kbGVTdWJtaXQiLCJzZXRWYWx1ZSIsImZvcm1TdGF0ZSIsImVycm9ycyIsImlzQXV0aGVudGljYXRlZCIsInB1c2giLCJjbGllbnRJZCIsImdldCIsIm9uU3VibWl0IiwiZGF0YSIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImxvZ2luIiwic2V0QXV0aCIsInRva2VuIiwiY2xpZW50Iiwic3VjY2VzcyIsIm5hbWUiLCJlcnJvciIsIm1lc3NhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJmb3JtIiwibGFiZWwiLCJodG1sRm9yIiwiaW5wdXQiLCJyZXF1aXJlZCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImF1dG9Db21wbGV0ZSIsInVuaXF1ZUlkIiwiYnV0dG9uIiwib25DbGljayIsInBhc3N3b3JkIiwiZGlzYWJsZWQiLCJzcGFuIiwic3Ryb25nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"http://localhost:5000/api\" || 0,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"client_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle auth errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401) {\n        // Clear token and redirect to login\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"client_token\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"client_user\");\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nconst authService = {\n    setAuth: (token, client)=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"client_token\", token, {\n            expires: 7\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"client_user\", JSON.stringify(client), {\n            expires: 7\n        });\n    },\n    getToken: ()=>{\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"client_token\") || null;\n    },\n    getCurrentClient: ()=>{\n        const clientData = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"client_user\");\n        if (clientData) {\n            try {\n                return JSON.parse(clientData);\n            } catch  {\n                return null;\n            }\n        }\n        return null;\n    },\n    isAuthenticated: ()=>{\n        return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"client_token\");\n    },\n    logout: ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"client_token\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"client_user\");\n        if (false) {}\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/services.ts":
/*!*****************************!*\
  !*** ./src/lib/services.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   imageService: () => (/* binding */ imageService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n\nconst authService = {\n    async login (data) {\n        console.log(\"API call to:\", \"/clients/login\", \"with data:\", data);\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/clients/login\", data);\n        console.log(\"API response:\", response.data);\n        return response.data.data;\n    }\n};\nconst imageService = {\n    async getMyImages () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/client-portal/my-images\");\n        return response.data.data || [];\n    },\n    async downloadAllImages () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/client-portal/download-all\", {\n            responseType: \"blob\"\n        });\n        // Create download link\n        const url = window.URL.createObjectURL(new Blob([\n            response.data\n        ]));\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.setAttribute(\"download\", \"my-images.zip\");\n        document.body.appendChild(link);\n        link.click();\n        link.remove();\n        window.URL.revokeObjectURL(url);\n    },\n    getImageUrl (imagePath) {\n        // Simple and clean: always use relative path from backend\n        return `http://localhost:5000/${imagePath}`;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/services.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c2b41e91aa7f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY2FwLWNsaWVudC1wb3J0YWwvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2JiNTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjMmI0MWU5MWFhN2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\nconst metadata = {\n    title: \"Photo Cap - Client Portal\",\n    description: \"Access your photo gallery\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\my-project\\\\photo-cap-new\\\\client-portal\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ21CO0FBSWxDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlOzs4QkFDOUIsOERBQUNXO29CQUFJRCxXQUFVOzhCQUNaSjs7Ozs7OzhCQUVILDhEQUFDTCxvREFBT0E7b0JBQUNXLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY2FwLWNsaWVudC1wb3J0YWwvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcclxuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xyXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXHJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiAnUGhvdG8gQ2FwIC0gQ2xpZW50IFBvcnRhbCcsXHJcbiAgZGVzY3JpcHRpb246ICdBY2Nlc3MgeW91ciBwaG90byBnYWxsZXJ5JyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XHJcbiAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPFRvYXN0ZXIgcG9zaXRpb249XCJ0b3AtcmlnaHRcIiAvPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJpbnRlciIsIlRvYXN0ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiLCJwb3NpdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`G:\my-project\photo-cap-new\client-portal\src\app\login\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/js-cookie","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/lucide-react","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/react-hook-form","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cmy-project%5Cphoto-cap-new%5Cclient-portal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();