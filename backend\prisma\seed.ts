import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import config from '../src/config/env';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default admin user
  const hashedPassword = await bcrypt.hash(config.admin.password, 12);
  
  const admin = await prisma.admin.upsert({
    where: { email: config.admin.email },
    update: {},
    create: {
      email: config.admin.email,
      password: hashedPassword,
      name: 'System Administrator',
    },
  });

  console.log('✅ Admin user created:', admin.email);

  // Create sample studio (for testing)
  const studioPassword = await bcrypt.hash('studio123', 12);
  
  const studio = await prisma.studio.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: studioPassword,
      name: 'Demo Photography Studio',
      description: 'A demo studio for testing purposes',
      phone: '+1234567890',
      address: '123 Demo Street, Demo City',
      status: 'APPROVED',
    },
  });

  console.log('✅ Demo studio created:', studio.email);

  // Create sample user
  const userPassword = await bcrypt.hash('user123', 12);
  
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: userPassword,
      name: 'Demo User',
      phone: '+1234567891',
      role: 'USER',
      studioId: studio.id,
    },
  });

  console.log('✅ Demo user created:', user.email);

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
