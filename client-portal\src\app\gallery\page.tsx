'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Camera, 
  Download, 
  Eye, 
  X, 
  LogOut, 
  User, 
  Calendar,
  Image as ImageIcon,
  Grid,
  List
} from 'lucide-react';
import { imageService } from '@/lib/services';
import { authService } from '@/lib/auth';
import { Image } from '@/types';
import toast from 'react-hot-toast';

export default function GalleryPage() {
  const [images, setImages] = useState<Image[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<Image | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isDownloading, setIsDownloading] = useState(false);
  const router = useRouter();
  const client = authService.getCurrentClient();

  useEffect(() => {
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }
    fetchImages();
  }, [router]);

  const fetchImages = async () => {
    try {
      const data = await imageService.getMyImages();
      setImages(data);
    } catch (error) {
      toast.error('Failed to fetch images');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadAll = async () => {
    if (images.length === 0) {
      toast.error('No images to download');
      return;
    }

    setIsDownloading(true);
    try {
      await imageService.downloadAllImages();
      toast.success('Download started');
    } catch (error) {
      toast.error('Failed to download images');
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadSingleImage = (image: Image) => {
    const link = document.createElement('a');
    link.href = imageService.getImageUrl(image.path);
    link.download = image.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your gallery...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Camera className="h-8 w-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Your Photo Gallery</h1>
                  <p className="text-sm text-gray-600">{client?.studioName}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center text-sm text-gray-600">
                  <User className="h-4 w-4 mr-1" />
                  {client?.name}
                </div>
                <button
                  onClick={authService.logout}
                  className="flex items-center text-sm text-gray-600 hover:text-gray-900"
                >
                  <LogOut className="h-4 w-4 mr-1" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Gallery Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                Your Photos ({images.length})
              </h2>
              <p className="text-gray-600 mt-1">
                Click on any image to view in full size
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex items-center bg-white border border-gray-300 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${
                    viewMode === 'grid'
                      ? 'bg-primary-100 text-primary-600'
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${
                    viewMode === 'list'
                      ? 'bg-primary-100 text-primary-600'
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>

              {/* Download All Button */}
              {images.length > 0 && (
                <button
                  onClick={handleDownloadAll}
                  disabled={isDownloading}
                  className="btn-primary flex items-center space-x-2 disabled:opacity-50"
                >
                  <Download className="h-4 w-4" />
                  <span>{isDownloading ? 'Preparing...' : 'Download All'}</span>
                </button>
              )}
            </div>
          </div>

          {/* Gallery */}
          {images.length === 0 ? (
            <div className="text-center py-16">
              <ImageIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No photos yet</h3>
              <p className="text-gray-600">
                Your photographer hasn't uploaded any photos yet. Check back later!
              </p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {images.map((image) => (
                <div key={image.id} className="group relative bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                  <div className="aspect-square relative">
                    <img
                      src={imageService.getImageUrl(image.path)}
                      alt={image.originalName}
                      className="w-full h-full object-cover cursor-pointer"
                      onClick={() => setSelectedImage(image)}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                        <button
                          onClick={() => setSelectedImage(image)}
                          className="bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100"
                        >
                          <Eye className="h-4 w-4 text-gray-700" />
                        </button>
                        <button
                          onClick={() => downloadSingleImage(image)}
                          className="bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100"
                        >
                          <Download className="h-4 w-4 text-gray-700" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="p-3">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {image.originalName}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatFileSize(image.size)} • {formatDate(image.uploadedAt)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="divide-y divide-gray-200">
                {images.map((image) => (
                  <div key={image.id} className="p-4 hover:bg-gray-50 flex items-center space-x-4">
                    <img
                      src={imageService.getImageUrl(image.path)}
                      alt={image.originalName}
                      className="w-16 h-16 object-cover rounded cursor-pointer"
                      onClick={() => setSelectedImage(image)}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {image.originalName}
                      </p>
                      <div className="flex items-center text-xs text-gray-500 mt-1 space-x-4">
                        <span>{formatFileSize(image.size)}</span>
                        <span className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(image.uploadedAt)}
                        </span>
                      </div>
                      {image.description && (
                        <p className="text-xs text-gray-600 mt-1">{image.description}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedImage(image)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => downloadSingleImage(image)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </main>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            <img
              src={imageService.getImageUrl(selectedImage.path)}
              alt={selectedImage.originalName}
              className="w-full h-full object-contain"
            />
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
            >
              <X className="h-6 w-6" />
            </button>
            <button
              onClick={() => downloadSingleImage(selectedImage)}
              className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
            >
              <Download className="h-6 w-6" />
            </button>
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-4 rounded">
              <h3 className="font-medium">{selectedImage.originalName}</h3>
              <div className="flex items-center text-sm text-gray-300 mt-1 space-x-4">
                <span>{formatFileSize(selectedImage.size)}</span>
                <span>{formatDate(selectedImage.uploadedAt)}</span>
              </div>
              {selectedImage.description && (
                <p className="text-sm text-gray-300 mt-2">{selectedImage.description}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
