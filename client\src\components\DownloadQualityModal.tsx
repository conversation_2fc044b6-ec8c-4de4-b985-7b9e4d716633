import React, { useState } from 'react';
import { X, Download, Info, Image as ImageIcon, Zap } from 'lucide-react';

interface DownloadQualityModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDownload: (quality: 'original' | 'optimized') => void;
  imageCount?: number;
  estimatedSizes?: {
    original: string;
    optimized: string;
  };
}

export default function DownloadQualityModal({
  isOpen,
  onClose,
  onDownload,
  imageCount = 1,
  estimatedSizes
}: DownloadQualityModalProps) {
  const [selectedQuality, setSelectedQuality] = useState<'original' | 'optimized'>('original');

  if (!isOpen) return null;

  const handleDownload = () => {
    onDownload(selectedQuality);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Choose Download Quality
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <div className="text-sm text-gray-600 mb-4">
            {imageCount === 1 
              ? 'Select the quality for your image download:'
              : `Select the quality for downloading ${imageCount} images:`
            }
          </div>

          {/* Original Quality Option */}
          <div 
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              selectedQuality === 'original' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedQuality('original')}
          >
            <div className="flex items-start space-x-3">
              <div className={`w-4 h-4 rounded-full border-2 mt-0.5 ${
                selectedQuality === 'original' 
                  ? 'border-blue-500 bg-blue-500' 
                  : 'border-gray-300'
              }`}>
                {selectedQuality === 'original' && (
                  <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <ImageIcon className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-gray-900">Original Quality</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Full resolution, uncompressed images as originally uploaded
                </p>
                {estimatedSizes?.original && (
                  <p className="text-xs text-gray-500 mt-1">
                    Estimated size: {estimatedSizes.original}
                  </p>
                )}
                <div className="flex items-center space-x-1 mt-2">
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    Best Quality
                  </span>
                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                    Larger Size
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Optimized Quality Option */}
          <div 
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              selectedQuality === 'optimized' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedQuality('optimized')}
          >
            <div className="flex items-start space-x-3">
              <div className={`w-4 h-4 rounded-full border-2 mt-0.5 ${
                selectedQuality === 'optimized' 
                  ? 'border-blue-500 bg-blue-500' 
                  : 'border-gray-300'
              }`}>
                {selectedQuality === 'optimized' && (
                  <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-gray-900">Optimized Quality</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  High-quality compressed images, perfect for web and sharing
                </p>
                {estimatedSizes?.optimized && (
                  <p className="text-xs text-gray-500 mt-1">
                    Estimated size: {estimatedSizes.optimized}
                  </p>
                )}
                <div className="flex items-center space-x-1 mt-2">
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    Fast Download
                  </span>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    Smaller Size
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Info Box */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-800">
                <p className="font-medium mb-1">Quality Comparison:</p>
                <ul className="space-y-1">
                  <li>• <strong>Original:</strong> Exact files as uploaded, best for printing</li>
                  <li>• <strong>Optimized:</strong> 80-90% smaller, perfect for digital use</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleDownload}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>
              Download {selectedQuality === 'original' ? 'Original' : 'Optimized'}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
