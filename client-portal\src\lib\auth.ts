import Cookies from 'js-cookie';
import { Client } from '@/types';

export const authService = {
  setAuth: (token: string, client: Client) => {
    Cookies.set('client_token', token, { expires: 7 });
    Cookies.set('client_user', JSON.stringify(client), { expires: 7 });
  },

  getToken: (): string | null => {
    return Cookies.get('client_token') || null;
  },

  getCurrentClient: (): Client | null => {
    const clientData = Cookies.get('client_user');
    if (clientData) {
      try {
        return JSON.parse(clientData);
      } catch {
        return null;
      }
    }
    return null;
  },

  isAuthenticated: (): boolean => {
    return !!Cookies.get('client_token');
  },

  logout: () => {
    Cookies.remove('client_token');
    Cookies.remove('client_user');
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  },
};
