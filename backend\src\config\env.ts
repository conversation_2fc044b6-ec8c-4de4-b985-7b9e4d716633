import dotenv from 'dotenv';

dotenv.config();

export const config = {
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  database: {
    url: process.env.DATABASE_URL || '',
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback-secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
  
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || [
      'http://localhost:3000',  // Studio Panel
      'http://localhost:3001',  // Studio Panel (alternative)
      'http://localhost:3002',  // Client Portal
    ],
  },
  
  admin: {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    password: process.env.ADMIN_PASSWORD || 'admin123',
  },
};

export default config;
