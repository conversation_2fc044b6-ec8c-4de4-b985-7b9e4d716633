export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: 'ADMIN' | 'STUDIO' | 'USER';
  createdAt: string;
}

export interface Studio {
  id: string;
  email: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterStudioRequest {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface Client {
  id: string;
  uniqueId: string;
  name: string;
  email?: string;
  phone?: string;
  qrCode?: string;
  accessLink: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    images: number;
  };
}

export interface CreateClientRequest {
  name: string;
  email?: string;
  phone?: string;
}

export interface UpdateClientRequest {
  name?: string;
  email?: string;
  phone?: string;
  isActive?: boolean;
}

export interface Image {
  id: string;
  filename: string;
  originalName: string;
  path: string;
  size: number;
  mimeType: string;
  description?: string;
  tags?: string;
  uploadedAt: string;
  client?: {
    name: string;
    uniqueId: string;
  };
}

export interface DashboardStats {
  totalClients: number;
  activeClients: number;
  totalImages: number;
  recentClients: number;
  recentImages: number;
  storageUsed: number;
}

export interface RecentActivity {
  recentClients: Client[];
  recentImages: Image[];
}

export interface StudioSettings {
  id: string;
  studioId: string;
  profilePicture?: string;
  businessAddress?: string;
  operatingHours?: string;
  socialLinks?: any;
  logoUrl?: string;
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  defaultAccessDuration: number;
  autoGeneratePassword: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  maxFileSize: number;
  allowedFormats: string;
  autoResize: boolean;
  storageQuota: number;
  enableWatermark: boolean;
  watermarkPosition: string;
  watermarkOpacity: number;
  watermarkScale: number;
  watermarkMargin: number;
}

export interface StudioProfile {
  id: string;
  email: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
  settings: StudioSettings;
}
