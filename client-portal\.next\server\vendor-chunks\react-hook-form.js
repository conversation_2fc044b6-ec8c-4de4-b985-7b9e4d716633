"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar isUndefined = (val)=>val === undefined;\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [\n        path\n    ] : stringToPath(path)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = \"HookFormContext\";\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2, _internal_visited) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, compute } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const _compute = react__WEBPACK_IMPORTED_MODULE_0__.useRef(compute);\n    const _computeFormValues = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    _compute.current = compute;\n    const defaultValueMemo = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>control._getWatch(name, _defaultValue.current), [\n        control,\n        name\n    ]);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(_compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo);\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>{\n                if (!disabled) {\n                    const formValues = generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current);\n                    if (_compute.current) {\n                        const computedFormValues = _compute.current(formValues);\n                        if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                            updateValue(computedFormValues);\n                            _computeFormValues.current = computedFormValues;\n                        }\n                    } else {\n                        updateValue(formValues);\n                    }\n                }\n            }\n        }), [\n        control,\n        disabled,\n        name,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister, defaultValue } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const defaultValueMemo = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>get(control._formValues, name, get(control._defaultValues, name, defaultValue)), [\n        control,\n        name,\n        defaultValue\n    ]);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: defaultValueMemo,\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    _props.current = props;\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus && elm.focus(),\n                select: ()=>elm.select && elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType && encType !== \"multipart/form-data\" ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = name + \".\" + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState,\n            name\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>\"values\" in payload && name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState,\n                        defaultValues: _defaultValues\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = cloneObject(values);\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    unset(fieldValues, name);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount){\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                } else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    if (typeof crypto !== \"undefined\" && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>rules && control.register(name, rules), [\n        control,\n        rules,\n        name\n    ]);\n    useIsomorphicLayoutEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === name || !fieldArrayName) {\n                    const fieldValues = get(values, name);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control,\n        name\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        } else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [\n        control,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [\n        control,\n        props.errors\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions\n            });\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaG9vay1mb3JtL2Rpc3QvaW5kZXguZXNtLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUUxQixJQUFJQyxrQkFBa0IsQ0FBQ0MsVUFBWUEsUUFBUUMsSUFBSSxLQUFLO0FBRXBELElBQUlDLGVBQWUsQ0FBQ0MsU0FBVUEsa0JBQWlCQztBQUUvQyxJQUFJQyxvQkFBb0IsQ0FBQ0YsU0FBVUEsVUFBUztBQUU1QyxNQUFNRyxlQUFlLENBQUNILFNBQVUsT0FBT0EsV0FBVTtBQUNqRCxJQUFJSSxXQUFXLENBQUNKLFNBQVUsQ0FBQ0Usa0JBQWtCRixXQUN6QyxDQUFDSyxNQUFNQyxPQUFPLENBQUNOLFdBQ2ZHLGFBQWFILFdBQ2IsQ0FBQ0QsYUFBYUM7QUFFbEIsSUFBSU8sZ0JBQWdCLENBQUNDLFFBQVVKLFNBQVNJLFVBQVVBLE1BQU1DLE1BQU0sR0FDeERiLGdCQUFnQlksTUFBTUMsTUFBTSxJQUN4QkQsTUFBTUMsTUFBTSxDQUFDQyxPQUFPLEdBQ3BCRixNQUFNQyxNQUFNLENBQUNULEtBQUssR0FDdEJRO0FBRU4sSUFBSUcsb0JBQW9CLENBQUNDLE9BQVNBLEtBQUtDLFNBQVMsQ0FBQyxHQUFHRCxLQUFLRSxNQUFNLENBQUMsbUJBQW1CRjtBQUVuRixJQUFJRyxxQkFBcUIsQ0FBQ0MsT0FBT0osT0FBU0ksTUFBTUMsR0FBRyxDQUFDTixrQkFBa0JDO0FBRXRFLElBQUlNLGdCQUFnQixDQUFDQztJQUNqQixNQUFNQyxnQkFBZ0JELFdBQVdFLFdBQVcsSUFBSUYsV0FBV0UsV0FBVyxDQUFDQyxTQUFTO0lBQ2hGLE9BQVFsQixTQUFTZ0Isa0JBQWtCQSxjQUFjRyxjQUFjLENBQUM7QUFDcEU7QUFFQSxJQUFJQyxRQUFRLE1BQ3NCLElBQzlCLENBQW9CO0FBRXhCLFNBQVNJLFlBQVlDLElBQUk7SUFDckIsSUFBSUM7SUFDSixNQUFNeEIsVUFBVUQsTUFBTUMsT0FBTyxDQUFDdUI7SUFDOUIsTUFBTUUscUJBQXFCLE9BQU9DLGFBQWEsY0FBY0gsZ0JBQWdCRyxXQUFXO0lBQ3hGLElBQUlILGdCQUFnQjVCLE1BQU07UUFDdEI2QixPQUFPLElBQUk3QixLQUFLNEI7SUFDcEIsT0FDSyxJQUFJLENBQUVMLENBQUFBLFNBQVVLLENBQUFBLGdCQUFnQkksUUFBUUYsa0JBQWlCLENBQUMsS0FDMUR6QixDQUFBQSxXQUFXRixTQUFTeUIsS0FBSSxHQUFJO1FBQzdCQyxPQUFPeEIsVUFBVSxFQUFFLEdBQUcsQ0FBQztRQUN2QixJQUFJLENBQUNBLFdBQVcsQ0FBQ1ksY0FBY1csT0FBTztZQUNsQ0MsT0FBT0Q7UUFDWCxPQUNLO1lBQ0QsSUFBSyxNQUFNSyxPQUFPTCxLQUFNO2dCQUNwQixJQUFJQSxLQUFLTixjQUFjLENBQUNXLE1BQU07b0JBQzFCSixJQUFJLENBQUNJLElBQUksR0FBR04sWUFBWUMsSUFBSSxDQUFDSyxJQUFJO2dCQUNyQztZQUNKO1FBQ0o7SUFDSixPQUNLO1FBQ0QsT0FBT0w7SUFDWDtJQUNBLE9BQU9DO0FBQ1g7QUFFQSxJQUFJSyxRQUFRLENBQUNuQyxTQUFVLFFBQVFvQyxJQUFJLENBQUNwQztBQUVwQyxJQUFJcUMsY0FBYyxDQUFDQyxNQUFRQSxRQUFRQztBQUVuQyxJQUFJQyxVQUFVLENBQUN4QyxTQUFVSyxNQUFNQyxPQUFPLENBQUNOLFVBQVNBLE9BQU15QyxNQUFNLENBQUNDLFdBQVcsRUFBRTtBQUUxRSxJQUFJQyxlQUFlLENBQUNDLFFBQVVKLFFBQVFJLE1BQU1DLE9BQU8sQ0FBQyxhQUFhLElBQUlDLEtBQUssQ0FBQztBQUUzRSxJQUFJQyxNQUFNLENBQUNDLFFBQVFDLE1BQU1DO0lBQ3JCLElBQUksQ0FBQ0QsUUFBUSxDQUFDN0MsU0FBUzRDLFNBQVM7UUFDNUIsT0FBT0U7SUFDWDtJQUNBLE1BQU1DLFNBQVMsQ0FBQ2hCLE1BQU1jLFFBQVE7UUFBQ0E7S0FBSyxHQUFHTixhQUFhTSxLQUFJLEVBQUdHLE1BQU0sQ0FBQyxDQUFDRCxRQUFRakIsTUFBUWhDLGtCQUFrQmlELFVBQVVBLFNBQVNBLE1BQU0sQ0FBQ2pCLElBQUksRUFBRWM7SUFDckksT0FBT1gsWUFBWWMsV0FBV0EsV0FBV0gsU0FDbkNYLFlBQVlXLE1BQU0sQ0FBQ0MsS0FBSyxJQUNwQkMsZUFDQUYsTUFBTSxDQUFDQyxLQUFLLEdBQ2hCRTtBQUNWO0FBRUEsSUFBSUUsWUFBWSxDQUFDckQsU0FBVSxPQUFPQSxXQUFVO0FBRTVDLElBQUlzRCxNQUFNLENBQUNOLFFBQVFDLE1BQU1qRDtJQUNyQixJQUFJdUQsUUFBUSxDQUFDO0lBQ2IsTUFBTUMsV0FBV3JCLE1BQU1jLFFBQVE7UUFBQ0E7S0FBSyxHQUFHTixhQUFhTTtJQUNyRCxNQUFNUSxTQUFTRCxTQUFTQyxNQUFNO0lBQzlCLE1BQU1DLFlBQVlELFNBQVM7SUFDM0IsTUFBTyxFQUFFRixRQUFRRSxPQUFRO1FBQ3JCLE1BQU12QixNQUFNc0IsUUFBUSxDQUFDRCxNQUFNO1FBQzNCLElBQUlJLFdBQVczRDtRQUNmLElBQUl1RCxVQUFVRyxXQUFXO1lBQ3JCLE1BQU1FLFdBQVdaLE1BQU0sQ0FBQ2QsSUFBSTtZQUM1QnlCLFdBQ0l2RCxTQUFTd0QsYUFBYXZELE1BQU1DLE9BQU8sQ0FBQ3NELFlBQzlCQSxXQUNBLENBQUNDLE1BQU0sQ0FBQ0wsUUFBUSxDQUFDRCxRQUFRLEVBQUUsSUFDdkIsRUFBRSxHQUNGLENBQUM7UUFDbkI7UUFDQSxJQUFJckIsUUFBUSxlQUFlQSxRQUFRLGlCQUFpQkEsUUFBUSxhQUFhO1lBQ3JFO1FBQ0o7UUFDQWMsTUFBTSxDQUFDZCxJQUFJLEdBQUd5QjtRQUNkWCxTQUFTQSxNQUFNLENBQUNkLElBQUk7SUFDeEI7QUFDSjtBQUVBLE1BQU00QixTQUFTO0lBQ1hDLE1BQU07SUFDTkMsV0FBVztJQUNYQyxRQUFRO0FBQ1o7QUFDQSxNQUFNQyxrQkFBa0I7SUFDcEJDLFFBQVE7SUFDUkMsVUFBVTtJQUNWQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsS0FBSztBQUNUO0FBQ0EsTUFBTUMseUJBQXlCO0lBQzNCQyxLQUFLO0lBQ0xDLEtBQUs7SUFDTEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLFNBQVM7SUFDVEMsVUFBVTtJQUNWQyxVQUFVO0FBQ2Q7QUFFQSxNQUFNQyxnQ0FBa0JyRixnREFBbUIsQ0FBQztBQUM1Q3FGLGdCQUFnQkUsV0FBVyxHQUFHO0FBQzlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCQyxHQUNELE1BQU1DLGlCQUFpQixJQUFNeEYsNkNBQWdCLENBQUNxRjtBQUM5Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0E2QkMsR0FDRCxNQUFNSyxlQUFlLENBQUNDO0lBQ2xCLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUcxRCxNQUFNLEdBQUd5RDtJQUM5QixxQkFBUTNGLGdEQUFtQixDQUFDcUYsZ0JBQWdCUyxRQUFRLEVBQUU7UUFBRXpGLE9BQU82QjtJQUFLLEdBQUcwRDtBQUMzRTtBQUVBLElBQUlHLG9CQUFvQixDQUFDQyxXQUFXQyxTQUFTQyxxQkFBcUJDLFNBQVMsSUFBSTtJQUMzRSxNQUFNM0MsU0FBUztRQUNYNEMsZUFBZUgsUUFBUUksY0FBYztJQUN6QztJQUNBLElBQUssTUFBTTlELE9BQU95RCxVQUFXO1FBQ3pCTSxPQUFPQyxjQUFjLENBQUMvQyxRQUFRakIsS0FBSztZQUMvQmEsS0FBSztnQkFDRCxNQUFNb0QsT0FBT2pFO2dCQUNiLElBQUkwRCxRQUFRUSxlQUFlLENBQUNELEtBQUssS0FBS2pDLGdCQUFnQkssR0FBRyxFQUFFO29CQUN2RHFCLFFBQVFRLGVBQWUsQ0FBQ0QsS0FBSyxHQUFHLENBQUNMLFVBQVU1QixnQkFBZ0JLLEdBQUc7Z0JBQ2xFO2dCQUNBc0IsdUJBQXdCQSxDQUFBQSxtQkFBbUIsQ0FBQ00sS0FBSyxHQUFHLElBQUc7Z0JBQ3ZELE9BQU9SLFNBQVMsQ0FBQ1EsS0FBSztZQUMxQjtRQUNKO0lBQ0o7SUFDQSxPQUFPaEQ7QUFDWDtBQUVBLE1BQU1rRCw0QkFBNEIsTUFBa0IsR0FBYzFHLENBQXFCLEdBQUdBLDRDQUFlO0FBRXpHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCQyxHQUNELFNBQVM2RyxhQUFhbEIsS0FBSztJQUN2QixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRVMsVUFBVWEsUUFBUWIsT0FBTyxFQUFFYyxRQUFRLEVBQUU5RixJQUFJLEVBQUUrRixLQUFLLEVBQUUsR0FBR3JCLFNBQVMsQ0FBQztJQUN2RSxNQUFNLENBQUNLLFdBQVdpQixnQkFBZ0IsR0FBR2pILDJDQUFjLENBQUNpRyxRQUFRa0IsVUFBVTtJQUN0RSxNQUFNQyx1QkFBdUJwSCx5Q0FBWSxDQUFDO1FBQ3RDc0gsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxrQkFBa0I7UUFDbEJDLGNBQWM7UUFDZEMsU0FBUztRQUNUQyxRQUFRO0lBQ1o7SUFDQW5CLDBCQUEwQixJQUFNVCxRQUFRNkIsVUFBVSxDQUFDO1lBQy9DN0c7WUFDQStFLFdBQVdvQixxQkFBcUJXLE9BQU87WUFDdkNmO1lBQ0FnQixVQUFVLENBQUNoQztnQkFDUCxDQUFDZSxZQUNHRSxnQkFBZ0I7b0JBQ1osR0FBR2hCLFFBQVFrQixVQUFVO29CQUNyQixHQUFHbkIsU0FBUztnQkFDaEI7WUFDUjtRQUNKLElBQUk7UUFBQy9FO1FBQU04RjtRQUFVQztLQUFNO0lBQzNCaEgsNENBQWUsQ0FBQztRQUNab0gscUJBQXFCVyxPQUFPLENBQUNILE9BQU8sSUFBSTNCLFFBQVFnQyxTQUFTLENBQUM7SUFDOUQsR0FBRztRQUFDaEM7S0FBUTtJQUNaLE9BQU9qRywwQ0FBYSxDQUFDLElBQU0rRixrQkFBa0JDLFdBQVdDLFNBQVNtQixxQkFBcUJXLE9BQU8sRUFBRSxRQUFRO1FBQUMvQjtRQUFXQztLQUFRO0FBQy9IO0FBRUEsSUFBSWtDLFdBQVcsQ0FBQzlILFNBQVUsT0FBT0EsV0FBVTtBQUUzQyxJQUFJK0gsc0JBQXNCLENBQUMvRyxPQUFPZ0gsUUFBUUMsWUFBWUMsVUFBVWhGO0lBQzVELElBQUk0RSxTQUFTOUcsUUFBUTtRQUNqQmtILFlBQVlGLE9BQU9HLEtBQUssQ0FBQ0MsR0FBRyxDQUFDcEg7UUFDN0IsT0FBTytCLElBQUlrRixZQUFZakgsT0FBT2tDO0lBQ2xDO0lBQ0EsSUFBSTdDLE1BQU1DLE9BQU8sQ0FBQ1UsUUFBUTtRQUN0QixPQUFPQSxNQUFNcUgsR0FBRyxDQUFDLENBQUNDLFlBQWVKLENBQUFBLFlBQVlGLE9BQU9HLEtBQUssQ0FBQ0MsR0FBRyxDQUFDRSxZQUMxRHZGLElBQUlrRixZQUFZSyxVQUFTO0lBQ2pDO0lBQ0FKLFlBQWFGLENBQUFBLE9BQU9PLFFBQVEsR0FBRyxJQUFHO0lBQ2xDLE9BQU9OO0FBQ1g7QUFFQSxJQUFJTyxjQUFjLENBQUN4SSxTQUFVRSxrQkFBa0JGLFdBQVUsQ0FBQ0csYUFBYUg7QUFFdkUsU0FBU3lJLFVBQVVDLE9BQU8sRUFBRUMsT0FBTyxFQUFFQyxvQkFBb0IsSUFBSUMsU0FBUztJQUNsRSxJQUFJTCxZQUFZRSxZQUFZRixZQUFZRyxVQUFVO1FBQzlDLE9BQU9ELFlBQVlDO0lBQ3ZCO0lBQ0EsSUFBSTVJLGFBQWEySSxZQUFZM0ksYUFBYTRJLFVBQVU7UUFDaEQsT0FBT0QsUUFBUUksT0FBTyxPQUFPSCxRQUFRRyxPQUFPO0lBQ2hEO0lBQ0EsTUFBTUMsUUFBUTlDLE9BQU8rQyxJQUFJLENBQUNOO0lBQzFCLE1BQU1PLFFBQVFoRCxPQUFPK0MsSUFBSSxDQUFDTDtJQUMxQixJQUFJSSxNQUFNdEYsTUFBTSxLQUFLd0YsTUFBTXhGLE1BQU0sRUFBRTtRQUMvQixPQUFPO0lBQ1g7SUFDQSxJQUFJbUYsa0JBQWtCM0gsR0FBRyxDQUFDeUgsWUFBWUUsa0JBQWtCM0gsR0FBRyxDQUFDMEgsVUFBVTtRQUNsRSxPQUFPO0lBQ1g7SUFDQUMsa0JBQWtCUixHQUFHLENBQUNNO0lBQ3RCRSxrQkFBa0JSLEdBQUcsQ0FBQ087SUFDdEIsS0FBSyxNQUFNekcsT0FBTzZHLE1BQU87UUFDckIsTUFBTUcsT0FBT1IsT0FBTyxDQUFDeEcsSUFBSTtRQUN6QixJQUFJLENBQUMrRyxNQUFNRSxRQUFRLENBQUNqSCxNQUFNO1lBQ3RCLE9BQU87UUFDWDtRQUNBLElBQUlBLFFBQVEsT0FBTztZQUNmLE1BQU1rSCxPQUFPVCxPQUFPLENBQUN6RyxJQUFJO1lBQ3pCLElBQUksYUFBY2dILFNBQVNuSixhQUFhcUosU0FDbkNoSixTQUFTOEksU0FBUzlJLFNBQVNnSixTQUMzQi9JLE1BQU1DLE9BQU8sQ0FBQzRJLFNBQVM3SSxNQUFNQyxPQUFPLENBQUM4SSxRQUNwQyxDQUFDWCxVQUFVUyxNQUFNRSxNQUFNUixxQkFDdkJNLFNBQVNFLE1BQU07Z0JBQ2pCLE9BQU87WUFDWDtRQUNKO0lBQ0o7SUFDQSxPQUFPO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FDRCxTQUFTQyxTQUFTL0QsS0FBSztJQUNuQixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRVMsVUFBVWEsUUFBUWIsT0FBTyxFQUFFaEYsSUFBSSxFQUFFc0MsWUFBWSxFQUFFd0QsUUFBUSxFQUFFQyxLQUFLLEVBQUUyQyxPQUFPLEVBQUcsR0FBR2hFLFNBQVMsQ0FBQztJQUMvRixNQUFNaUUsZ0JBQWdCNUoseUNBQVksQ0FBQ3VEO0lBQ25DLE1BQU1zRyxXQUFXN0oseUNBQVksQ0FBQzJKO0lBQzlCLE1BQU1HLHFCQUFxQjlKLHlDQUFZLENBQUM0QztJQUN4Q2lILFNBQVM5QixPQUFPLEdBQUc0QjtJQUNuQixNQUFNSSxtQkFBbUIvSiwwQ0FBYSxDQUFDLElBQU1pRyxRQUFRK0QsU0FBUyxDQUFDL0ksTUFBTTJJLGNBQWM3QixPQUFPLEdBQUc7UUFBQzlCO1FBQVNoRjtLQUFLO0lBQzVHLE1BQU0sQ0FBQ1osUUFBTzRKLFlBQVksR0FBR2pLLDJDQUFjLENBQUM2SixTQUFTOUIsT0FBTyxHQUFHOEIsU0FBUzlCLE9BQU8sQ0FBQ2dDLG9CQUFvQkE7SUFDcEdyRCwwQkFBMEIsSUFBTVQsUUFBUTZCLFVBQVUsQ0FBQztZQUMvQzdHO1lBQ0ErRSxXQUFXO2dCQUNQa0UsUUFBUTtZQUNaO1lBQ0FsRDtZQUNBZ0IsVUFBVSxDQUFDaEM7Z0JBQ1AsSUFBSSxDQUFDZSxVQUFVO29CQUNYLE1BQU11QixhQUFhRixvQkFBb0JuSCxNQUFNZ0YsUUFBUW9DLE1BQU0sRUFBRXJDLFVBQVVrRSxNQUFNLElBQUlqRSxRQUFRa0UsV0FBVyxFQUFFLE9BQU9QLGNBQWM3QixPQUFPO29CQUNsSSxJQUFJOEIsU0FBUzlCLE9BQU8sRUFBRTt3QkFDbEIsTUFBTXFDLHFCQUFxQlAsU0FBUzlCLE9BQU8sQ0FBQ087d0JBQzVDLElBQUksQ0FBQ1EsVUFBVXNCLG9CQUFvQk4sbUJBQW1CL0IsT0FBTyxHQUFHOzRCQUM1RGtDLFlBQVlHOzRCQUNaTixtQkFBbUIvQixPQUFPLEdBQUdxQzt3QkFDakM7b0JBQ0osT0FDSzt3QkFDREgsWUFBWTNCO29CQUNoQjtnQkFDSjtZQUNKO1FBQ0osSUFBSTtRQUFDckM7UUFBU2M7UUFBVTlGO1FBQU0rRjtLQUFNO0lBQ3BDaEgsNENBQWUsQ0FBQyxJQUFNaUcsUUFBUW9FLGdCQUFnQjtJQUM5QyxPQUFPaEs7QUFDWDtBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXVCQyxHQUNELFNBQVNpSyxjQUFjM0UsS0FBSztJQUN4QixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRXZFLElBQUksRUFBRThGLFFBQVEsRUFBRWQsVUFBVWEsUUFBUWIsT0FBTyxFQUFFc0UsZ0JBQWdCLEVBQUVoSCxZQUFZLEVBQUcsR0FBR29DO0lBQ3ZGLE1BQU02RSxlQUFlcEosbUJBQW1CNkUsUUFBUW9DLE1BQU0sQ0FBQ29DLEtBQUssRUFBRXhKO0lBQzlELE1BQU04SSxtQkFBbUIvSiwwQ0FBYSxDQUFDLElBQU1vRCxJQUFJNkMsUUFBUWtFLFdBQVcsRUFBRWxKLE1BQU1tQyxJQUFJNkMsUUFBUUksY0FBYyxFQUFFcEYsTUFBTXNDLGdCQUFnQjtRQUFDMEM7UUFBU2hGO1FBQU1zQztLQUFhO0lBQzNKLE1BQU1sRCxTQUFRcUosU0FBUztRQUNuQnpEO1FBQ0FoRjtRQUNBc0MsY0FBY3dHO1FBQ2QvQyxPQUFPO0lBQ1g7SUFDQSxNQUFNaEIsWUFBWWEsYUFBYTtRQUMzQlo7UUFDQWhGO1FBQ0ErRixPQUFPO0lBQ1g7SUFDQSxNQUFNMEQsU0FBUzFLLHlDQUFZLENBQUMyRjtJQUM1QixNQUFNZ0YsaUJBQWlCM0sseUNBQVksQ0FBQ2lHLFFBQVEyRSxRQUFRLENBQUMzSixNQUFNO1FBQ3ZELEdBQUcwRSxNQUFNa0YsS0FBSztRQUNkeEssT0FBQUE7UUFDQSxHQUFJcUQsVUFBVWlDLE1BQU1vQixRQUFRLElBQUk7WUFBRUEsVUFBVXBCLE1BQU1vQixRQUFRO1FBQUMsSUFBSSxDQUFDLENBQUM7SUFDckU7SUFDQTJELE9BQU8zQyxPQUFPLEdBQUdwQztJQUNqQixNQUFNbUYsYUFBYTlLLDBDQUFhLENBQUMsSUFBTXNHLE9BQU95RSxnQkFBZ0IsQ0FBQyxDQUFDLEdBQUc7WUFDL0RDLFNBQVM7Z0JBQ0xDLFlBQVk7Z0JBQ1o3SCxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJNEMsVUFBVTZCLE1BQU0sRUFBRTVHO1lBQ3ZDO1lBQ0FxRyxTQUFTO2dCQUNMMkQsWUFBWTtnQkFDWjdILEtBQUssSUFBTSxDQUFDLENBQUNBLElBQUk0QyxVQUFVd0IsV0FBVyxFQUFFdkc7WUFDNUM7WUFDQWlLLFdBQVc7Z0JBQ1BELFlBQVk7Z0JBQ1o3SCxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJNEMsVUFBVXlCLGFBQWEsRUFBRXhHO1lBQzlDO1lBQ0EwRyxjQUFjO2dCQUNWc0QsWUFBWTtnQkFDWjdILEtBQUssSUFBTSxDQUFDLENBQUNBLElBQUk0QyxVQUFVMEIsZ0JBQWdCLEVBQUV6RztZQUNqRDtZQUNBa0ssT0FBTztnQkFDSEYsWUFBWTtnQkFDWjdILEtBQUssSUFBTUEsSUFBSTRDLFVBQVU2QixNQUFNLEVBQUU1RztZQUNyQztRQUNKLElBQUk7UUFBQytFO1FBQVcvRTtLQUFLO0lBQ3JCLE1BQU13RCxXQUFXekUsOENBQWlCLENBQUMsQ0FBQ2EsUUFBVThKLGVBQWU1QyxPQUFPLENBQUN0RCxRQUFRLENBQUM7WUFDMUUzRCxRQUFRO2dCQUNKVCxPQUFPTyxjQUFjQztnQkFDckJJLE1BQU1BO1lBQ1Y7WUFDQWQsTUFBTWdFLE9BQU9HLE1BQU07UUFDdkIsSUFBSTtRQUFDckQ7S0FBSztJQUNWLE1BQU11RCxTQUFTeEUsOENBQWlCLENBQUMsSUFBTTJLLGVBQWU1QyxPQUFPLENBQUN2RCxNQUFNLENBQUM7WUFDakUxRCxRQUFRO2dCQUNKVCxPQUFPK0MsSUFBSTZDLFFBQVFrRSxXQUFXLEVBQUVsSjtnQkFDaENBLE1BQU1BO1lBQ1Y7WUFDQWQsTUFBTWdFLE9BQU9DLElBQUk7UUFDckIsSUFBSTtRQUFDbkQ7UUFBTWdGLFFBQVFrRSxXQUFXO0tBQUM7SUFDL0IsTUFBTWtCLE1BQU1yTCw4Q0FBaUIsQ0FBQyxDQUFDc0w7UUFDM0IsTUFBTUMsUUFBUW5JLElBQUk2QyxRQUFRdUYsT0FBTyxFQUFFdks7UUFDbkMsSUFBSXNLLFNBQVNELEtBQUs7WUFDZEMsTUFBTUUsRUFBRSxDQUFDSixHQUFHLEdBQUc7Z0JBQ1hLLE9BQU8sSUFBTUosSUFBSUksS0FBSyxJQUFJSixJQUFJSSxLQUFLO2dCQUNuQ0MsUUFBUSxJQUFNTCxJQUFJSyxNQUFNLElBQUlMLElBQUlLLE1BQU07Z0JBQ3RDQyxtQkFBbUIsQ0FBQ0MsVUFBWVAsSUFBSU0saUJBQWlCLENBQUNDO2dCQUN0REMsZ0JBQWdCLElBQU1SLElBQUlRLGNBQWM7WUFDNUM7UUFDSjtJQUNKLEdBQUc7UUFBQzdGLFFBQVF1RixPQUFPO1FBQUV2SztLQUFLO0lBQzFCLE1BQU1zSyxRQUFRdkwsMENBQWEsQ0FBQyxJQUFPO1lBQy9CaUI7WUFDQVosT0FBQUE7WUFDQSxHQUFJcUQsVUFBVXFELGFBQWFmLFVBQVVlLFFBQVEsR0FDdkM7Z0JBQUVBLFVBQVVmLFVBQVVlLFFBQVEsSUFBSUE7WUFBUyxJQUMzQyxDQUFDLENBQUM7WUFDUnRDO1lBQ0FEO1lBQ0E2RztRQUNKLElBQUk7UUFBQ3BLO1FBQU04RjtRQUFVZixVQUFVZSxRQUFRO1FBQUV0QztRQUFVRDtRQUFRNkc7UUFBS2hMO0tBQU07SUFDdEVMLDRDQUFlLENBQUM7UUFDWixNQUFNK0wseUJBQXlCOUYsUUFBUStGLFFBQVEsQ0FBQ3pCLGdCQUFnQixJQUFJQTtRQUNwRXRFLFFBQVEyRSxRQUFRLENBQUMzSixNQUFNO1lBQ25CLEdBQUd5SixPQUFPM0MsT0FBTyxDQUFDOEMsS0FBSztZQUN2QixHQUFJbkgsVUFBVWdILE9BQU8zQyxPQUFPLENBQUNoQixRQUFRLElBQy9CO2dCQUFFQSxVQUFVMkQsT0FBTzNDLE9BQU8sQ0FBQ2hCLFFBQVE7WUFBQyxJQUNwQyxDQUFDLENBQUM7UUFDWjtRQUNBLE1BQU1rRixnQkFBZ0IsQ0FBQ2hMLE1BQU1aO1lBQ3pCLE1BQU1rTCxRQUFRbkksSUFBSTZDLFFBQVF1RixPQUFPLEVBQUV2SztZQUNuQyxJQUFJc0ssU0FBU0EsTUFBTUUsRUFBRSxFQUFFO2dCQUNuQkYsTUFBTUUsRUFBRSxDQUFDUyxLQUFLLEdBQUc3TDtZQUNyQjtRQUNKO1FBQ0E0TCxjQUFjaEwsTUFBTTtRQUNwQixJQUFJOEssd0JBQXdCO1lBQ3hCLE1BQU0xTCxTQUFRNEIsWUFBWW1CLElBQUk2QyxRQUFRK0YsUUFBUSxDQUFDNUYsYUFBYSxFQUFFbkY7WUFDOUQwQyxJQUFJc0MsUUFBUUksY0FBYyxFQUFFcEYsTUFBTVo7WUFDbEMsSUFBSXFDLFlBQVlVLElBQUk2QyxRQUFRa0UsV0FBVyxFQUFFbEosUUFBUTtnQkFDN0MwQyxJQUFJc0MsUUFBUWtFLFdBQVcsRUFBRWxKLE1BQU1aO1lBQ25DO1FBQ0o7UUFDQSxDQUFDbUssZ0JBQWdCdkUsUUFBUTJFLFFBQVEsQ0FBQzNKO1FBQ2xDLE9BQU87WUFDRnVKLENBQUFBLGVBQ0t1QiwwQkFBMEIsQ0FBQzlGLFFBQVFrRyxNQUFNLENBQUNDLE1BQU0sR0FDaERMLHNCQUFxQixJQUNyQjlGLFFBQVFvRyxVQUFVLENBQUNwTCxRQUNuQmdMLGNBQWNoTCxNQUFNO1FBQzlCO0lBQ0osR0FBRztRQUFDQTtRQUFNZ0Y7UUFBU3VFO1FBQWNEO0tBQWlCO0lBQ2xEdkssNENBQWUsQ0FBQztRQUNaaUcsUUFBUXFHLGlCQUFpQixDQUFDO1lBQ3RCdkY7WUFDQTlGO1FBQ0o7SUFDSixHQUFHO1FBQUM4RjtRQUFVOUY7UUFBTWdGO0tBQVE7SUFDNUIsT0FBT2pHLDBDQUFhLENBQUMsSUFBTztZQUN4QnVMO1lBQ0F2RjtZQUNBOEU7UUFDSixJQUFJO1FBQUNTO1FBQU92RjtRQUFXOEU7S0FBVztBQUN0QztBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXlDQyxHQUNELE1BQU15QixhQUFhLENBQUM1RyxRQUFVQSxNQUFNNkcsTUFBTSxDQUFDbEMsY0FBYzNFO0FBRXpELE1BQU04RyxVQUFVLENBQUNDO0lBQ2IsTUFBTUMsU0FBUyxDQUFDO0lBQ2hCLEtBQUssTUFBTXBLLE9BQU8rRCxPQUFPK0MsSUFBSSxDQUFDcUQsS0FBTTtRQUNoQyxJQUFJbE0sYUFBYWtNLEdBQUcsQ0FBQ25LLElBQUksS0FBS21LLEdBQUcsQ0FBQ25LLElBQUksS0FBSyxNQUFNO1lBQzdDLE1BQU1xSyxTQUFTSCxRQUFRQyxHQUFHLENBQUNuSyxJQUFJO1lBQy9CLEtBQUssTUFBTXNLLGFBQWF2RyxPQUFPK0MsSUFBSSxDQUFDdUQsUUFBUztnQkFDekNELE1BQU0sQ0FBQyxDQUFDLEVBQUVwSyxJQUFJLENBQUMsRUFBRXNLLFVBQVUsQ0FBQyxDQUFDLEdBQUdELE1BQU0sQ0FBQ0MsVUFBVTtZQUNyRDtRQUNKLE9BQ0s7WUFDREYsTUFBTSxDQUFDcEssSUFBSSxHQUFHbUssR0FBRyxDQUFDbkssSUFBSTtRQUMxQjtJQUNKO0lBQ0EsT0FBT29LO0FBQ1g7QUFFQSxNQUFNRyxlQUFlO0FBQ3JCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FxQkMsR0FDRCxTQUFTQyxLQUFLcEgsS0FBSztJQUNmLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxDQUFDd0gsU0FBU0MsV0FBVyxHQUFHak4sMkNBQWMsQ0FBQztJQUM3QyxNQUFNLEVBQUVpRyxVQUFVYSxRQUFRYixPQUFPLEVBQUV2QixRQUFRLEVBQUVrQixRQUFRLEVBQUV3RyxNQUFNLEVBQUVjLFNBQVNKLFlBQVksRUFBRUssT0FBTyxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRWIsTUFBTSxFQUFFYyxTQUFTLEVBQUVDLGNBQWMsRUFBRSxHQUFHQyxNQUFNLEdBQUc3SDtJQUNoSyxNQUFNOEgsU0FBUyxPQUFPNU07UUFDbEIsSUFBSTZNLFdBQVc7UUFDZixJQUFJdk4sT0FBTztRQUNYLE1BQU04RixRQUFRMEgsWUFBWSxDQUFDLE9BQU96TDtZQUM5QixNQUFNMEwsV0FBVyxJQUFJQztZQUNyQixJQUFJQyxlQUFlO1lBQ25CLElBQUk7Z0JBQ0FBLGVBQWVDLEtBQUtDLFNBQVMsQ0FBQzlMO1lBQ2xDLEVBQ0EsT0FBTytMLElBQUksQ0FBRTtZQUNiLE1BQU1DLG9CQUFvQnpCLFFBQVF4RyxRQUFRa0UsV0FBVztZQUNyRCxJQUFLLE1BQU01SCxPQUFPMkwsa0JBQW1CO2dCQUNqQ04sU0FBU08sTUFBTSxDQUFDNUwsS0FBSzJMLGlCQUFpQixDQUFDM0wsSUFBSTtZQUMvQztZQUNBLElBQUltQyxVQUFVO2dCQUNWLE1BQU1BLFNBQVM7b0JBQ1h4QztvQkFDQXJCO29CQUNBcU07b0JBQ0FVO29CQUNBRTtnQkFDSjtZQUNKO1lBQ0EsSUFBSTFCLFFBQVE7Z0JBQ1IsSUFBSTtvQkFDQSxNQUFNZ0MsZ0NBQWdDO3dCQUNsQ2pCLFdBQVdBLE9BQU8sQ0FBQyxlQUFlO3dCQUNsQ0M7cUJBQ0gsQ0FBQ2lCLElBQUksQ0FBQyxDQUFDaE8sU0FBVUEsVUFBU0EsT0FBTW1KLFFBQVEsQ0FBQztvQkFDMUMsTUFBTThFLFdBQVcsTUFBTUMsTUFBTUMsT0FBT3BDLFNBQVM7d0JBQ3pDYzt3QkFDQUMsU0FBUzs0QkFDTCxHQUFHQSxPQUFPOzRCQUNWLEdBQUlDLFdBQVdBLFlBQVksd0JBQ3JCO2dDQUFFLGdCQUFnQkE7NEJBQVEsSUFDMUIsQ0FBQyxDQUFDO3dCQUNaO3dCQUNBcUIsTUFBTUwsZ0NBQWdDTixlQUFlRjtvQkFDekQ7b0JBQ0EsSUFBSVUsWUFDQ2YsQ0FBQUEsaUJBQ0ssQ0FBQ0EsZUFBZWUsU0FBU0ksTUFBTSxJQUMvQkosU0FBU0ksTUFBTSxHQUFHLE9BQU9KLFNBQVNJLE1BQU0sSUFBSSxHQUFFLEdBQUk7d0JBQ3hEaEIsV0FBVzt3QkFDWEwsV0FBV0EsUUFBUTs0QkFBRWlCO3dCQUFTO3dCQUM5Qm5PLE9BQU9xTyxPQUFPRixTQUFTSSxNQUFNO29CQUNqQyxPQUNLO3dCQUNEcEIsYUFBYUEsVUFBVTs0QkFBRWdCO3dCQUFTO29CQUN0QztnQkFDSixFQUNBLE9BQU9uRCxPQUFPO29CQUNWdUMsV0FBVztvQkFDWEwsV0FBV0EsUUFBUTt3QkFBRWxDO29CQUFNO2dCQUMvQjtZQUNKO1FBQ0osR0FBR3RLO1FBQ0gsSUFBSTZNLFlBQVkvSCxNQUFNTSxPQUFPLEVBQUU7WUFDM0JOLE1BQU1NLE9BQU8sQ0FBQzBJLFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQy9CQyxvQkFBb0I7WUFDeEI7WUFDQW5KLE1BQU1NLE9BQU8sQ0FBQzhJLFFBQVEsQ0FBQyxlQUFlO2dCQUNsQzVPO1lBQ0o7UUFDSjtJQUNKO0lBQ0FILDRDQUFlLENBQUM7UUFDWmlOLFdBQVc7SUFDZixHQUFHLEVBQUU7SUFDTCxPQUFPVCx1QkFBVXhNLGdEQUFtQixDQUFDQSwyQ0FBYyxFQUFFLE1BQU13TSxPQUFPO1FBQzlEaUI7SUFDSixvQkFBUXpOLGdEQUFtQixDQUFDLFFBQVE7UUFBRWlQLFlBQVlqQztRQUFTWixRQUFRQTtRQUFRYyxRQUFRQTtRQUFRRSxTQUFTQTtRQUFTMUksVUFBVStJO1FBQVEsR0FBR0QsSUFBSTtJQUFDLEdBQUc1SDtBQUM5STtBQUVBLElBQUlzSixlQUFlLENBQUNqTyxNQUFNa08sMEJBQTBCdEgsUUFBUTFILE1BQU0wTCxVQUFZc0QsMkJBQ3hFO1FBQ0UsR0FBR3RILE1BQU0sQ0FBQzVHLEtBQUs7UUFDZm1PLE9BQU87WUFDSCxHQUFJdkgsTUFBTSxDQUFDNUcsS0FBSyxJQUFJNEcsTUFBTSxDQUFDNUcsS0FBSyxDQUFDbU8sS0FBSyxHQUFHdkgsTUFBTSxDQUFDNUcsS0FBSyxDQUFDbU8sS0FBSyxHQUFHLENBQUMsQ0FBQztZQUNoRSxDQUFDalAsS0FBSyxFQUFFMEwsV0FBVztRQUN2QjtJQUNKLElBQ0UsQ0FBQztBQUVQLElBQUl3RCx3QkFBd0IsQ0FBQ2hQLFNBQVdLLE1BQU1DLE9BQU8sQ0FBQ04sVUFBU0EsU0FBUTtRQUFDQTtLQUFNO0FBRTlFLElBQUlpUCxnQkFBZ0I7SUFDaEIsSUFBSUMsYUFBYSxFQUFFO0lBQ25CLE1BQU1WLE9BQU8sQ0FBQ3hPO1FBQ1YsS0FBSyxNQUFNbVAsWUFBWUQsV0FBWTtZQUMvQkMsU0FBU1gsSUFBSSxJQUFJVyxTQUFTWCxJQUFJLENBQUN4TztRQUNuQztJQUNKO0lBQ0EsTUFBTW9QLFlBQVksQ0FBQ0Q7UUFDZkQsV0FBV0csSUFBSSxDQUFDRjtRQUNoQixPQUFPO1lBQ0hHLGFBQWE7Z0JBQ1RKLGFBQWFBLFdBQVd6TSxNQUFNLENBQUMsQ0FBQzhNLElBQU1BLE1BQU1KO1lBQ2hEO1FBQ0o7SUFDSjtJQUNBLE1BQU1HLGNBQWM7UUFDaEJKLGFBQWEsRUFBRTtJQUNuQjtJQUNBLE9BQU87UUFDSCxJQUFJTSxhQUFZO1lBQ1osT0FBT047UUFDWDtRQUNBVjtRQUNBWTtRQUNBRTtJQUNKO0FBQ0o7QUFFQSxJQUFJRyxnQkFBZ0IsQ0FBQ3pQLFNBQVVJLFNBQVNKLFdBQVUsQ0FBQ2lHLE9BQU8rQyxJQUFJLENBQUNoSixRQUFPeUQsTUFBTTtBQUU1RSxJQUFJaU0sY0FBYyxDQUFDN1AsVUFBWUEsUUFBUUMsSUFBSSxLQUFLO0FBRWhELElBQUk2UCxhQUFhLENBQUMzUCxTQUFVLE9BQU9BLFdBQVU7QUFFN0MsSUFBSTRQLGdCQUFnQixDQUFDNVA7SUFDakIsSUFBSSxDQUFDd0IsT0FBTztRQUNSLE9BQU87SUFDWDtJQUNBLE1BQU1xTyxRQUFRN1AsU0FBUUEsT0FBTThQLGFBQWEsR0FBRztJQUM1QyxPQUFROVAsa0JBQ0g2UCxDQUFBQSxTQUFTQSxNQUFNRSxXQUFXLEdBQUdGLE1BQU1FLFdBQVcsQ0FBQ3JPLFdBQVcsR0FBR0EsV0FBVTtBQUNoRjtBQUVBLElBQUlzTyxtQkFBbUIsQ0FBQ25RLFVBQVlBLFFBQVFDLElBQUksS0FBSyxDQUFDLGVBQWUsQ0FBQztBQUV0RSxJQUFJbVEsZUFBZSxDQUFDcFEsVUFBWUEsUUFBUUMsSUFBSSxLQUFLO0FBRWpELElBQUlvUSxvQkFBb0IsQ0FBQ2xGLE1BQVFpRixhQUFhakYsUUFBUXBMLGdCQUFnQm9MO0FBRXRFLElBQUltRixPQUFPLENBQUNuRixNQUFRNEUsY0FBYzVFLFFBQVFBLElBQUlvRixXQUFXO0FBRXpELFNBQVNDLFFBQVFyTixNQUFNLEVBQUVzTixVQUFVO0lBQy9CLE1BQU03TSxTQUFTNk0sV0FBV0MsS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHOU0sTUFBTTtJQUM3QyxJQUFJRixRQUFRO0lBQ1osTUFBT0EsUUFBUUUsT0FBUTtRQUNuQlQsU0FBU1gsWUFBWVcsVUFBVU8sVUFBVVAsTUFBTSxDQUFDc04sVUFBVSxDQUFDL00sUUFBUSxDQUFDO0lBQ3hFO0lBQ0EsT0FBT1A7QUFDWDtBQUNBLFNBQVN3TixhQUFhbkUsR0FBRztJQUNyQixJQUFLLE1BQU1uSyxPQUFPbUssSUFBSztRQUNuQixJQUFJQSxJQUFJOUssY0FBYyxDQUFDVyxRQUFRLENBQUNHLFlBQVlnSyxHQUFHLENBQUNuSyxJQUFJLEdBQUc7WUFDbkQsT0FBTztRQUNYO0lBQ0o7SUFDQSxPQUFPO0FBQ1g7QUFDQSxTQUFTdU8sTUFBTXpOLE1BQU0sRUFBRUMsSUFBSTtJQUN2QixNQUFNeU4sUUFBUXJRLE1BQU1DLE9BQU8sQ0FBQzJDLFFBQ3RCQSxPQUNBZCxNQUFNYyxRQUNGO1FBQUNBO0tBQUssR0FDTk4sYUFBYU07SUFDdkIsTUFBTTBOLGNBQWNELE1BQU1qTixNQUFNLEtBQUssSUFBSVQsU0FBU3FOLFFBQVFyTixRQUFRME47SUFDbEUsTUFBTW5OLFFBQVFtTixNQUFNak4sTUFBTSxHQUFHO0lBQzdCLE1BQU12QixNQUFNd08sS0FBSyxDQUFDbk4sTUFBTTtJQUN4QixJQUFJb04sYUFBYTtRQUNiLE9BQU9BLFdBQVcsQ0FBQ3pPLElBQUk7SUFDM0I7SUFDQSxJQUFJcUIsVUFBVSxLQUNULFVBQVVvTixnQkFBZ0JsQixjQUFja0IsZ0JBQ3BDdFEsTUFBTUMsT0FBTyxDQUFDcVEsZ0JBQWdCSCxhQUFhRyxZQUFZLEdBQUk7UUFDaEVGLE1BQU16TixRQUFRME4sTUFBTUgsS0FBSyxDQUFDLEdBQUcsQ0FBQztJQUNsQztJQUNBLE9BQU92TjtBQUNYO0FBRUEsSUFBSTROLG9CQUFvQixDQUFDL087SUFDckIsSUFBSyxNQUFNSyxPQUFPTCxLQUFNO1FBQ3BCLElBQUk4TixXQUFXOU4sSUFBSSxDQUFDSyxJQUFJLEdBQUc7WUFDdkIsT0FBTztRQUNYO0lBQ0o7SUFDQSxPQUFPO0FBQ1g7QUFFQSxTQUFTMk8sZ0JBQWdCaFAsSUFBSSxFQUFFaVAsU0FBUyxDQUFDLENBQUM7SUFDdEMsTUFBTUMsb0JBQW9CMVEsTUFBTUMsT0FBTyxDQUFDdUI7SUFDeEMsSUFBSXpCLFNBQVN5QixTQUFTa1AsbUJBQW1CO1FBQ3JDLElBQUssTUFBTTdPLE9BQU9MLEtBQU07WUFDcEIsSUFBSXhCLE1BQU1DLE9BQU8sQ0FBQ3VCLElBQUksQ0FBQ0ssSUFBSSxLQUN0QjlCLFNBQVN5QixJQUFJLENBQUNLLElBQUksS0FBSyxDQUFDME8sa0JBQWtCL08sSUFBSSxDQUFDSyxJQUFJLEdBQUk7Z0JBQ3hENE8sTUFBTSxDQUFDNU8sSUFBSSxHQUFHN0IsTUFBTUMsT0FBTyxDQUFDdUIsSUFBSSxDQUFDSyxJQUFJLElBQUksRUFBRSxHQUFHLENBQUM7Z0JBQy9DMk8sZ0JBQWdCaFAsSUFBSSxDQUFDSyxJQUFJLEVBQUU0TyxNQUFNLENBQUM1TyxJQUFJO1lBQzFDLE9BQ0ssSUFBSSxDQUFDaEMsa0JBQWtCMkIsSUFBSSxDQUFDSyxJQUFJLEdBQUc7Z0JBQ3BDNE8sTUFBTSxDQUFDNU8sSUFBSSxHQUFHO1lBQ2xCO1FBQ0o7SUFDSjtJQUNBLE9BQU80TztBQUNYO0FBQ0EsU0FBU0UsZ0NBQWdDblAsSUFBSSxFQUFFb0csVUFBVSxFQUFFZ0oscUJBQXFCO0lBQzVFLE1BQU1GLG9CQUFvQjFRLE1BQU1DLE9BQU8sQ0FBQ3VCO0lBQ3hDLElBQUl6QixTQUFTeUIsU0FBU2tQLG1CQUFtQjtRQUNyQyxJQUFLLE1BQU03TyxPQUFPTCxLQUFNO1lBQ3BCLElBQUl4QixNQUFNQyxPQUFPLENBQUN1QixJQUFJLENBQUNLLElBQUksS0FDdEI5QixTQUFTeUIsSUFBSSxDQUFDSyxJQUFJLEtBQUssQ0FBQzBPLGtCQUFrQi9PLElBQUksQ0FBQ0ssSUFBSSxHQUFJO2dCQUN4RCxJQUFJRyxZQUFZNEYsZUFDWk8sWUFBWXlJLHFCQUFxQixDQUFDL08sSUFBSSxHQUFHO29CQUN6QytPLHFCQUFxQixDQUFDL08sSUFBSSxHQUFHN0IsTUFBTUMsT0FBTyxDQUFDdUIsSUFBSSxDQUFDSyxJQUFJLElBQzlDMk8sZ0JBQWdCaFAsSUFBSSxDQUFDSyxJQUFJLEVBQUUsRUFBRSxJQUM3Qjt3QkFBRSxHQUFHMk8sZ0JBQWdCaFAsSUFBSSxDQUFDSyxJQUFJLENBQUM7b0JBQUM7Z0JBQzFDLE9BQ0s7b0JBQ0Q4TyxnQ0FBZ0NuUCxJQUFJLENBQUNLLElBQUksRUFBRWhDLGtCQUFrQitILGNBQWMsQ0FBQyxJQUFJQSxVQUFVLENBQUMvRixJQUFJLEVBQUUrTyxxQkFBcUIsQ0FBQy9PLElBQUk7Z0JBQy9IO1lBQ0osT0FDSztnQkFDRCtPLHFCQUFxQixDQUFDL08sSUFBSSxHQUFHLENBQUN1RyxVQUFVNUcsSUFBSSxDQUFDSyxJQUFJLEVBQUUrRixVQUFVLENBQUMvRixJQUFJO1lBQ3RFO1FBQ0o7SUFDSjtJQUNBLE9BQU8rTztBQUNYO0FBQ0EsSUFBSUMsaUJBQWlCLENBQUNuTCxlQUFla0MsYUFBZStJLGdDQUFnQ2pMLGVBQWVrQyxZQUFZNEksZ0JBQWdCNUk7QUFFL0gsTUFBTWtKLGdCQUFnQjtJQUNsQm5SLE9BQU87SUFDUHVILFNBQVM7QUFDYjtBQUNBLE1BQU02SixjQUFjO0lBQUVwUixPQUFPO0lBQU11SCxTQUFTO0FBQUs7QUFDakQsSUFBSThKLG1CQUFtQixDQUFDQztJQUNwQixJQUFJalIsTUFBTUMsT0FBTyxDQUFDZ1IsVUFBVTtRQUN4QixJQUFJQSxRQUFRN04sTUFBTSxHQUFHLEdBQUc7WUFDcEIsTUFBTW9HLFNBQVN5SCxRQUNWN08sTUFBTSxDQUFDLENBQUM4TyxTQUFXQSxVQUFVQSxPQUFPN1EsT0FBTyxJQUFJLENBQUM2USxPQUFPN0ssUUFBUSxFQUMvRDJCLEdBQUcsQ0FBQyxDQUFDa0osU0FBV0EsT0FBT3ZSLEtBQUs7WUFDakMsT0FBTztnQkFBRUEsT0FBTzZKO2dCQUFRdEMsU0FBUyxDQUFDLENBQUNzQyxPQUFPcEcsTUFBTTtZQUFDO1FBQ3JEO1FBQ0EsT0FBTzZOLE9BQU8sQ0FBQyxFQUFFLENBQUM1USxPQUFPLElBQUksQ0FBQzRRLE9BQU8sQ0FBQyxFQUFFLENBQUM1SyxRQUFRLEdBRXpDNEssT0FBTyxDQUFDLEVBQUUsQ0FBQ0UsVUFBVSxJQUFJLENBQUNuUCxZQUFZaVAsT0FBTyxDQUFDLEVBQUUsQ0FBQ0UsVUFBVSxDQUFDeFIsS0FBSyxJQUMzRHFDLFlBQVlpUCxPQUFPLENBQUMsRUFBRSxDQUFDdFIsS0FBSyxLQUFLc1IsT0FBTyxDQUFDLEVBQUUsQ0FBQ3RSLEtBQUssS0FBSyxLQUNsRG9SLGNBQ0E7WUFBRXBSLE9BQU9zUixPQUFPLENBQUMsRUFBRSxDQUFDdFIsS0FBSztZQUFFdUgsU0FBUztRQUFLLElBQzdDNkosY0FDUkQ7SUFDVjtJQUNBLE9BQU9BO0FBQ1g7QUFFQSxJQUFJTSxrQkFBa0IsQ0FBQ3pSLFFBQU8sRUFBRTBSLGFBQWEsRUFBRUMsV0FBVyxFQUFFQyxVQUFVLEVBQUUsR0FBS3ZQLFlBQVlyQyxVQUNuRkEsU0FDQTBSLGdCQUNJMVIsV0FBVSxLQUNONlIsTUFDQTdSLFNBQ0ksQ0FBQ0EsU0FDREEsU0FDUjJSLGVBQWU3SixTQUFTOUgsVUFDcEIsSUFBSUMsS0FBS0QsVUFDVDRSLGFBQ0lBLFdBQVc1UixVQUNYQTtBQUVsQixNQUFNOFIsZ0JBQWdCO0lBQ2xCdkssU0FBUztJQUNUdkgsT0FBTztBQUNYO0FBQ0EsSUFBSStSLGdCQUFnQixDQUFDVCxVQUFZalIsTUFBTUMsT0FBTyxDQUFDZ1IsV0FDekNBLFFBQVFsTyxNQUFNLENBQUMsQ0FBQzRPLFVBQVVULFNBQVdBLFVBQVVBLE9BQU83USxPQUFPLElBQUksQ0FBQzZRLE9BQU83SyxRQUFRLEdBQzdFO1lBQ0VhLFNBQVM7WUFDVHZILE9BQU91UixPQUFPdlIsS0FBSztRQUN2QixJQUNFZ1MsVUFBVUYsaUJBQ2RBO0FBRU4sU0FBU0csY0FBYzdHLEVBQUU7SUFDckIsTUFBTUosTUFBTUksR0FBR0osR0FBRztJQUNsQixJQUFJMEUsWUFBWTFFLE1BQU07UUFDbEIsT0FBT0EsSUFBSWtILEtBQUs7SUFDcEI7SUFDQSxJQUFJakMsYUFBYWpGLE1BQU07UUFDbkIsT0FBTytHLGNBQWMzRyxHQUFHK0csSUFBSSxFQUFFblMsS0FBSztJQUN2QztJQUNBLElBQUlnUSxpQkFBaUJoRixNQUFNO1FBQ3ZCLE9BQU87ZUFBSUEsSUFBSW9ILGVBQWU7U0FBQyxDQUFDL0osR0FBRyxDQUFDLENBQUMsRUFBRXJJLE9BQUFBLE1BQUssRUFBRSxHQUFLQTtJQUN2RDtJQUNBLElBQUlKLGdCQUFnQm9MLE1BQU07UUFDdEIsT0FBT3FHLGlCQUFpQmpHLEdBQUcrRyxJQUFJLEVBQUVuUyxLQUFLO0lBQzFDO0lBQ0EsT0FBT3lSLGdCQUFnQnBQLFlBQVkySSxJQUFJaEwsS0FBSyxJQUFJb0wsR0FBR0osR0FBRyxDQUFDaEwsS0FBSyxHQUFHZ0wsSUFBSWhMLEtBQUssRUFBRW9MO0FBQzlFO0FBRUEsSUFBSWlILHFCQUFxQixDQUFDQyxhQUFhbkgsU0FBU29ILGNBQWNDO0lBQzFELE1BQU0xQixTQUFTLENBQUM7SUFDaEIsS0FBSyxNQUFNbFEsUUFBUTBSLFlBQWE7UUFDNUIsTUFBTXBILFFBQVFuSSxJQUFJb0ksU0FBU3ZLO1FBQzNCc0ssU0FBUzVILElBQUl3TixRQUFRbFEsTUFBTXNLLE1BQU1FLEVBQUU7SUFDdkM7SUFDQSxPQUFPO1FBQ0htSDtRQUNBdlIsT0FBTztlQUFJc1I7U0FBWTtRQUN2QnhCO1FBQ0EwQjtJQUNKO0FBQ0o7QUFFQSxJQUFJQyxVQUFVLENBQUN6UyxTQUFVQSxrQkFBaUIwUztBQUUxQyxJQUFJQyxlQUFlLENBQUNDLE9BQVN2USxZQUFZdVEsUUFDbkNBLE9BQ0FILFFBQVFHLFFBQ0pBLEtBQUtDLE1BQU0sR0FDWHpTLFNBQVN3UyxRQUNMSCxRQUFRRyxLQUFLNVMsS0FBSyxJQUNkNFMsS0FBSzVTLEtBQUssQ0FBQzZTLE1BQU0sR0FDakJELEtBQUs1UyxLQUFLLEdBQ2Q0UztBQUVkLElBQUlFLHFCQUFxQixDQUFDQyxPQUFVO1FBQ2hDQyxZQUFZLENBQUNELFFBQVFBLFNBQVM3TyxnQkFBZ0JHLFFBQVE7UUFDdEQ0TyxVQUFVRixTQUFTN08sZ0JBQWdCQyxNQUFNO1FBQ3pDK08sWUFBWUgsU0FBUzdPLGdCQUFnQkUsUUFBUTtRQUM3QytPLFNBQVNKLFNBQVM3TyxnQkFBZ0JLLEdBQUc7UUFDckM2TyxXQUFXTCxTQUFTN08sZ0JBQWdCSSxTQUFTO0lBQ2pEO0FBRUEsTUFBTStPLGlCQUFpQjtBQUN2QixJQUFJQyx1QkFBdUIsQ0FBQ0MsaUJBQW1CLENBQUMsQ0FBQ0Esa0JBQzdDLENBQUMsQ0FBQ0EsZUFBZXhPLFFBQVEsSUFDekIsQ0FBQyxDQUFFLFlBQVl3TyxlQUFleE8sUUFBUSxLQUNsQ3dPLGVBQWV4TyxRQUFRLENBQUMxRCxXQUFXLENBQUNULElBQUksS0FBS3lTLGtCQUM1Q2pULFNBQVNtVCxlQUFleE8sUUFBUSxLQUM3QmtCLE9BQU80RCxNQUFNLENBQUMwSixlQUFleE8sUUFBUSxFQUFFeU8sSUFBSSxDQUFDLENBQUNDLG1CQUFxQkEsaUJBQWlCcFMsV0FBVyxDQUFDVCxJQUFJLEtBQUt5UyxlQUFlO0FBRW5JLElBQUlLLGdCQUFnQixDQUFDcEMsVUFBWUEsUUFBUXpGLEtBQUssSUFDekN5RixDQUFBQSxRQUFReE0sUUFBUSxJQUNid00sUUFBUTVNLEdBQUcsSUFDWDRNLFFBQVE3TSxHQUFHLElBQ1g2TSxRQUFRM00sU0FBUyxJQUNqQjJNLFFBQVExTSxTQUFTLElBQ2pCME0sUUFBUXpNLE9BQU8sSUFDZnlNLFFBQVF2TSxRQUFRO0FBRXhCLElBQUk0TyxZQUFZLENBQUMvUyxNQUFNb0gsUUFBUTRMLGNBQWdCLENBQUNBLGVBQzNDNUwsQ0FBQUEsT0FBT08sUUFBUSxJQUNaUCxPQUFPRyxLQUFLLENBQUNsSCxHQUFHLENBQUNMLFNBQ2pCO1dBQUlvSCxPQUFPRyxLQUFLO0tBQUMsQ0FBQzZGLElBQUksQ0FBQyxDQUFDNkYsWUFBY2pULEtBQUtrVCxVQUFVLENBQUNELGNBQ2xELFNBQVN6UixJQUFJLENBQUN4QixLQUFLMlAsS0FBSyxDQUFDc0QsVUFBVXBRLE1BQU0sR0FBRTtBQUV2RCxNQUFNc1Esd0JBQXdCLENBQUNqRCxRQUFRL0UsUUFBUXVHLGFBQWEwQjtJQUN4RCxLQUFLLE1BQU05UixPQUFPb1EsZUFBZXJNLE9BQU8rQyxJQUFJLENBQUM4SCxRQUFTO1FBQ2xELE1BQU01RixRQUFRbkksSUFBSStOLFFBQVE1TztRQUMxQixJQUFJZ0osT0FBTztZQUNQLE1BQU0sRUFBRUUsRUFBRSxFQUFFLEdBQUc2SSxjQUFjLEdBQUcvSTtZQUNoQyxJQUFJRSxJQUFJO2dCQUNKLElBQUlBLEdBQUcrRyxJQUFJLElBQUkvRyxHQUFHK0csSUFBSSxDQUFDLEVBQUUsSUFBSXBHLE9BQU9YLEdBQUcrRyxJQUFJLENBQUMsRUFBRSxFQUFFalEsUUFBUSxDQUFDOFIsWUFBWTtvQkFDakUsT0FBTztnQkFDWCxPQUNLLElBQUk1SSxHQUFHSixHQUFHLElBQUllLE9BQU9YLEdBQUdKLEdBQUcsRUFBRUksR0FBR3hLLElBQUksS0FBSyxDQUFDb1QsWUFBWTtvQkFDdkQsT0FBTztnQkFDWCxPQUNLO29CQUNELElBQUlELHNCQUFzQkUsY0FBY2xJLFNBQVM7d0JBQzdDO29CQUNKO2dCQUNKO1lBQ0osT0FDSyxJQUFJM0wsU0FBUzZULGVBQWU7Z0JBQzdCLElBQUlGLHNCQUFzQkUsY0FBY2xJLFNBQVM7b0JBQzdDO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBQ0E7QUFDSjtBQUVBLFNBQVNtSSxrQkFBa0IxTSxNQUFNLEVBQUUyRCxPQUFPLEVBQUV2SyxJQUFJO0lBQzVDLE1BQU1rSyxRQUFRL0gsSUFBSXlFLFFBQVE1RztJQUMxQixJQUFJa0ssU0FBUzNJLE1BQU12QixPQUFPO1FBQ3RCLE9BQU87WUFDSGtLO1lBQ0FsSztRQUNKO0lBQ0o7SUFDQSxNQUFNSSxRQUFRSixLQUFLa0MsS0FBSyxDQUFDO0lBQ3pCLE1BQU85QixNQUFNeUMsTUFBTSxDQUFFO1FBQ2pCLE1BQU02RSxZQUFZdEgsTUFBTW1ULElBQUksQ0FBQztRQUM3QixNQUFNakosUUFBUW5JLElBQUlvSSxTQUFTN0M7UUFDM0IsTUFBTThMLGFBQWFyUixJQUFJeUUsUUFBUWM7UUFDL0IsSUFBSTRDLFNBQVMsQ0FBQzdLLE1BQU1DLE9BQU8sQ0FBQzRLLFVBQVV0SyxTQUFTMEgsV0FBVztZQUN0RCxPQUFPO2dCQUFFMUg7WUFBSztRQUNsQjtRQUNBLElBQUl3VCxjQUFjQSxXQUFXdFUsSUFBSSxFQUFFO1lBQy9CLE9BQU87Z0JBQ0hjLE1BQU0wSDtnQkFDTndDLE9BQU9zSjtZQUNYO1FBQ0o7UUFDQSxJQUFJQSxjQUFjQSxXQUFXQyxJQUFJLElBQUlELFdBQVdDLElBQUksQ0FBQ3ZVLElBQUksRUFBRTtZQUN2RCxPQUFPO2dCQUNIYyxNQUFNLENBQUMsRUFBRTBILFVBQVUsS0FBSyxDQUFDO2dCQUN6QndDLE9BQU9zSixXQUFXQyxJQUFJO1lBQzFCO1FBQ0o7UUFDQXJULE1BQU1zVCxHQUFHO0lBQ2I7SUFDQSxPQUFPO1FBQ0gxVDtJQUNKO0FBQ0o7QUFFQSxJQUFJMlQsd0JBQXdCLENBQUNDLGVBQWVwTyxpQkFBaUJRLGlCQUFpQmQ7SUFDMUVjLGdCQUFnQjROO0lBQ2hCLE1BQU0sRUFBRTVULElBQUksRUFBRSxHQUFHK0UsV0FBVyxHQUFHNk87SUFDL0IsT0FBUS9FLGNBQWM5SixjQUNsQk0sT0FBTytDLElBQUksQ0FBQ3JELFdBQVdsQyxNQUFNLElBQUl3QyxPQUFPK0MsSUFBSSxDQUFDNUMsaUJBQWlCM0MsTUFBTSxJQUNwRXdDLE9BQU8rQyxJQUFJLENBQUNyRCxXQUFXNk4sSUFBSSxDQUFDLENBQUN0UixNQUFRa0UsZUFBZSxDQUFDbEUsSUFBSSxLQUNwRCxFQUFDNEQsVUFBVTVCLGdCQUFnQkssR0FBRztBQUMzQztBQUVBLElBQUlrUSx3QkFBd0IsQ0FBQzdULE1BQU04VCxZQUFZL04sUUFBVSxDQUFDL0YsUUFDdEQsQ0FBQzhULGNBQ0Q5VCxTQUFTOFQsY0FDVDFGLHNCQUFzQnBPLE1BQU1vTixJQUFJLENBQUMsQ0FBQzJHLGNBQWdCQSxlQUM3Q2hPLENBQUFBLFFBQ0tnTyxnQkFBZ0JELGFBQ2hCQyxZQUFZYixVQUFVLENBQUNZLGVBQ3JCQSxXQUFXWixVQUFVLENBQUNhLFlBQVc7QUFFakQsSUFBSUMsaUJBQWlCLENBQUNoQixhQUFhL0ksV0FBV2dLLGFBQWFDLGdCQUFnQi9CO0lBQ3ZFLElBQUlBLEtBQUtJLE9BQU8sRUFBRTtRQUNkLE9BQU87SUFDWCxPQUNLLElBQUksQ0FBQzBCLGVBQWU5QixLQUFLSyxTQUFTLEVBQUU7UUFDckMsT0FBTyxDQUFFdkksQ0FBQUEsYUFBYStJLFdBQVU7SUFDcEMsT0FDSyxJQUFJaUIsY0FBY0MsZUFBZTdCLFFBQVEsR0FBR0YsS0FBS0UsUUFBUSxFQUFFO1FBQzVELE9BQU8sQ0FBQ1c7SUFDWixPQUNLLElBQUlpQixjQUFjQyxlQUFlNUIsVUFBVSxHQUFHSCxLQUFLRyxVQUFVLEVBQUU7UUFDaEUsT0FBT1U7SUFDWDtJQUNBLE9BQU87QUFDWDtBQUVBLElBQUltQixrQkFBa0IsQ0FBQy9KLEtBQUtwSyxPQUFTLENBQUM0QixRQUFRTyxJQUFJaUksS0FBS3BLLE9BQU82QyxNQUFNLElBQUlnTixNQUFNekYsS0FBS3BLO0FBRW5GLElBQUlvVSw0QkFBNEIsQ0FBQ3hOLFFBQVFzRCxPQUFPbEs7SUFDNUMsTUFBTXFVLG1CQUFtQmpHLHNCQUFzQmpNLElBQUl5RSxRQUFRNUc7SUFDM0QwQyxJQUFJMlIsa0JBQWtCLFFBQVFuSyxLQUFLLENBQUNsSyxLQUFLO0lBQ3pDMEMsSUFBSWtFLFFBQVE1RyxNQUFNcVU7SUFDbEIsT0FBT3pOO0FBQ1g7QUFFQSxJQUFJME4sWUFBWSxDQUFDbFYsU0FBVThILFNBQVM5SDtBQUVwQyxTQUFTbVYsaUJBQWlCaFMsTUFBTSxFQUFFNkgsR0FBRyxFQUFFbEwsT0FBTyxVQUFVO0lBQ3BELElBQUlvVixVQUFVL1IsV0FDVDlDLE1BQU1DLE9BQU8sQ0FBQzZDLFdBQVdBLE9BQU9pUyxLQUFLLENBQUNGLGNBQ3RDN1IsVUFBVUYsV0FBVyxDQUFDQSxRQUFTO1FBQ2hDLE9BQU87WUFDSHJEO1lBQ0EwTCxTQUFTMEosVUFBVS9SLFVBQVVBLFNBQVM7WUFDdEM2SDtRQUNKO0lBQ0o7QUFDSjtBQUVBLElBQUlxSyxxQkFBcUIsQ0FBQ0MsaUJBQW1CbFYsU0FBU2tWLG1CQUFtQixDQUFDN0MsUUFBUTZDLGtCQUM1RUEsaUJBQ0E7UUFDRXRWLE9BQU9zVjtRQUNQOUosU0FBUztJQUNiO0FBRUosSUFBSStKLGdCQUFnQixPQUFPckssT0FBT3NLLG9CQUFvQnZOLFlBQVk2RywwQkFBMEIwRCwyQkFBMkJpRDtJQUNuSCxNQUFNLEVBQUV6SyxHQUFHLEVBQUVtSCxJQUFJLEVBQUVyTixRQUFRLEVBQUVILFNBQVMsRUFBRUMsU0FBUyxFQUFFRixHQUFHLEVBQUVELEdBQUcsRUFBRUksT0FBTyxFQUFFRSxRQUFRLEVBQUVuRSxJQUFJLEVBQUU4USxhQUFhLEVBQUU3RixLQUFLLEVBQUcsR0FBR1gsTUFBTUUsRUFBRTtJQUN4SCxNQUFNc0ssYUFBYTNTLElBQUlrRixZQUFZckg7SUFDbkMsSUFBSSxDQUFDaUwsU0FBUzJKLG1CQUFtQnZVLEdBQUcsQ0FBQ0wsT0FBTztRQUN4QyxPQUFPLENBQUM7SUFDWjtJQUNBLE1BQU0rVSxXQUFXeEQsT0FBT0EsSUFBSSxDQUFDLEVBQUUsR0FBR25IO0lBQ2xDLE1BQU1PLG9CQUFvQixDQUFDQztRQUN2QixJQUFJZ0gsNkJBQTZCbUQsU0FBU2xLLGNBQWMsRUFBRTtZQUN0RGtLLFNBQVNwSyxpQkFBaUIsQ0FBQ2xJLFVBQVVtSSxXQUFXLEtBQUtBLFdBQVc7WUFDaEVtSyxTQUFTbEssY0FBYztRQUMzQjtJQUNKO0lBQ0EsTUFBTVgsUUFBUSxDQUFDO0lBQ2YsTUFBTThLLFVBQVUzRixhQUFhakY7SUFDN0IsTUFBTTZLLGFBQWFqVyxnQkFBZ0JvTDtJQUNuQyxNQUFNa0Ysb0JBQW9CMEYsV0FBV0M7SUFDckMsTUFBTUMsVUFBVSxDQUFFcEUsaUJBQWlCaEMsWUFBWTFFLElBQUcsS0FDOUMzSSxZQUFZMkksSUFBSWhMLEtBQUssS0FDckJxQyxZQUFZcVQsZUFDWDlGLGNBQWM1RSxRQUFRQSxJQUFJaEwsS0FBSyxLQUFLLE1BQ3JDMFYsZUFBZSxNQUNkclYsTUFBTUMsT0FBTyxDQUFDb1YsZUFBZSxDQUFDQSxXQUFXalMsTUFBTTtJQUNwRCxNQUFNc1Msb0JBQW9CbEgsYUFBYW1ILElBQUksQ0FBQyxNQUFNcFYsTUFBTWtPLDBCQUEwQmhFO0lBQ2xGLE1BQU1tTCxtQkFBbUIsQ0FBQ0MsV0FBV0Msa0JBQWtCQyxrQkFBa0JDLFVBQVU3Uix1QkFBdUJHLFNBQVMsRUFBRTJSLFVBQVU5Uix1QkFBdUJJLFNBQVM7UUFDM0osTUFBTTRHLFVBQVUwSyxZQUFZQyxtQkFBbUJDO1FBQy9DdEwsS0FBSyxDQUFDbEssS0FBSyxHQUFHO1lBQ1ZkLE1BQU1vVyxZQUFZRyxVQUFVQztZQUM1QjlLO1lBQ0FSO1lBQ0EsR0FBRytLLGtCQUFrQkcsWUFBWUcsVUFBVUMsU0FBUzlLLFFBQVE7UUFDaEU7SUFDSjtJQUNBLElBQUlpSyxlQUNFLENBQUNwVixNQUFNQyxPQUFPLENBQUNvVixlQUFlLENBQUNBLFdBQVdqUyxNQUFNLEdBQ2hEcUIsWUFDRyxFQUFFb0wscUJBQXNCNEYsQ0FBQUEsV0FBVzVWLGtCQUFrQndWLFdBQVUsS0FDM0RyUyxVQUFVcVMsZUFBZSxDQUFDQSxjQUMxQkcsY0FBYyxDQUFDeEUsaUJBQWlCYyxNQUFNNUssT0FBTyxJQUM3Q3FPLFdBQVcsQ0FBQzdELGNBQWNJLE1BQU01SyxPQUFPLEdBQUk7UUFDcEQsTUFBTSxFQUFFdkgsT0FBQUEsTUFBSyxFQUFFd0wsT0FBTyxFQUFFLEdBQUcwSixVQUFVcFEsWUFDL0I7WUFBRTlFLE9BQU8sQ0FBQyxDQUFDOEU7WUFBVTBHLFNBQVMxRztRQUFTLElBQ3ZDdVEsbUJBQW1CdlE7UUFDekIsSUFBSTlFLFFBQU87WUFDUDhLLEtBQUssQ0FBQ2xLLEtBQUssR0FBRztnQkFDVmQsTUFBTTBFLHVCQUF1Qk0sUUFBUTtnQkFDckMwRztnQkFDQVIsS0FBSzJLO2dCQUNMLEdBQUdJLGtCQUFrQnZSLHVCQUF1Qk0sUUFBUSxFQUFFMEcsUUFBUTtZQUNsRTtZQUNBLElBQUksQ0FBQ3NELDBCQUEwQjtnQkFDM0J2RCxrQkFBa0JDO2dCQUNsQixPQUFPVjtZQUNYO1FBQ0o7SUFDSjtJQUNBLElBQUksQ0FBQ2dMLFdBQVksRUFBQzVWLGtCQUFrQndFLFFBQVEsQ0FBQ3hFLGtCQUFrQnVFLElBQUcsR0FBSTtRQUNsRSxJQUFJeVI7UUFDSixJQUFJSztRQUNKLE1BQU1DLFlBQVluQixtQkFBbUI1UTtRQUNyQyxNQUFNZ1MsWUFBWXBCLG1CQUFtQjNRO1FBQ3JDLElBQUksQ0FBQ3hFLGtCQUFrQndWLGVBQWUsQ0FBQzdSLE1BQU02UixhQUFhO1lBQ3RELE1BQU1nQixjQUFjMUwsSUFBSTBHLGFBQWEsSUFDaENnRSxDQUFBQSxhQUFhLENBQUNBLGFBQWFBLFVBQVM7WUFDekMsSUFBSSxDQUFDeFYsa0JBQWtCc1csVUFBVXhXLEtBQUssR0FBRztnQkFDckNrVyxZQUFZUSxjQUFjRixVQUFVeFcsS0FBSztZQUM3QztZQUNBLElBQUksQ0FBQ0Usa0JBQWtCdVcsVUFBVXpXLEtBQUssR0FBRztnQkFDckN1VyxZQUFZRyxjQUFjRCxVQUFVelcsS0FBSztZQUM3QztRQUNKLE9BQ0s7WUFDRCxNQUFNMlcsWUFBWTNMLElBQUkyRyxXQUFXLElBQUksSUFBSTFSLEtBQUt5VjtZQUM5QyxNQUFNa0Isb0JBQW9CLENBQUNDLE9BQVMsSUFBSTVXLEtBQUssSUFBSUEsT0FBTzZXLFlBQVksS0FBSyxNQUFNRDtZQUMvRSxNQUFNRSxTQUFTL0wsSUFBSWxMLElBQUksSUFBSTtZQUMzQixNQUFNa1gsU0FBU2hNLElBQUlsTCxJQUFJLElBQUk7WUFDM0IsSUFBSWdJLFNBQVMwTyxVQUFVeFcsS0FBSyxLQUFLMFYsWUFBWTtnQkFDekNRLFlBQVlhLFNBQ05ILGtCQUFrQmxCLGNBQWNrQixrQkFBa0JKLFVBQVV4VyxLQUFLLElBQ2pFZ1gsU0FDSXRCLGFBQWFjLFVBQVV4VyxLQUFLLEdBQzVCMlcsWUFBWSxJQUFJMVcsS0FBS3VXLFVBQVV4VyxLQUFLO1lBQ2xEO1lBQ0EsSUFBSThILFNBQVMyTyxVQUFVelcsS0FBSyxLQUFLMFYsWUFBWTtnQkFDekNhLFlBQVlRLFNBQ05ILGtCQUFrQmxCLGNBQWNrQixrQkFBa0JILFVBQVV6VyxLQUFLLElBQ2pFZ1gsU0FDSXRCLGFBQWFlLFVBQVV6VyxLQUFLLEdBQzVCMlcsWUFBWSxJQUFJMVcsS0FBS3dXLFVBQVV6VyxLQUFLO1lBQ2xEO1FBQ0o7UUFDQSxJQUFJa1csYUFBYUssV0FBVztZQUN4Qk4saUJBQWlCLENBQUMsQ0FBQ0MsV0FBV00sVUFBVWhMLE9BQU8sRUFBRWlMLFVBQVVqTCxPQUFPLEVBQUVoSCx1QkFBdUJDLEdBQUcsRUFBRUQsdUJBQXVCRSxHQUFHO1lBQzFILElBQUksQ0FBQ29LLDBCQUEwQjtnQkFDM0J2RCxrQkFBa0JULEtBQUssQ0FBQ2xLLEtBQUssQ0FBQzRLLE9BQU87Z0JBQ3JDLE9BQU9WO1lBQ1g7UUFDSjtJQUNKO0lBQ0EsSUFBSSxDQUFDbkcsYUFBYUMsU0FBUSxLQUN0QixDQUFDa1IsV0FDQWhPLENBQUFBLFNBQVM0TixlQUFnQkQsZ0JBQWdCcFYsTUFBTUMsT0FBTyxDQUFDb1YsV0FBVyxHQUFJO1FBQ3ZFLE1BQU11QixrQkFBa0I1QixtQkFBbUIxUTtRQUMzQyxNQUFNdVMsa0JBQWtCN0IsbUJBQW1CelE7UUFDM0MsTUFBTXNSLFlBQVksQ0FBQ2hXLGtCQUFrQitXLGdCQUFnQmpYLEtBQUssS0FDdEQwVixXQUFXalMsTUFBTSxHQUFHLENBQUN3VCxnQkFBZ0JqWCxLQUFLO1FBQzlDLE1BQU11VyxZQUFZLENBQUNyVyxrQkFBa0JnWCxnQkFBZ0JsWCxLQUFLLEtBQ3REMFYsV0FBV2pTLE1BQU0sR0FBRyxDQUFDeVQsZ0JBQWdCbFgsS0FBSztRQUM5QyxJQUFJa1csYUFBYUssV0FBVztZQUN4Qk4saUJBQWlCQyxXQUFXZSxnQkFBZ0J6TCxPQUFPLEVBQUUwTCxnQkFBZ0IxTCxPQUFPO1lBQzVFLElBQUksQ0FBQ3NELDBCQUEwQjtnQkFDM0J2RCxrQkFBa0JULEtBQUssQ0FBQ2xLLEtBQUssQ0FBQzRLLE9BQU87Z0JBQ3JDLE9BQU9WO1lBQ1g7UUFDSjtJQUNKO0lBQ0EsSUFBSWpHLFdBQVcsQ0FBQ2lSLFdBQVdoTyxTQUFTNE4sYUFBYTtRQUM3QyxNQUFNLEVBQUUxVixPQUFPbVgsWUFBWSxFQUFFM0wsT0FBTyxFQUFFLEdBQUc2SixtQkFBbUJ4UTtRQUM1RCxJQUFJNE4sUUFBUTBFLGlCQUFpQixDQUFDekIsV0FBVzBCLEtBQUssQ0FBQ0QsZUFBZTtZQUMxRHJNLEtBQUssQ0FBQ2xLLEtBQUssR0FBRztnQkFDVmQsTUFBTTBFLHVCQUF1QkssT0FBTztnQkFDcEMyRztnQkFDQVI7Z0JBQ0EsR0FBRytLLGtCQUFrQnZSLHVCQUF1QkssT0FBTyxFQUFFMkcsUUFBUTtZQUNqRTtZQUNBLElBQUksQ0FBQ3NELDBCQUEwQjtnQkFDM0J2RCxrQkFBa0JDO2dCQUNsQixPQUFPVjtZQUNYO1FBQ0o7SUFDSjtJQUNBLElBQUkvRixVQUFVO1FBQ1YsSUFBSTRLLFdBQVc1SyxXQUFXO1lBQ3RCLE1BQU01QixTQUFTLE1BQU00QixTQUFTMlEsWUFBWXpOO1lBQzFDLE1BQU1vUCxnQkFBZ0JsQyxpQkFBaUJoUyxRQUFRd1M7WUFDL0MsSUFBSTBCLGVBQWU7Z0JBQ2Z2TSxLQUFLLENBQUNsSyxLQUFLLEdBQUc7b0JBQ1YsR0FBR3lXLGFBQWE7b0JBQ2hCLEdBQUd0QixrQkFBa0J2Uix1QkFBdUJPLFFBQVEsRUFBRXNTLGNBQWM3TCxPQUFPLENBQUM7Z0JBQ2hGO2dCQUNBLElBQUksQ0FBQ3NELDBCQUEwQjtvQkFDM0J2RCxrQkFBa0I4TCxjQUFjN0wsT0FBTztvQkFDdkMsT0FBT1Y7Z0JBQ1g7WUFDSjtRQUNKLE9BQ0ssSUFBSTFLLFNBQVMyRSxXQUFXO1lBQ3pCLElBQUl1UyxtQkFBbUIsQ0FBQztZQUN4QixJQUFLLE1BQU1wVixPQUFPNkMsU0FBVTtnQkFDeEIsSUFBSSxDQUFDMEssY0FBYzZILHFCQUFxQixDQUFDeEksMEJBQTBCO29CQUMvRDtnQkFDSjtnQkFDQSxNQUFNdUksZ0JBQWdCbEMsaUJBQWlCLE1BQU1wUSxRQUFRLENBQUM3QyxJQUFJLENBQUN3VCxZQUFZek4sYUFBYTBOLFVBQVV6VDtnQkFDOUYsSUFBSW1WLGVBQWU7b0JBQ2ZDLG1CQUFtQjt3QkFDZixHQUFHRCxhQUFhO3dCQUNoQixHQUFHdEIsa0JBQWtCN1QsS0FBS21WLGNBQWM3TCxPQUFPLENBQUM7b0JBQ3BEO29CQUNBRCxrQkFBa0I4TCxjQUFjN0wsT0FBTztvQkFDdkMsSUFBSXNELDBCQUEwQjt3QkFDMUJoRSxLQUFLLENBQUNsSyxLQUFLLEdBQUcwVztvQkFDbEI7Z0JBQ0o7WUFDSjtZQUNBLElBQUksQ0FBQzdILGNBQWM2SCxtQkFBbUI7Z0JBQ2xDeE0sS0FBSyxDQUFDbEssS0FBSyxHQUFHO29CQUNWb0ssS0FBSzJLO29CQUNMLEdBQUcyQixnQkFBZ0I7Z0JBQ3ZCO2dCQUNBLElBQUksQ0FBQ3hJLDBCQUEwQjtvQkFDM0IsT0FBT2hFO2dCQUNYO1lBQ0o7UUFDSjtJQUNKO0lBQ0FTLGtCQUFrQjtJQUNsQixPQUFPVDtBQUNYO0FBRUEsTUFBTXlNLGlCQUFpQjtJQUNuQnhFLE1BQU03TyxnQkFBZ0JHLFFBQVE7SUFDOUJ5USxnQkFBZ0I1USxnQkFBZ0JFLFFBQVE7SUFDeENvVCxrQkFBa0I7QUFDdEI7QUFDQSxTQUFTQyxrQkFBa0JuUyxRQUFRLENBQUMsQ0FBQztJQUNqQyxJQUFJcUcsV0FBVztRQUNYLEdBQUc0TCxjQUFjO1FBQ2pCLEdBQUdqUyxLQUFLO0lBQ1o7SUFDQSxJQUFJd0IsYUFBYTtRQUNiNFEsYUFBYTtRQUNielEsU0FBUztRQUNUMFEsU0FBUztRQUNUelEsV0FBV3lJLFdBQVdoRSxTQUFTNUYsYUFBYTtRQUM1Q3VCLGNBQWM7UUFDZHVOLGFBQWE7UUFDYitDLGNBQWM7UUFDZG5KLG9CQUFvQjtRQUNwQmxILFNBQVM7UUFDVEgsZUFBZSxDQUFDO1FBQ2hCRCxhQUFhLENBQUM7UUFDZEUsa0JBQWtCLENBQUM7UUFDbkJHLFFBQVFtRSxTQUFTbkUsTUFBTSxJQUFJLENBQUM7UUFDNUJkLFVBQVVpRixTQUFTakYsUUFBUSxJQUFJO0lBQ25DO0lBQ0EsSUFBSXlFLFVBQVUsQ0FBQztJQUNmLElBQUluRixpQkFBaUI1RixTQUFTdUwsU0FBUzVGLGFBQWEsS0FBSzNGLFNBQVN1TCxTQUFTOUIsTUFBTSxJQUMzRWpJLFlBQVkrSixTQUFTNUYsYUFBYSxJQUFJNEYsU0FBUzlCLE1BQU0sS0FBSyxDQUFDLElBQzNELENBQUM7SUFDUCxJQUFJQyxjQUFjNkIsU0FBU3pCLGdCQUFnQixHQUNyQyxDQUFDLElBQ0R0SSxZQUFZb0U7SUFDbEIsSUFBSThGLFNBQVM7UUFDVEMsUUFBUTtRQUNSRixPQUFPO1FBQ1AxRCxPQUFPO0lBQ1g7SUFDQSxJQUFJSCxTQUFTO1FBQ1Q2RCxPQUFPLElBQUlnTTtRQUNYblIsVUFBVSxJQUFJbVI7UUFDZEMsU0FBUyxJQUFJRDtRQUNiek4sT0FBTyxJQUFJeU47UUFDWDFQLE9BQU8sSUFBSTBQO0lBQ2Y7SUFDQSxJQUFJRTtJQUNKLElBQUlDLFFBQVE7SUFDWixNQUFNNVIsa0JBQWtCO1FBQ3BCYSxTQUFTO1FBQ1RFLGFBQWE7UUFDYkUsa0JBQWtCO1FBQ2xCRCxlQUFlO1FBQ2ZFLGNBQWM7UUFDZEMsU0FBUztRQUNUQyxRQUFRO0lBQ1o7SUFDQSxJQUFJeVEsMkJBQTJCO1FBQzNCLEdBQUc3UixlQUFlO0lBQ3RCO0lBQ0EsTUFBTWtJLFlBQVk7UUFDZGxFLE9BQU82RTtRQUNQVixPQUFPVTtJQUNYO0lBQ0EsTUFBTWlKLG1DQUFtQ3ZNLFNBQVM0RyxZQUFZLEtBQUtyTyxnQkFBZ0JLLEdBQUc7SUFDdEYsTUFBTTRULFdBQVcsQ0FBQ3hRLFdBQWEsQ0FBQ3lRO1lBQzVCQyxhQUFhTDtZQUNiQSxRQUFRTSxXQUFXM1EsVUFBVXlRO1FBQ2pDO0lBQ0EsTUFBTXhRLFlBQVksT0FBTzJRO1FBQ3JCLElBQUksQ0FBQzVNLFNBQVNqRixRQUFRLElBQ2pCTixDQUFBQSxnQkFBZ0JtQixPQUFPLElBQ3BCMFEseUJBQXlCMVEsT0FBTyxJQUNoQ2dSLGlCQUFnQixHQUFJO1lBQ3hCLE1BQU1oUixVQUFVb0UsU0FBUzZNLFFBQVEsR0FDM0IvSSxjQUFjLENBQUMsTUFBTWdKLFlBQVcsRUFBR2pSLE1BQU0sSUFDekMsTUFBTWtSLHlCQUF5QnZOLFNBQVM7WUFDOUMsSUFBSTVELFlBQVlULFdBQVdTLE9BQU8sRUFBRTtnQkFDaEMrRyxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDakJqSDtnQkFDSjtZQUNKO1FBQ0o7SUFDSjtJQUNBLE1BQU1vUixzQkFBc0IsQ0FBQzNYLE9BQU9zRztRQUNoQyxJQUFJLENBQUNxRSxTQUFTakYsUUFBUSxJQUNqQk4sQ0FBQUEsZ0JBQWdCa0IsWUFBWSxJQUN6QmxCLGdCQUFnQmlCLGdCQUFnQixJQUNoQzRRLHlCQUF5QjNRLFlBQVksSUFDckMyUSx5QkFBeUI1USxnQkFBZ0IsR0FBRztZQUMvQ3JHLENBQUFBLFNBQVNYLE1BQU11WSxJQUFJLENBQUM1USxPQUFPNkQsS0FBSyxHQUFHZ04sT0FBTyxDQUFDLENBQUNqWTtnQkFDekMsSUFBSUEsTUFBTTtvQkFDTjBHLGVBQ01oRSxJQUFJd0QsV0FBV08sZ0JBQWdCLEVBQUV6RyxNQUFNMEcsZ0JBQ3ZDbUosTUFBTTNKLFdBQVdPLGdCQUFnQixFQUFFekc7Z0JBQzdDO1lBQ0o7WUFDQTBOLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQm5ILGtCQUFrQlAsV0FBV08sZ0JBQWdCO2dCQUM3Q0MsY0FBYyxDQUFDbUksY0FBYzNJLFdBQVdPLGdCQUFnQjtZQUM1RDtRQUNKO0lBQ0o7SUFDQSxNQUFNeVIsaUJBQWlCLENBQUNsWSxNQUFNaUosU0FBUyxFQUFFLEVBQUVnRCxRQUFRa00sTUFBTUMsa0JBQWtCLElBQUksRUFBRUMsNkJBQTZCLElBQUk7UUFDOUcsSUFBSUYsUUFBUWxNLFVBQVUsQ0FBQ2xCLFNBQVNqRixRQUFRLEVBQUU7WUFDdENvRixPQUFPQyxNQUFNLEdBQUc7WUFDaEIsSUFBSWtOLDhCQUE4QjVZLE1BQU1DLE9BQU8sQ0FBQ3lDLElBQUlvSSxTQUFTdkssUUFBUTtnQkFDakUsTUFBTXNZLGNBQWNyTSxPQUFPOUosSUFBSW9JLFNBQVN2SyxPQUFPbVksS0FBS0ksSUFBSSxFQUFFSixLQUFLSyxJQUFJO2dCQUNuRUosbUJBQW1CMVYsSUFBSTZILFNBQVN2SyxNQUFNc1k7WUFDMUM7WUFDQSxJQUFJRCw4QkFDQTVZLE1BQU1DLE9BQU8sQ0FBQ3lDLElBQUkrRCxXQUFXVSxNQUFNLEVBQUU1RyxRQUFRO2dCQUM3QyxNQUFNNEcsU0FBU3FGLE9BQU85SixJQUFJK0QsV0FBV1UsTUFBTSxFQUFFNUcsT0FBT21ZLEtBQUtJLElBQUksRUFBRUosS0FBS0ssSUFBSTtnQkFDeEVKLG1CQUFtQjFWLElBQUl3RCxXQUFXVSxNQUFNLEVBQUU1RyxNQUFNNEc7Z0JBQ2hEdU4sZ0JBQWdCak8sV0FBV1UsTUFBTSxFQUFFNUc7WUFDdkM7WUFDQSxJQUFJLENBQUN3RixnQkFBZ0JnQixhQUFhLElBQzlCNlEseUJBQXlCN1EsYUFBYSxLQUN0QzZSLDhCQUNBNVksTUFBTUMsT0FBTyxDQUFDeUMsSUFBSStELFdBQVdNLGFBQWEsRUFBRXhHLFFBQVE7Z0JBQ3BELE1BQU13RyxnQkFBZ0J5RixPQUFPOUosSUFBSStELFdBQVdNLGFBQWEsRUFBRXhHLE9BQU9tWSxLQUFLSSxJQUFJLEVBQUVKLEtBQUtLLElBQUk7Z0JBQ3RGSixtQkFBbUIxVixJQUFJd0QsV0FBV00sYUFBYSxFQUFFeEcsTUFBTXdHO1lBQzNEO1lBQ0EsSUFBSWhCLGdCQUFnQmUsV0FBVyxJQUFJOFEseUJBQXlCOVEsV0FBVyxFQUFFO2dCQUNyRUwsV0FBV0ssV0FBVyxHQUFHK0osZUFBZWxMLGdCQUFnQjhEO1lBQzVEO1lBQ0F3RSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDakI1TjtnQkFDQXFHLFNBQVNvUyxVQUFVelksTUFBTWlKO2dCQUN6QjFDLGFBQWFMLFdBQVdLLFdBQVc7Z0JBQ25DSyxRQUFRVixXQUFXVSxNQUFNO2dCQUN6QkQsU0FBU1QsV0FBV1MsT0FBTztZQUMvQjtRQUNKLE9BQ0s7WUFDRGpFLElBQUl3RyxhQUFhbEosTUFBTWlKO1FBQzNCO0lBQ0o7SUFDQSxNQUFNeVAsZUFBZSxDQUFDMVksTUFBTWtLO1FBQ3hCeEgsSUFBSXdELFdBQVdVLE1BQU0sRUFBRTVHLE1BQU1rSztRQUM3QndELFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCaEgsUUFBUVYsV0FBV1UsTUFBTTtRQUM3QjtJQUNKO0lBQ0EsTUFBTStSLGFBQWEsQ0FBQy9SO1FBQ2hCVixXQUFXVSxNQUFNLEdBQUdBO1FBQ3BCOEcsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakJoSCxRQUFRVixXQUFXVSxNQUFNO1lBQ3pCRCxTQUFTO1FBQ2I7SUFDSjtJQUNBLE1BQU1pUyxzQkFBc0IsQ0FBQzVZLE1BQU02WSxzQkFBc0J6WixRQUFPZ0w7UUFDNUQsTUFBTUUsUUFBUW5JLElBQUlvSSxTQUFTdks7UUFDM0IsSUFBSXNLLE9BQU87WUFDUCxNQUFNaEksZUFBZUgsSUFBSStHLGFBQWFsSixNQUFNeUIsWUFBWXJDLFVBQVMrQyxJQUFJaUQsZ0JBQWdCcEYsUUFBUVo7WUFDN0ZxQyxZQUFZYSxpQkFDUDhILE9BQU9BLElBQUkwTyxjQUFjLElBQzFCRCx1QkFDRW5XLElBQUl3RyxhQUFhbEosTUFBTTZZLHVCQUF1QnZXLGVBQWUrTyxjQUFjL0csTUFBTUUsRUFBRSxLQUNuRnVPLGNBQWMvWSxNQUFNc0M7WUFDMUI0SSxPQUFPRCxLQUFLLElBQUlqRTtRQUNwQjtJQUNKO0lBQ0EsTUFBTWdTLHNCQUFzQixDQUFDaFosTUFBTWlaLFlBQVlqRyxhQUFha0csYUFBYUM7UUFDckUsSUFBSUMsb0JBQW9CO1FBQ3hCLElBQUlDLGtCQUFrQjtRQUN0QixNQUFNM04sU0FBUztZQUNYMUw7UUFDSjtRQUNBLElBQUksQ0FBQytLLFNBQVNqRixRQUFRLEVBQUU7WUFDcEIsSUFBSSxDQUFDa04sZUFBZWtHLGFBQWE7Z0JBQzdCLElBQUkxVCxnQkFBZ0JhLE9BQU8sSUFBSWdSLHlCQUF5QmhSLE9BQU8sRUFBRTtvQkFDN0RnVCxrQkFBa0JuVCxXQUFXRyxPQUFPO29CQUNwQ0gsV0FBV0csT0FBTyxHQUFHcUYsT0FBT3JGLE9BQU8sR0FBR29TO29CQUN0Q1csb0JBQW9CQyxvQkFBb0IzTixPQUFPckYsT0FBTztnQkFDMUQ7Z0JBQ0EsTUFBTWlULHlCQUF5QnpSLFVBQVUxRixJQUFJaUQsZ0JBQWdCcEYsT0FBT2laO2dCQUNwRUksa0JBQWtCLENBQUMsQ0FBQ2xYLElBQUkrRCxXQUFXSyxXQUFXLEVBQUV2RztnQkFDaERzWix5QkFDTXpKLE1BQU0zSixXQUFXSyxXQUFXLEVBQUV2RyxRQUM5QjBDLElBQUl3RCxXQUFXSyxXQUFXLEVBQUV2RyxNQUFNO2dCQUN4QzBMLE9BQU9uRixXQUFXLEdBQUdMLFdBQVdLLFdBQVc7Z0JBQzNDNlMsb0JBQ0lBLHFCQUNLLENBQUM1VCxnQkFBZ0JlLFdBQVcsSUFDekI4USx5QkFBeUI5USxXQUFXLEtBQ3BDOFMsb0JBQW9CLENBQUNDO1lBQ3JDO1lBQ0EsSUFBSXRHLGFBQWE7Z0JBQ2IsTUFBTXVHLHlCQUF5QnBYLElBQUkrRCxXQUFXTSxhQUFhLEVBQUV4RztnQkFDN0QsSUFBSSxDQUFDdVosd0JBQXdCO29CQUN6QjdXLElBQUl3RCxXQUFXTSxhQUFhLEVBQUV4RyxNQUFNZ1Q7b0JBQ3BDdEgsT0FBT2xGLGFBQWEsR0FBR04sV0FBV00sYUFBYTtvQkFDL0M0UyxvQkFDSUEscUJBQ0ssQ0FBQzVULGdCQUFnQmdCLGFBQWEsSUFDM0I2USx5QkFBeUI3USxhQUFhLEtBQ3RDK1MsMkJBQTJCdkc7Z0JBQzNDO1lBQ0o7WUFDQW9HLHFCQUFxQkQsZ0JBQWdCekwsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUNsQztRQUM5RDtRQUNBLE9BQU8wTixvQkFBb0IxTixTQUFTLENBQUM7SUFDekM7SUFDQSxNQUFNOE4sc0JBQXNCLENBQUN4WixNQUFNMkcsU0FBU3VELE9BQU9MO1FBQy9DLE1BQU00UCxxQkFBcUJ0WCxJQUFJK0QsV0FBV1UsTUFBTSxFQUFFNUc7UUFDbEQsTUFBTTJYLG9CQUFvQixDQUFDblMsZ0JBQWdCbUIsT0FBTyxJQUFJMFEseUJBQXlCMVEsT0FBTyxLQUNsRmxFLFVBQVVrRSxZQUNWVCxXQUFXUyxPQUFPLEtBQUtBO1FBQzNCLElBQUlvRSxTQUFTMk8sVUFBVSxJQUFJeFAsT0FBTztZQUM5QmlOLHFCQUFxQkksU0FBUyxJQUFNbUIsYUFBYTFZLE1BQU1rSztZQUN2RGlOLG1CQUFtQnBNLFNBQVMyTyxVQUFVO1FBQzFDLE9BQ0s7WUFDRGpDLGFBQWFMO1lBQ2JELHFCQUFxQjtZQUNyQmpOLFFBQ014SCxJQUFJd0QsV0FBV1UsTUFBTSxFQUFFNUcsTUFBTWtLLFNBQzdCMkYsTUFBTTNKLFdBQVdVLE1BQU0sRUFBRTVHO1FBQ25DO1FBQ0EsSUFBSSxDQUFDa0ssUUFBUSxDQUFDckMsVUFBVTRSLG9CQUFvQnZQLFNBQVN1UCxrQkFBaUIsS0FDbEUsQ0FBQzVLLGNBQWNoRixlQUNmOE4sbUJBQW1CO1lBQ25CLE1BQU1nQyxtQkFBbUI7Z0JBQ3JCLEdBQUc5UCxVQUFVO2dCQUNiLEdBQUk4TixxQkFBcUJsVixVQUFVa0UsV0FBVztvQkFBRUE7Z0JBQVEsSUFBSSxDQUFDLENBQUM7Z0JBQzlEQyxRQUFRVixXQUFXVSxNQUFNO2dCQUN6QjVHO1lBQ0o7WUFDQWtHLGFBQWE7Z0JBQ1QsR0FBR0EsVUFBVTtnQkFDYixHQUFHeVQsZ0JBQWdCO1lBQ3ZCO1lBQ0FqTSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQytMO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNOUIsYUFBYSxPQUFPN1g7UUFDdEIrWCxvQkFBb0IvWCxNQUFNO1FBQzFCLE1BQU11QyxTQUFTLE1BQU13SSxTQUFTNk0sUUFBUSxDQUFDMU8sYUFBYTZCLFNBQVM2TyxPQUFPLEVBQUVuSSxtQkFBbUJ6UixRQUFRb0gsT0FBTzZELEtBQUssRUFBRVYsU0FBU1EsU0FBUzRHLFlBQVksRUFBRTVHLFNBQVM2Ryx5QkFBeUI7UUFDakxtRyxvQkFBb0IvWDtRQUNwQixPQUFPdUM7SUFDWDtJQUNBLE1BQU1zWCw4QkFBOEIsT0FBT3paO1FBQ3ZDLE1BQU0sRUFBRXdHLE1BQU0sRUFBRSxHQUFHLE1BQU1pUixXQUFXelg7UUFDcEMsSUFBSUEsT0FBTztZQUNQLEtBQUssTUFBTUosUUFBUUksTUFBTztnQkFDdEIsTUFBTThKLFFBQVEvSCxJQUFJeUUsUUFBUTVHO2dCQUMxQmtLLFFBQ014SCxJQUFJd0QsV0FBV1UsTUFBTSxFQUFFNUcsTUFBTWtLLFNBQzdCMkYsTUFBTTNKLFdBQVdVLE1BQU0sRUFBRTVHO1lBQ25DO1FBQ0osT0FDSztZQUNEa0csV0FBV1UsTUFBTSxHQUFHQTtRQUN4QjtRQUNBLE9BQU9BO0lBQ1g7SUFDQSxNQUFNa1IsMkJBQTJCLE9BQU81SCxRQUFRNEosc0JBQXNCRixVQUFVO1FBQzVFRyxPQUFPO0lBQ1gsQ0FBQztRQUNHLElBQUssTUFBTS9aLFFBQVFrUSxPQUFRO1lBQ3ZCLE1BQU01RixRQUFRNEYsTUFBTSxDQUFDbFEsS0FBSztZQUMxQixJQUFJc0ssT0FBTztnQkFDUCxNQUFNLEVBQUVFLEVBQUUsRUFBRSxHQUFHeU8sWUFBWSxHQUFHM087Z0JBQzlCLElBQUlFLElBQUk7b0JBQ0osTUFBTXdQLG1CQUFtQjVTLE9BQU9vQyxLQUFLLENBQUNuSixHQUFHLENBQUNtSyxHQUFHeEssSUFBSTtvQkFDakQsTUFBTWlhLG9CQUFvQjNQLE1BQU1FLEVBQUUsSUFBSWtJLHFCQUFxQnBJLE1BQU1FLEVBQUU7b0JBQ25FLElBQUl5UCxxQkFBcUJ6VSxnQkFBZ0JpQixnQkFBZ0IsRUFBRTt3QkFDdkRzUixvQkFBb0I7NEJBQUMvWDt5QkFBSyxFQUFFO29CQUNoQztvQkFDQSxNQUFNa2EsYUFBYSxNQUFNdkYsY0FBY3JLLE9BQU9sRCxPQUFPdEIsUUFBUSxFQUFFb0QsYUFBYW9PLGtDQUFrQ3ZNLFNBQVM2Ryx5QkFBeUIsSUFBSSxDQUFDa0ksc0JBQXNCRTtvQkFDM0ssSUFBSUMscUJBQXFCelUsZ0JBQWdCaUIsZ0JBQWdCLEVBQUU7d0JBQ3ZEc1Isb0JBQW9COzRCQUFDL1g7eUJBQUs7b0JBQzlCO29CQUNBLElBQUlrYSxVQUFVLENBQUMxUCxHQUFHeEssSUFBSSxDQUFDLEVBQUU7d0JBQ3JCNFosUUFBUUcsS0FBSyxHQUFHO3dCQUNoQixJQUFJRCxzQkFBc0I7NEJBQ3RCO3dCQUNKO29CQUNKO29CQUNBLENBQUNBLHdCQUNJM1gsQ0FBQUEsSUFBSStYLFlBQVkxUCxHQUFHeEssSUFBSSxJQUNsQmdhLG1CQUNJNUYsMEJBQTBCbE8sV0FBV1UsTUFBTSxFQUFFc1QsWUFBWTFQLEdBQUd4SyxJQUFJLElBQ2hFMEMsSUFBSXdELFdBQVdVLE1BQU0sRUFBRTRELEdBQUd4SyxJQUFJLEVBQUVrYSxVQUFVLENBQUMxUCxHQUFHeEssSUFBSSxDQUFDLElBQ3ZENlAsTUFBTTNKLFdBQVdVLE1BQU0sRUFBRTRELEdBQUd4SyxJQUFJO2dCQUM5QztnQkFDQSxDQUFDNk8sY0FBY29LLGVBQ1YsTUFBTW5CLHlCQUF5Qm1CLFlBQVlhLHNCQUFzQkY7WUFDMUU7UUFDSjtRQUNBLE9BQU9BLFFBQVFHLEtBQUs7SUFDeEI7SUFDQSxNQUFNM1EsbUJBQW1CO1FBQ3JCLEtBQUssTUFBTXBKLFFBQVFvSCxPQUFPOFAsT0FBTyxDQUFFO1lBQy9CLE1BQU01TSxRQUFRbkksSUFBSW9JLFNBQVN2SztZQUMzQnNLLFNBQ0tBLENBQUFBLE1BQU1FLEVBQUUsQ0FBQytHLElBQUksR0FDUmpILE1BQU1FLEVBQUUsQ0FBQytHLElBQUksQ0FBQ2lELEtBQUssQ0FBQyxDQUFDcEssTUFBUSxDQUFDbUYsS0FBS25GLFFBQ25DLENBQUNtRixLQUFLakYsTUFBTUUsRUFBRSxDQUFDSixHQUFHLE1BQ3hCZ0IsV0FBV3BMO1FBQ25CO1FBQ0FvSCxPQUFPOFAsT0FBTyxHQUFHLElBQUlEO0lBQ3pCO0lBQ0EsTUFBTXdCLFlBQVksQ0FBQ3pZLE1BQU1pQixPQUFTLENBQUM4SixTQUFTakYsUUFBUSxJQUMvQzlGLENBQUFBLFFBQVFpQixRQUFReUIsSUFBSXdHLGFBQWFsSixNQUFNaUIsT0FDcEMsQ0FBQzRHLFVBQVVzUyxhQUFhL1UsZUFBYztJQUM5QyxNQUFNMkQsWUFBWSxDQUFDM0ksT0FBT2tDLGNBQWNnRixXQUFhSCxvQkFBb0IvRyxPQUFPZ0gsUUFBUTtZQUNwRixHQUFJOEQsT0FBT0QsS0FBSyxHQUNWL0IsY0FDQXpILFlBQVlhLGdCQUNSOEMsaUJBQ0E4QixTQUFTOUcsU0FDTDtnQkFBRSxDQUFDQSxNQUFNLEVBQUVrQztZQUFhLElBQ3hCQSxZQUFZO1FBQzlCLEdBQUdnRixVQUFVaEY7SUFDYixNQUFNOFgsaUJBQWlCLENBQUNwYSxPQUFTNEIsUUFBUU8sSUFBSStJLE9BQU9ELEtBQUssR0FBRy9CLGNBQWM5RCxnQkFBZ0JwRixNQUFNK0ssU0FBU3pCLGdCQUFnQixHQUFHbkgsSUFBSWlELGdCQUFnQnBGLE1BQU0sRUFBRSxJQUFJLEVBQUU7SUFDOUosTUFBTStZLGdCQUFnQixDQUFDL1ksTUFBTVosUUFBT3NSLFVBQVUsQ0FBQyxDQUFDO1FBQzVDLE1BQU1wRyxRQUFRbkksSUFBSW9JLFNBQVN2SztRQUMzQixJQUFJaVosYUFBYTdaO1FBQ2pCLElBQUlrTCxPQUFPO1lBQ1AsTUFBTXFJLGlCQUFpQnJJLE1BQU1FLEVBQUU7WUFDL0IsSUFBSW1JLGdCQUFnQjtnQkFDaEIsQ0FBQ0EsZUFBZTdNLFFBQVEsSUFDcEJwRCxJQUFJd0csYUFBYWxKLE1BQU02USxnQkFBZ0J6UixRQUFPdVQ7Z0JBQ2xEc0csYUFDSWpLLGNBQWMyRCxlQUFldkksR0FBRyxLQUFLOUssa0JBQWtCRixVQUNqRCxLQUNBQTtnQkFDVixJQUFJZ1EsaUJBQWlCdUQsZUFBZXZJLEdBQUcsR0FBRztvQkFDdEM7MkJBQUl1SSxlQUFldkksR0FBRyxDQUFDc0csT0FBTztxQkFBQyxDQUFDdUgsT0FBTyxDQUFDLENBQUNvQyxZQUFlQSxVQUFVQyxRQUFRLEdBQUdyQixXQUFXMVEsUUFBUSxDQUFDOFIsVUFBVWpiLEtBQUs7Z0JBQ3BILE9BQ0ssSUFBSXVULGVBQWVwQixJQUFJLEVBQUU7b0JBQzFCLElBQUl2UyxnQkFBZ0IyVCxlQUFldkksR0FBRyxHQUFHO3dCQUNyQ3VJLGVBQWVwQixJQUFJLENBQUMwRyxPQUFPLENBQUMsQ0FBQ3NDOzRCQUN6QixJQUFJLENBQUNBLFlBQVl6QixjQUFjLElBQUksQ0FBQ3lCLFlBQVl6VSxRQUFRLEVBQUU7Z0NBQ3RELElBQUlyRyxNQUFNQyxPQUFPLENBQUN1WixhQUFhO29DQUMzQnNCLFlBQVl6YSxPQUFPLEdBQUcsQ0FBQyxDQUFDbVosV0FBV3JHLElBQUksQ0FBQyxDQUFDM1IsT0FBU0EsU0FBU3NaLFlBQVluYixLQUFLO2dDQUNoRixPQUNLO29DQUNEbWIsWUFBWXphLE9BQU8sR0FDZm1aLGVBQWVzQixZQUFZbmIsS0FBSyxJQUFJLENBQUMsQ0FBQzZaO2dDQUM5Qzs0QkFDSjt3QkFDSjtvQkFDSixPQUNLO3dCQUNEdEcsZUFBZXBCLElBQUksQ0FBQzBHLE9BQU8sQ0FBQyxDQUFDdUMsV0FBY0EsU0FBUzFhLE9BQU8sR0FBRzBhLFNBQVNwYixLQUFLLEtBQUs2WjtvQkFDckY7Z0JBQ0osT0FDSyxJQUFJbkssWUFBWTZELGVBQWV2SSxHQUFHLEdBQUc7b0JBQ3RDdUksZUFBZXZJLEdBQUcsQ0FBQ2hMLEtBQUssR0FBRztnQkFDL0IsT0FDSztvQkFDRHVULGVBQWV2SSxHQUFHLENBQUNoTCxLQUFLLEdBQUc2WjtvQkFDM0IsSUFBSSxDQUFDdEcsZUFBZXZJLEdBQUcsQ0FBQ2xMLElBQUksRUFBRTt3QkFDMUJ3TyxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQzs0QkFDakI1Tjs0QkFDQWlKLFFBQVFqSSxZQUFZa0k7d0JBQ3hCO29CQUNKO2dCQUNKO1lBQ0o7UUFDSjtRQUNDd0gsQ0FBQUEsUUFBUXdJLFdBQVcsSUFBSXhJLFFBQVErSixXQUFXLEtBQ3ZDekIsb0JBQW9CaFosTUFBTWlaLFlBQVl2SSxRQUFRK0osV0FBVyxFQUFFL0osUUFBUXdJLFdBQVcsRUFBRTtRQUNwRnhJLFFBQVFnSyxjQUFjLElBQUlDLFFBQVEzYTtJQUN0QztJQUNBLE1BQU00YSxZQUFZLENBQUM1YSxNQUFNWixRQUFPc1I7UUFDNUIsSUFBSyxNQUFNbUssWUFBWXpiLE9BQU87WUFDMUIsSUFBSSxDQUFDQSxPQUFNdUIsY0FBYyxDQUFDa2EsV0FBVztnQkFDakM7WUFDSjtZQUNBLE1BQU01QixhQUFhN1osTUFBSyxDQUFDeWIsU0FBUztZQUNsQyxNQUFNblQsWUFBWTFILE9BQU8sTUFBTTZhO1lBQy9CLE1BQU12USxRQUFRbkksSUFBSW9JLFNBQVM3QztZQUMxQk4sQ0FBQUEsT0FBT29DLEtBQUssQ0FBQ25KLEdBQUcsQ0FBQ0wsU0FDZFIsU0FBU3laLGVBQ1IzTyxTQUFTLENBQUNBLE1BQU1FLEVBQUUsS0FDbkIsQ0FBQ3JMLGFBQWE4WixjQUNaMkIsVUFBVWxULFdBQVd1UixZQUFZdkksV0FDakNxSSxjQUFjclIsV0FBV3VSLFlBQVl2STtRQUMvQztJQUNKO0lBQ0EsTUFBTW9LLFdBQVcsQ0FBQzlhLE1BQU1aLFFBQU9zUixVQUFVLENBQUMsQ0FBQztRQUN2QyxNQUFNcEcsUUFBUW5JLElBQUlvSSxTQUFTdks7UUFDM0IsTUFBTTZVLGVBQWV6TixPQUFPb0MsS0FBSyxDQUFDbkosR0FBRyxDQUFDTDtRQUN0QyxNQUFNK2EsYUFBYS9aLFlBQVk1QjtRQUMvQnNELElBQUl3RyxhQUFhbEosTUFBTSthO1FBQ3ZCLElBQUlsRyxjQUFjO1lBQ2RuSCxVQUFVbEUsS0FBSyxDQUFDb0UsSUFBSSxDQUFDO2dCQUNqQjVOO2dCQUNBaUosUUFBUWpJLFlBQVlrSTtZQUN4QjtZQUNBLElBQUksQ0FBQzFELGdCQUFnQmEsT0FBTyxJQUN4QmIsZ0JBQWdCZSxXQUFXLElBQzNCOFEseUJBQXlCaFIsT0FBTyxJQUNoQ2dSLHlCQUF5QjlRLFdBQVcsS0FDcENtSyxRQUFRd0ksV0FBVyxFQUFFO2dCQUNyQnhMLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUNqQjVOO29CQUNBdUcsYUFBYStKLGVBQWVsTCxnQkFBZ0I4RDtvQkFDNUM3QyxTQUFTb1MsVUFBVXpZLE1BQU0rYTtnQkFDN0I7WUFDSjtRQUNKLE9BQ0s7WUFDRHpRLFNBQVMsQ0FBQ0EsTUFBTUUsRUFBRSxJQUFJLENBQUNsTCxrQkFBa0J5YixjQUNuQ0gsVUFBVTVhLE1BQU0rYSxZQUFZckssV0FDNUJxSSxjQUFjL1ksTUFBTSthLFlBQVlySztRQUMxQztRQUNBcUMsVUFBVS9TLE1BQU1vSCxXQUFXc0csVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFBRSxHQUFHMUgsVUFBVTtZQUFFbEc7UUFBSztRQUN0RTBOLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCNU4sTUFBTWtMLE9BQU9ELEtBQUssR0FBR2pMLE9BQU8yQjtZQUM1QnNILFFBQVFqSSxZQUFZa0k7UUFDeEI7SUFDSjtJQUNBLE1BQU0xRixXQUFXLE9BQU81RDtRQUNwQnNMLE9BQU9ELEtBQUssR0FBRztRQUNmLE1BQU1wTCxTQUFTRCxNQUFNQyxNQUFNO1FBQzNCLElBQUlHLE9BQU9ILE9BQU9HLElBQUk7UUFDdEIsSUFBSWdiLHNCQUFzQjtRQUMxQixNQUFNMVEsUUFBUW5JLElBQUlvSSxTQUFTdks7UUFDM0IsTUFBTWliLDZCQUE2QixDQUFDaEM7WUFDaEMrQixzQkFDSUUsT0FBT2pZLEtBQUssQ0FBQ2dXLGVBQ1I5WixhQUFhOFosZUFBZWhXLE1BQU1nVyxXQUFXL1EsT0FBTyxPQUNyREwsVUFBVW9SLFlBQVk5VyxJQUFJK0csYUFBYWxKLE1BQU1pWjtRQUN6RDtRQUNBLE1BQU1rQyw2QkFBNkJqSixtQkFBbUJuSCxTQUFTb0gsSUFBSTtRQUNuRSxNQUFNaUosNEJBQTRCbEosbUJBQW1CbkgsU0FBU21KLGNBQWM7UUFDNUUsSUFBSTVKLE9BQU87WUFDUCxJQUFJSjtZQUNKLElBQUl2RDtZQUNKLE1BQU1zUyxhQUFhcFosT0FBT1gsSUFBSSxHQUN4Qm1TLGNBQWMvRyxNQUFNRSxFQUFFLElBQ3RCN0ssY0FBY0M7WUFDcEIsTUFBTW9ULGNBQWNwVCxNQUFNVixJQUFJLEtBQUtnRSxPQUFPQyxJQUFJLElBQUl2RCxNQUFNVixJQUFJLEtBQUtnRSxPQUFPRSxTQUFTO1lBQ2pGLE1BQU1pWSx1QkFBdUIsQ0FBRXZJLGNBQWN4SSxNQUFNRSxFQUFFLEtBQ2pELENBQUNPLFNBQVM2TSxRQUFRLElBQ2xCLENBQUN6VixJQUFJK0QsV0FBV1UsTUFBTSxFQUFFNUcsU0FDeEIsQ0FBQ3NLLE1BQU1FLEVBQUUsQ0FBQzhRLElBQUksSUFDZHRILGVBQWVoQixhQUFhN1EsSUFBSStELFdBQVdNLGFBQWEsRUFBRXhHLE9BQU9rRyxXQUFXK04sV0FBVyxFQUFFbUgsMkJBQTJCRDtZQUN4SCxNQUFNSSxVQUFVeEksVUFBVS9TLE1BQU1vSCxRQUFRNEw7WUFDeEN0USxJQUFJd0csYUFBYWxKLE1BQU1pWjtZQUN2QixJQUFJakcsYUFBYTtnQkFDYjFJLE1BQU1FLEVBQUUsQ0FBQ2pILE1BQU0sSUFBSStHLE1BQU1FLEVBQUUsQ0FBQ2pILE1BQU0sQ0FBQzNEO2dCQUNuQ3VYLHNCQUFzQkEsbUJBQW1CO1lBQzdDLE9BQ0ssSUFBSTdNLE1BQU1FLEVBQUUsQ0FBQ2hILFFBQVEsRUFBRTtnQkFDeEI4RyxNQUFNRSxFQUFFLENBQUNoSCxRQUFRLENBQUM1RDtZQUN0QjtZQUNBLE1BQU1pSyxhQUFhbVAsb0JBQW9CaFosTUFBTWlaLFlBQVlqRztZQUN6RCxNQUFNbUcsZUFBZSxDQUFDdEssY0FBY2hGLGVBQWUwUjtZQUNuRCxDQUFDdkksZUFDR3RGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjVOO2dCQUNBZCxNQUFNVSxNQUFNVixJQUFJO2dCQUNoQitKLFFBQVFqSSxZQUFZa0k7WUFDeEI7WUFDSixJQUFJbVMsc0JBQXNCO2dCQUN0QixJQUFJN1YsZ0JBQWdCbUIsT0FBTyxJQUFJMFEseUJBQXlCMVEsT0FBTyxFQUFFO29CQUM3RCxJQUFJb0UsU0FBU29ILElBQUksS0FBSyxVQUFVO3dCQUM1QixJQUFJYSxhQUFhOzRCQUNiaE07d0JBQ0o7b0JBQ0osT0FDSyxJQUFJLENBQUNnTSxhQUFhO3dCQUNuQmhNO29CQUNKO2dCQUNKO2dCQUNBLE9BQVFtUyxnQkFDSnpMLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUFFNU47b0JBQU0sR0FBSXViLFVBQVUsQ0FBQyxJQUFJMVIsVUFBVTtnQkFBRTtZQUNwRTtZQUNBLENBQUNtSixlQUFldUksV0FBVzdOLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFLEdBQUcxSCxVQUFVO1lBQUM7WUFDaEUsSUFBSTZFLFNBQVM2TSxRQUFRLEVBQUU7Z0JBQ25CLE1BQU0sRUFBRWhSLE1BQU0sRUFBRSxHQUFHLE1BQU1pUixXQUFXO29CQUFDN1g7aUJBQUs7Z0JBQzFDaWIsMkJBQTJCaEM7Z0JBQzNCLElBQUkrQixxQkFBcUI7b0JBQ3JCLE1BQU1RLDRCQUE0QmxJLGtCQUFrQnBOLFdBQVdVLE1BQU0sRUFBRTJELFNBQVN2SztvQkFDaEYsTUFBTXliLG9CQUFvQm5JLGtCQUFrQjFNLFFBQVEyRCxTQUFTaVIsMEJBQTBCeGIsSUFBSSxJQUFJQTtvQkFDL0ZrSyxRQUFRdVIsa0JBQWtCdlIsS0FBSztvQkFDL0JsSyxPQUFPeWIsa0JBQWtCemIsSUFBSTtvQkFDN0IyRyxVQUFVa0ksY0FBY2pJO2dCQUM1QjtZQUNKLE9BQ0s7Z0JBQ0RtUixvQkFBb0I7b0JBQUMvWDtpQkFBSyxFQUFFO2dCQUM1QmtLLFFBQVEsQ0FBQyxNQUFNeUssY0FBY3JLLE9BQU9sRCxPQUFPdEIsUUFBUSxFQUFFb0QsYUFBYW9PLGtDQUFrQ3ZNLFNBQVM2Ryx5QkFBeUIsRUFBRSxDQUFDNVIsS0FBSztnQkFDOUkrWCxvQkFBb0I7b0JBQUMvWDtpQkFBSztnQkFDMUJpYiwyQkFBMkJoQztnQkFDM0IsSUFBSStCLHFCQUFxQjtvQkFDckIsSUFBSTlRLE9BQU87d0JBQ1B2RCxVQUFVO29CQUNkLE9BQ0ssSUFBSW5CLGdCQUFnQm1CLE9BQU8sSUFDNUIwUSx5QkFBeUIxUSxPQUFPLEVBQUU7d0JBQ2xDQSxVQUFVLE1BQU1tUix5QkFBeUJ2TixTQUFTO29CQUN0RDtnQkFDSjtZQUNKO1lBQ0EsSUFBSXlRLHFCQUFxQjtnQkFDckIxUSxNQUFNRSxFQUFFLENBQUM4USxJQUFJLElBQ1RYLFFBQVFyUSxNQUFNRSxFQUFFLENBQUM4USxJQUFJO2dCQUN6QjlCLG9CQUFvQnhaLE1BQU0yRyxTQUFTdUQsT0FBT0w7WUFDOUM7UUFDSjtJQUNKO0lBQ0EsTUFBTTZSLGNBQWMsQ0FBQ3RSLEtBQUs5STtRQUN0QixJQUFJYSxJQUFJK0QsV0FBV1UsTUFBTSxFQUFFdEYsUUFBUThJLElBQUlLLEtBQUssRUFBRTtZQUMxQ0wsSUFBSUssS0FBSztZQUNULE9BQU87UUFDWDtRQUNBO0lBQ0o7SUFDQSxNQUFNa1EsVUFBVSxPQUFPM2EsTUFBTTBRLFVBQVUsQ0FBQyxDQUFDO1FBQ3JDLElBQUkvSjtRQUNKLElBQUkrUDtRQUNKLE1BQU1pRixhQUFhdk4sc0JBQXNCcE87UUFDekMsSUFBSStLLFNBQVM2TSxRQUFRLEVBQUU7WUFDbkIsTUFBTWhSLFNBQVMsTUFBTWlULDRCQUE0QnBZLFlBQVl6QixRQUFRQSxPQUFPMmI7WUFDNUVoVixVQUFVa0ksY0FBY2pJO1lBQ3hCOFAsbUJBQW1CMVcsT0FDYixDQUFDMmIsV0FBV3ZPLElBQUksQ0FBQyxDQUFDcE4sT0FBU21DLElBQUl5RSxRQUFRNUcsU0FDdkMyRztRQUNWLE9BQ0ssSUFBSTNHLE1BQU07WUFDWDBXLG1CQUFtQixDQUFDLE1BQU1rRixRQUFRalksR0FBRyxDQUFDZ1ksV0FBV2xVLEdBQUcsQ0FBQyxPQUFPQztnQkFDeEQsTUFBTTRDLFFBQVFuSSxJQUFJb0ksU0FBUzdDO2dCQUMzQixPQUFPLE1BQU1vUSx5QkFBeUJ4TixTQUFTQSxNQUFNRSxFQUFFLEdBQUc7b0JBQUUsQ0FBQzlDLFVBQVUsRUFBRTRDO2dCQUFNLElBQUlBO1lBQ3ZGLEdBQUUsRUFBR2tLLEtBQUssQ0FBQzFTO1lBQ1gsQ0FBRSxFQUFDNFUsb0JBQW9CLENBQUN4USxXQUFXUyxPQUFPLEtBQUtLO1FBQ25ELE9BQ0s7WUFDRDBQLG1CQUFtQi9QLFVBQVUsTUFBTW1SLHlCQUF5QnZOO1FBQ2hFO1FBQ0FtRCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQixHQUFJLENBQUMxRyxTQUFTbEgsU0FDVCxDQUFDd0YsZ0JBQWdCbUIsT0FBTyxJQUFJMFEseUJBQXlCMVEsT0FBTyxLQUN6REEsWUFBWVQsV0FBV1MsT0FBTyxHQUNoQyxDQUFDLElBQ0Q7Z0JBQUUzRztZQUFLLENBQUM7WUFDZCxHQUFJK0ssU0FBUzZNLFFBQVEsSUFBSSxDQUFDNVgsT0FBTztnQkFBRTJHO1lBQVEsSUFBSSxDQUFDLENBQUM7WUFDakRDLFFBQVFWLFdBQVdVLE1BQU07UUFDN0I7UUFDQThKLFFBQVFtTCxXQUFXLElBQ2YsQ0FBQ25GLG9CQUNEdkQsc0JBQXNCNUksU0FBU21SLGFBQWExYixPQUFPMmIsYUFBYXZVLE9BQU82RCxLQUFLO1FBQ2hGLE9BQU95TDtJQUNYO0lBQ0EsTUFBTXlELFlBQVksQ0FBQ3dCO1FBQ2YsTUFBTTFTLFNBQVM7WUFDWCxHQUFJaUMsT0FBT0QsS0FBSyxHQUFHL0IsY0FBYzlELGNBQWM7UUFDbkQ7UUFDQSxPQUFPM0QsWUFBWWthLGNBQ2IxUyxTQUNBL0IsU0FBU3lVLGNBQ0x4WixJQUFJOEcsUUFBUTBTLGNBQ1pBLFdBQVdsVSxHQUFHLENBQUMsQ0FBQ3pILE9BQVNtQyxJQUFJOEcsUUFBUWpKO0lBQ25EO0lBQ0EsTUFBTThiLGdCQUFnQixDQUFDOWIsTUFBTStFLFlBQWU7WUFDeENnRixTQUFTLENBQUMsQ0FBQzVILElBQUksQ0FBQzRDLGFBQWFtQixVQUFTLEVBQUdVLE1BQU0sRUFBRTVHO1lBQ2pEcUcsU0FBUyxDQUFDLENBQUNsRSxJQUFJLENBQUM0QyxhQUFhbUIsVUFBUyxFQUFHSyxXQUFXLEVBQUV2RztZQUN0RGtLLE9BQU8vSCxJQUFJLENBQUM0QyxhQUFhbUIsVUFBUyxFQUFHVSxNQUFNLEVBQUU1RztZQUM3QzBHLGNBQWMsQ0FBQyxDQUFDdkUsSUFBSStELFdBQVdPLGdCQUFnQixFQUFFekc7WUFDakRpSyxXQUFXLENBQUMsQ0FBQzlILElBQUksQ0FBQzRDLGFBQWFtQixVQUFTLEVBQUdNLGFBQWEsRUFBRXhHO1FBQzlEO0lBQ0EsTUFBTStiLGNBQWMsQ0FBQy9iO1FBQ2pCQSxRQUNJb08sc0JBQXNCcE8sTUFBTWlZLE9BQU8sQ0FBQyxDQUFDK0QsWUFBY25NLE1BQU0zSixXQUFXVSxNQUFNLEVBQUVvVjtRQUNoRnRPLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCaEgsUUFBUTVHLE9BQU9rRyxXQUFXVSxNQUFNLEdBQUcsQ0FBQztRQUN4QztJQUNKO0lBQ0EsTUFBTWtILFdBQVcsQ0FBQzlOLE1BQU1rSyxPQUFPd0c7UUFDM0IsTUFBTXRHLE1BQU0sQ0FBQ2pJLElBQUlvSSxTQUFTdkssTUFBTTtZQUFFd0ssSUFBSSxDQUFDO1FBQUUsR0FBR0EsRUFBRSxJQUFJLENBQUMsR0FBR0osR0FBRztRQUN6RCxNQUFNNlIsZUFBZTlaLElBQUkrRCxXQUFXVSxNQUFNLEVBQUU1RyxTQUFTLENBQUM7UUFDdEQsdUVBQXVFO1FBQ3ZFLE1BQU0sRUFBRW9LLEtBQUs4UixVQUFVLEVBQUV0UixPQUFPLEVBQUUxTCxJQUFJLEVBQUUsR0FBR2lkLGlCQUFpQixHQUFHRjtRQUMvRHZaLElBQUl3RCxXQUFXVSxNQUFNLEVBQUU1RyxNQUFNO1lBQ3pCLEdBQUdtYyxlQUFlO1lBQ2xCLEdBQUdqUyxLQUFLO1lBQ1JFO1FBQ0o7UUFDQXNELFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCNU47WUFDQTRHLFFBQVFWLFdBQVdVLE1BQU07WUFDekJELFNBQVM7UUFDYjtRQUNBK0osV0FBV0EsUUFBUW1MLFdBQVcsSUFBSXpSLE9BQU9BLElBQUlLLEtBQUssSUFBSUwsSUFBSUssS0FBSztJQUNuRTtJQUNBLE1BQU1sRCxRQUFRLENBQUN2SCxNQUFNc0MsZUFBaUJ5TSxXQUFXL08sUUFDM0MwTixVQUFVQyxLQUFLLENBQUNhLFNBQVMsQ0FBQztZQUN4QlosTUFBTSxDQUFDd08sVUFBWSxZQUFZQSxXQUMzQnBjLEtBQUsrSSxVQUFVcEgsV0FBV1csZUFBZThaO1FBQ2pELEtBQ0VyVCxVQUFVL0ksTUFBTXNDLGNBQWM7SUFDcEMsTUFBTXVFLGFBQWEsQ0FBQ25DLFFBQVVnSixVQUFVQyxLQUFLLENBQUNhLFNBQVMsQ0FBQztZQUNwRFosTUFBTSxDQUFDN0k7Z0JBQ0gsSUFBSThPLHNCQUFzQm5QLE1BQU0xRSxJQUFJLEVBQUUrRSxVQUFVL0UsSUFBSSxFQUFFMEUsTUFBTXFCLEtBQUssS0FDN0Q0TixzQkFBc0I1TyxXQUFXTCxNQUFNSyxTQUFTLElBQUlTLGlCQUFpQjZXLGVBQWUzWCxNQUFNNFgsWUFBWSxHQUFHO29CQUN6RzVYLE1BQU1xQyxRQUFRLENBQUM7d0JBQ1hrQyxRQUFROzRCQUFFLEdBQUdDLFdBQVc7d0JBQUM7d0JBQ3pCLEdBQUdoRCxVQUFVO3dCQUNiLEdBQUduQixTQUFTO3dCQUNaSSxlQUFlQztvQkFDbkI7Z0JBQ0o7WUFDSjtRQUNKLEdBQUdzSixXQUFXO0lBQ2QsTUFBTUYsWUFBWSxDQUFDOUo7UUFDZndHLE9BQU9ELEtBQUssR0FBRztRQUNmb00sMkJBQTJCO1lBQ3ZCLEdBQUdBLHdCQUF3QjtZQUMzQixHQUFHM1MsTUFBTUssU0FBUztRQUN0QjtRQUNBLE9BQU84QixXQUFXO1lBQ2QsR0FBR25DLEtBQUs7WUFDUkssV0FBV3NTO1FBQ2Y7SUFDSjtJQUNBLE1BQU1qTSxhQUFhLENBQUNwTCxNQUFNMFEsVUFBVSxDQUFDLENBQUM7UUFDbEMsS0FBSyxNQUFNaEosYUFBYTFILE9BQU9vTyxzQkFBc0JwTyxRQUFRb0gsT0FBTzZELEtBQUssQ0FBRTtZQUN2RTdELE9BQU82RCxLQUFLLENBQUNzUixNQUFNLENBQUM3VTtZQUNwQk4sT0FBT29DLEtBQUssQ0FBQytTLE1BQU0sQ0FBQzdVO1lBQ3BCLElBQUksQ0FBQ2dKLFFBQVE4TCxTQUFTLEVBQUU7Z0JBQ3BCM00sTUFBTXRGLFNBQVM3QztnQkFDZm1JLE1BQU0zRyxhQUFheEI7WUFDdkI7WUFDQSxDQUFDZ0osUUFBUStMLFNBQVMsSUFBSTVNLE1BQU0zSixXQUFXVSxNQUFNLEVBQUVjO1lBQy9DLENBQUNnSixRQUFRZ00sU0FBUyxJQUFJN00sTUFBTTNKLFdBQVdLLFdBQVcsRUFBRW1CO1lBQ3BELENBQUNnSixRQUFRaU0sV0FBVyxJQUFJOU0sTUFBTTNKLFdBQVdNLGFBQWEsRUFBRWtCO1lBQ3hELENBQUNnSixRQUFRa00sZ0JBQWdCLElBQ3JCL00sTUFBTTNKLFdBQVdPLGdCQUFnQixFQUFFaUI7WUFDdkMsQ0FBQ3FELFNBQVN6QixnQkFBZ0IsSUFDdEIsQ0FBQ29ILFFBQVFtTSxnQkFBZ0IsSUFDekJoTixNQUFNekssZ0JBQWdCc0M7UUFDOUI7UUFDQWdHLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCM0UsUUFBUWpJLFlBQVlrSTtRQUN4QjtRQUNBd0UsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakIsR0FBRzFILFVBQVU7WUFDYixHQUFJLENBQUN3SyxRQUFRZ00sU0FBUyxHQUFHLENBQUMsSUFBSTtnQkFBRXJXLFNBQVNvUztZQUFZLENBQUM7UUFDMUQ7UUFDQSxDQUFDL0gsUUFBUW9NLFdBQVcsSUFBSTlWO0lBQzVCO0lBQ0EsTUFBTXFFLG9CQUFvQixDQUFDLEVBQUV2RixRQUFRLEVBQUU5RixJQUFJLEVBQUc7UUFDMUMsSUFBSSxVQUFXOEYsYUFBYW9GLE9BQU9ELEtBQUssSUFDcEMsQ0FBQyxDQUFDbkYsWUFDRnNCLE9BQU90QixRQUFRLENBQUN6RixHQUFHLENBQUNMLE9BQU87WUFDM0I4RixXQUFXc0IsT0FBT3RCLFFBQVEsQ0FBQzBCLEdBQUcsQ0FBQ3hILFFBQVFvSCxPQUFPdEIsUUFBUSxDQUFDeVcsTUFBTSxDQUFDdmM7UUFDbEU7SUFDSjtJQUNBLE1BQU0ySixXQUFXLENBQUMzSixNQUFNMFEsVUFBVSxDQUFDLENBQUM7UUFDaEMsSUFBSXBHLFFBQVFuSSxJQUFJb0ksU0FBU3ZLO1FBQ3pCLE1BQU0rYyxvQkFBb0J0YSxVQUFVaU8sUUFBUTVLLFFBQVEsS0FBS3JELFVBQVVzSSxTQUFTakYsUUFBUTtRQUNwRnBELElBQUk2SCxTQUFTdkssTUFBTTtZQUNmLEdBQUlzSyxTQUFTLENBQUMsQ0FBQztZQUNmRSxJQUFJO2dCQUNBLEdBQUlGLFNBQVNBLE1BQU1FLEVBQUUsR0FBR0YsTUFBTUUsRUFBRSxHQUFHO29CQUFFSixLQUFLO3dCQUFFcEs7b0JBQUs7Z0JBQUUsQ0FBQztnQkFDcERBO2dCQUNBaUwsT0FBTztnQkFDUCxHQUFHeUYsT0FBTztZQUNkO1FBQ0o7UUFDQXRKLE9BQU82RCxLQUFLLENBQUN6RCxHQUFHLENBQUN4SDtRQUNqQixJQUFJc0ssT0FBTztZQUNQZSxrQkFBa0I7Z0JBQ2R2RixVQUFVckQsVUFBVWlPLFFBQVE1SyxRQUFRLElBQzlCNEssUUFBUTVLLFFBQVEsR0FDaEJpRixTQUFTakYsUUFBUTtnQkFDdkI5RjtZQUNKO1FBQ0osT0FDSztZQUNENFksb0JBQW9CNVksTUFBTSxNQUFNMFEsUUFBUXRSLEtBQUs7UUFDakQ7UUFDQSxPQUFPO1lBQ0gsR0FBSTJkLG9CQUNFO2dCQUFFalgsVUFBVTRLLFFBQVE1SyxRQUFRLElBQUlpRixTQUFTakYsUUFBUTtZQUFDLElBQ2xELENBQUMsQ0FBQztZQUNSLEdBQUlpRixTQUFTaVMsV0FBVyxHQUNsQjtnQkFDRTlZLFVBQVUsQ0FBQyxDQUFDd00sUUFBUXhNLFFBQVE7Z0JBQzVCSixLQUFLaU8sYUFBYXJCLFFBQVE1TSxHQUFHO2dCQUM3QkQsS0FBS2tPLGFBQWFyQixRQUFRN00sR0FBRztnQkFDN0JHLFdBQVcrTixhQUFhckIsUUFBUTFNLFNBQVM7Z0JBQ3pDRCxXQUFXZ08sYUFBYXJCLFFBQVEzTSxTQUFTO2dCQUN6Q0UsU0FBUzhOLGFBQWFyQixRQUFRek0sT0FBTztZQUN6QyxJQUNFLENBQUMsQ0FBQztZQUNSakU7WUFDQXdEO1lBQ0FELFFBQVFDO1lBQ1I0RyxLQUFLLENBQUNBO2dCQUNGLElBQUlBLEtBQUs7b0JBQ0xULFNBQVMzSixNQUFNMFE7b0JBQ2ZwRyxRQUFRbkksSUFBSW9JLFNBQVN2SztvQkFDckIsTUFBTWlkLFdBQVd4YixZQUFZMkksSUFBSWhMLEtBQUssSUFDaENnTCxJQUFJOFMsZ0JBQWdCLEdBQ2hCOVMsSUFBSThTLGdCQUFnQixDQUFDLHdCQUF3QixDQUFDLEVBQUUsSUFBSTlTLE1BQ3BEQSxNQUNKQTtvQkFDTixNQUFNK1Msa0JBQWtCN04sa0JBQWtCMk47b0JBQzFDLE1BQU0xTCxPQUFPakgsTUFBTUUsRUFBRSxDQUFDK0csSUFBSSxJQUFJLEVBQUU7b0JBQ2hDLElBQUk0TCxrQkFDRTVMLEtBQUtxQixJQUFJLENBQUMsQ0FBQ2pDLFNBQVdBLFdBQVdzTSxZQUNqQ0EsYUFBYTNTLE1BQU1FLEVBQUUsQ0FBQ0osR0FBRyxFQUFFO3dCQUM3QjtvQkFDSjtvQkFDQTFILElBQUk2SCxTQUFTdkssTUFBTTt3QkFDZndLLElBQUk7NEJBQ0EsR0FBR0YsTUFBTUUsRUFBRTs0QkFDWCxHQUFJMlMsa0JBQ0U7Z0NBQ0U1TCxNQUFNO3VDQUNDQSxLQUFLMVAsTUFBTSxDQUFDME47b0NBQ2YwTjt1Q0FDSXhkLE1BQU1DLE9BQU8sQ0FBQ3lDLElBQUlpRCxnQkFBZ0JwRixTQUFTO3dDQUFDLENBQUM7cUNBQUUsR0FBRyxFQUFFO2lDQUMzRDtnQ0FDRG9LLEtBQUs7b0NBQUVsTCxNQUFNK2QsU0FBUy9kLElBQUk7b0NBQUVjO2dDQUFLOzRCQUNyQyxJQUNFO2dDQUFFb0ssS0FBSzZTOzRCQUFTLENBQUM7d0JBQzNCO29CQUNKO29CQUNBckUsb0JBQW9CNVksTUFBTSxPQUFPMkIsV0FBV3NiO2dCQUNoRCxPQUNLO29CQUNEM1MsUUFBUW5JLElBQUlvSSxTQUFTdkssTUFBTSxDQUFDO29CQUM1QixJQUFJc0ssTUFBTUUsRUFBRSxFQUFFO3dCQUNWRixNQUFNRSxFQUFFLENBQUNTLEtBQUssR0FBRztvQkFDckI7b0JBQ0NGLENBQUFBLFNBQVN6QixnQkFBZ0IsSUFBSW9ILFFBQVFwSCxnQkFBZ0IsS0FDbEQsQ0FBRW5KLENBQUFBLG1CQUFtQmlILE9BQU9vQyxLQUFLLEVBQUV4SixTQUFTa0wsT0FBT0MsTUFBTSxLQUN6RC9ELE9BQU84UCxPQUFPLENBQUMxUCxHQUFHLENBQUN4SDtnQkFDM0I7WUFDSjtRQUNKO0lBQ0o7SUFDQSxNQUFNb2QsY0FBYyxJQUFNclMsU0FBUzZMLGdCQUFnQixJQUMvQ3pELHNCQUFzQjVJLFNBQVNtUixhQUFhdFUsT0FBTzZELEtBQUs7SUFDNUQsTUFBTW9TLGVBQWUsQ0FBQ3ZYO1FBQ2xCLElBQUlyRCxVQUFVcUQsV0FBVztZQUNyQjRILFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFOUg7WUFBUztZQUNoQ3FOLHNCQUFzQjVJLFNBQVMsQ0FBQ0gsS0FBS3BLO2dCQUNqQyxNQUFNcVQsZUFBZWxSLElBQUlvSSxTQUFTdks7Z0JBQ2xDLElBQUlxVCxjQUFjO29CQUNkakosSUFBSXRFLFFBQVEsR0FBR3VOLGFBQWE3SSxFQUFFLENBQUMxRSxRQUFRLElBQUlBO29CQUMzQyxJQUFJckcsTUFBTUMsT0FBTyxDQUFDMlQsYUFBYTdJLEVBQUUsQ0FBQytHLElBQUksR0FBRzt3QkFDckM4QixhQUFhN0ksRUFBRSxDQUFDK0csSUFBSSxDQUFDMEcsT0FBTyxDQUFDLENBQUNsRDs0QkFDMUJBLFNBQVNqUCxRQUFRLEdBQUd1TixhQUFhN0ksRUFBRSxDQUFDMUUsUUFBUSxJQUFJQTt3QkFDcEQ7b0JBQ0o7Z0JBQ0o7WUFDSixHQUFHLEdBQUc7UUFDVjtJQUNKO0lBQ0EsTUFBTTRHLGVBQWUsQ0FBQzRRLFNBQVNDLFlBQWMsT0FBT0M7WUFDaEQsSUFBSUMsZUFBZTliO1lBQ25CLElBQUk2YixHQUFHO2dCQUNIQSxFQUFFRSxjQUFjLElBQUlGLEVBQUVFLGNBQWM7Z0JBQ3BDRixFQUFFRyxPQUFPLElBQ0xILEVBQUVHLE9BQU87WUFDakI7WUFDQSxJQUFJckYsY0FBY3RYLFlBQVlrSTtZQUM5QndFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQm9KLGNBQWM7WUFDbEI7WUFDQSxJQUFJak0sU0FBUzZNLFFBQVEsRUFBRTtnQkFDbkIsTUFBTSxFQUFFaFIsTUFBTSxFQUFFcUMsTUFBTSxFQUFFLEdBQUcsTUFBTTRPO2dCQUNqQzNSLFdBQVdVLE1BQU0sR0FBR0E7Z0JBQ3BCMFIsY0FBY3RYLFlBQVlpSTtZQUM5QixPQUNLO2dCQUNELE1BQU02Tyx5QkFBeUJ2TjtZQUNuQztZQUNBLElBQUluRCxPQUFPdEIsUUFBUSxDQUFDOFgsSUFBSSxFQUFFO2dCQUN0QixLQUFLLE1BQU01ZCxRQUFRb0gsT0FBT3RCLFFBQVEsQ0FBRTtvQkFDaEMrSixNQUFNeUksYUFBYXRZO2dCQUN2QjtZQUNKO1lBQ0E2UCxNQUFNM0osV0FBV1UsTUFBTSxFQUFFO1lBQ3pCLElBQUlpSSxjQUFjM0ksV0FBV1UsTUFBTSxHQUFHO2dCQUNsQzhHLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUNqQmhILFFBQVEsQ0FBQztnQkFDYjtnQkFDQSxJQUFJO29CQUNBLE1BQU0wVyxRQUFRaEYsYUFBYWtGO2dCQUMvQixFQUNBLE9BQU90VCxPQUFPO29CQUNWdVQsZUFBZXZUO2dCQUNuQjtZQUNKLE9BQ0s7Z0JBQ0QsSUFBSXFULFdBQVc7b0JBQ1gsTUFBTUEsVUFBVTt3QkFBRSxHQUFHclgsV0FBV1UsTUFBTTtvQkFBQyxHQUFHNFc7Z0JBQzlDO2dCQUNBSjtnQkFDQTFGLFdBQVcwRjtZQUNmO1lBQ0ExUCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDakJxRyxhQUFhO2dCQUNiK0MsY0FBYztnQkFDZG5KLG9CQUFvQmdCLGNBQWMzSSxXQUFXVSxNQUFNLEtBQUssQ0FBQzZXO2dCQUN6RDNHLGFBQWE1USxXQUFXNFEsV0FBVyxHQUFHO2dCQUN0Q2xRLFFBQVFWLFdBQVdVLE1BQU07WUFDN0I7WUFDQSxJQUFJNlcsY0FBYztnQkFDZCxNQUFNQTtZQUNWO1FBQ0o7SUFDQSxNQUFNSSxhQUFhLENBQUM3ZCxNQUFNMFEsVUFBVSxDQUFDLENBQUM7UUFDbEMsSUFBSXZPLElBQUlvSSxTQUFTdkssT0FBTztZQUNwQixJQUFJeUIsWUFBWWlQLFFBQVFwTyxZQUFZLEdBQUc7Z0JBQ25Dd1ksU0FBUzlhLE1BQU1nQixZQUFZbUIsSUFBSWlELGdCQUFnQnBGO1lBQ25ELE9BQ0s7Z0JBQ0Q4YSxTQUFTOWEsTUFBTTBRLFFBQVFwTyxZQUFZO2dCQUNuQ0ksSUFBSTBDLGdCQUFnQnBGLE1BQU1nQixZQUFZMFAsUUFBUXBPLFlBQVk7WUFDOUQ7WUFDQSxJQUFJLENBQUNvTyxRQUFRaU0sV0FBVyxFQUFFO2dCQUN0QjlNLE1BQU0zSixXQUFXTSxhQUFhLEVBQUV4RztZQUNwQztZQUNBLElBQUksQ0FBQzBRLFFBQVFnTSxTQUFTLEVBQUU7Z0JBQ3BCN00sTUFBTTNKLFdBQVdLLFdBQVcsRUFBRXZHO2dCQUM5QmtHLFdBQVdHLE9BQU8sR0FBR3FLLFFBQVFwTyxZQUFZLEdBQ25DbVcsVUFBVXpZLE1BQU1nQixZQUFZbUIsSUFBSWlELGdCQUFnQnBGLFVBQ2hEeVk7WUFDVjtZQUNBLElBQUksQ0FBQy9ILFFBQVErTCxTQUFTLEVBQUU7Z0JBQ3BCNU0sTUFBTTNKLFdBQVdVLE1BQU0sRUFBRTVHO2dCQUN6QndGLGdCQUFnQm1CLE9BQU8sSUFBSUs7WUFDL0I7WUFDQTBHLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFLEdBQUcxSCxVQUFVO1lBQUM7UUFDekM7SUFDSjtJQUNBLE1BQU00WCxTQUFTLENBQUN6VyxZQUFZMFcsbUJBQW1CLENBQUMsQ0FBQztRQUM3QyxNQUFNQyxnQkFBZ0IzVyxhQUFhckcsWUFBWXFHLGNBQWNqQztRQUM3RCxNQUFNNlkscUJBQXFCamQsWUFBWWdkO1FBQ3ZDLE1BQU1FLHFCQUFxQnJQLGNBQWN4SDtRQUN6QyxNQUFNNEIsU0FBU2lWLHFCQUFxQjlZLGlCQUFpQjZZO1FBQ3JELElBQUksQ0FBQ0YsaUJBQWlCSSxpQkFBaUIsRUFBRTtZQUNyQy9ZLGlCQUFpQjRZO1FBQ3JCO1FBQ0EsSUFBSSxDQUFDRCxpQkFBaUJLLFVBQVUsRUFBRTtZQUM5QixJQUFJTCxpQkFBaUJNLGVBQWUsRUFBRTtnQkFDbEMsTUFBTUMsZ0JBQWdCLElBQUlySCxJQUFJO3VCQUN2QjdQLE9BQU82RCxLQUFLO3VCQUNaNUYsT0FBTytDLElBQUksQ0FBQ2tJLGVBQWVsTCxnQkFBZ0I4RDtpQkFDakQ7Z0JBQ0QsS0FBSyxNQUFNeEIsYUFBYWpJLE1BQU11WSxJQUFJLENBQUNzRyxlQUFnQjtvQkFDL0NuYyxJQUFJK0QsV0FBV0ssV0FBVyxFQUFFbUIsYUFDdEJoRixJQUFJdUcsUUFBUXZCLFdBQVd2RixJQUFJK0csYUFBYXhCLGNBQ3hDb1QsU0FBU3BULFdBQVd2RixJQUFJOEcsUUFBUXZCO2dCQUMxQztZQUNKLE9BQ0s7Z0JBQ0QsSUFBSTlHLFNBQVNhLFlBQVk0RixhQUFhO29CQUNsQyxLQUFLLE1BQU1ySCxRQUFRb0gsT0FBTzZELEtBQUssQ0FBRTt3QkFDN0IsTUFBTVgsUUFBUW5JLElBQUlvSSxTQUFTdks7d0JBQzNCLElBQUlzSyxTQUFTQSxNQUFNRSxFQUFFLEVBQUU7NEJBQ25CLE1BQU1tSSxpQkFBaUJsVCxNQUFNQyxPQUFPLENBQUM0SyxNQUFNRSxFQUFFLENBQUMrRyxJQUFJLElBQzVDakgsTUFBTUUsRUFBRSxDQUFDK0csSUFBSSxDQUFDLEVBQUUsR0FDaEJqSCxNQUFNRSxFQUFFLENBQUNKLEdBQUc7NEJBQ2xCLElBQUk0RSxjQUFjMkQsaUJBQWlCO2dDQUMvQixNQUFNNEwsT0FBTzVMLGVBQWU2TCxPQUFPLENBQUM7Z0NBQ3BDLElBQUlELE1BQU07b0NBQ05BLEtBQUtFLEtBQUs7b0NBQ1Y7Z0NBQ0o7NEJBQ0o7d0JBQ0o7b0JBQ0o7Z0JBQ0o7Z0JBQ0EsSUFBSVYsaUJBQWlCVyxhQUFhLEVBQUU7b0JBQ2hDLEtBQUssTUFBTWhYLGFBQWFOLE9BQU82RCxLQUFLLENBQUU7d0JBQ2xDNlAsU0FBU3BULFdBQVd2RixJQUFJOEcsUUFBUXZCO29CQUNwQztnQkFDSixPQUNLO29CQUNENkMsVUFBVSxDQUFDO2dCQUNmO1lBQ0o7WUFDQXJCLGNBQWM2QixTQUFTekIsZ0JBQWdCLEdBQ2pDeVUsaUJBQWlCSSxpQkFBaUIsR0FDOUJuZCxZQUFZb0Usa0JBQ1osQ0FBQyxJQUNMcEUsWUFBWWlJO1lBQ2xCeUUsVUFBVWxFLEtBQUssQ0FBQ29FLElBQUksQ0FBQztnQkFDakIzRSxRQUFRO29CQUFFLEdBQUdBLE1BQU07Z0JBQUM7WUFDeEI7WUFDQXlFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjNFLFFBQVE7b0JBQUUsR0FBR0EsTUFBTTtnQkFBQztZQUN4QjtRQUNKO1FBQ0E3QixTQUFTO1lBQ0w2RCxPQUFPOFMsaUJBQWlCTSxlQUFlLEdBQUdqWCxPQUFPNkQsS0FBSyxHQUFHLElBQUlnTTtZQUM3REMsU0FBUyxJQUFJRDtZQUNiek4sT0FBTyxJQUFJeU47WUFDWG5SLFVBQVUsSUFBSW1SO1lBQ2QxUCxPQUFPLElBQUkwUDtZQUNYdFAsVUFBVTtZQUNWOEMsT0FBTztRQUNYO1FBQ0FTLE9BQU9ELEtBQUssR0FDUixDQUFDekYsZ0JBQWdCbUIsT0FBTyxJQUNwQixDQUFDLENBQUNvWCxpQkFBaUJqQixXQUFXLElBQzlCLENBQUMsQ0FBQ2lCLGlCQUFpQk0sZUFBZTtRQUMxQ25ULE9BQU8zRCxLQUFLLEdBQUcsQ0FBQyxDQUFDd0QsU0FBU3pCLGdCQUFnQjtRQUMxQ29FLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCa0osYUFBYWlILGlCQUFpQlksZUFBZSxHQUN2Q3pZLFdBQVc0USxXQUFXLEdBQ3RCO1lBQ056USxTQUFTNlgscUJBQ0gsUUFDQUgsaUJBQWlCckIsU0FBUyxHQUN0QnhXLFdBQVdHLE9BQU8sR0FDbEIsQ0FBQyxDQUFFMFgsQ0FBQUEsaUJBQWlCSSxpQkFBaUIsSUFDbkMsQ0FBQ3RXLFVBQVVSLFlBQVlqQyxlQUFjO1lBQ2pENk8sYUFBYThKLGlCQUFpQmEsZUFBZSxHQUN2QzFZLFdBQVcrTixXQUFXLEdBQ3RCO1lBQ04xTixhQUFhMlgscUJBQ1AsQ0FBQyxJQUNESCxpQkFBaUJNLGVBQWUsR0FDNUJOLGlCQUFpQkksaUJBQWlCLElBQUlqVixjQUNsQ29ILGVBQWVsTCxnQkFBZ0I4RCxlQUMvQmhELFdBQVdLLFdBQVcsR0FDMUJ3WCxpQkFBaUJJLGlCQUFpQixJQUFJOVcsYUFDbENpSixlQUFlbEwsZ0JBQWdCaUMsY0FDL0IwVyxpQkFBaUJyQixTQUFTLEdBQ3RCeFcsV0FBV0ssV0FBVyxHQUN0QixDQUFDO1lBQ25CQyxlQUFldVgsaUJBQWlCcEIsV0FBVyxHQUNyQ3pXLFdBQVdNLGFBQWEsR0FDeEIsQ0FBQztZQUNQSSxRQUFRbVgsaUJBQWlCYyxVQUFVLEdBQUczWSxXQUFXVSxNQUFNLEdBQUcsQ0FBQztZQUMzRGlILG9CQUFvQmtRLGlCQUFpQmUsc0JBQXNCLEdBQ3JENVksV0FBVzJILGtCQUFrQixHQUM3QjtZQUNObUosY0FBYztRQUNsQjtJQUNKO0lBQ0EsTUFBTXlILFFBQVEsQ0FBQ3BYLFlBQVkwVyxtQkFBcUJELE9BQU8vTyxXQUFXMUgsY0FDNURBLFdBQVc2QixlQUNYN0IsWUFBWTBXO0lBQ2xCLE1BQU1nQixXQUFXLENBQUMvZSxNQUFNMFEsVUFBVSxDQUFDLENBQUM7UUFDaEMsTUFBTXBHLFFBQVFuSSxJQUFJb0ksU0FBU3ZLO1FBQzNCLE1BQU0yUyxpQkFBaUJySSxTQUFTQSxNQUFNRSxFQUFFO1FBQ3hDLElBQUltSSxnQkFBZ0I7WUFDaEIsTUFBTXNLLFdBQVd0SyxlQUFlcEIsSUFBSSxHQUM5Qm9CLGVBQWVwQixJQUFJLENBQUMsRUFBRSxHQUN0Qm9CLGVBQWV2SSxHQUFHO1lBQ3hCLElBQUk2UyxTQUFTeFMsS0FBSyxFQUFFO2dCQUNoQndTLFNBQVN4UyxLQUFLO2dCQUNkaUcsUUFBUXNPLFlBQVksSUFDaEJqUSxXQUFXa08sU0FBU3ZTLE1BQU0sS0FDMUJ1UyxTQUFTdlMsTUFBTTtZQUN2QjtRQUNKO0lBQ0o7SUFDQSxNQUFNMlIsZ0JBQWdCLENBQUMxQztRQUNuQnpULGFBQWE7WUFDVCxHQUFHQSxVQUFVO1lBQ2IsR0FBR3lULGdCQUFnQjtRQUN2QjtJQUNKO0lBQ0EsTUFBTXNGLHNCQUFzQixJQUFNbFEsV0FBV2hFLFNBQVM1RixhQUFhLEtBQy9ENEYsU0FBUzVGLGFBQWEsR0FBRytaLElBQUksQ0FBQyxDQUFDalc7WUFDM0J3VixNQUFNeFYsUUFBUThCLFNBQVNvVSxZQUFZO1lBQ25DelIsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCdEgsV0FBVztZQUNmO1FBQ0o7SUFDSixNQUFNVCxVQUFVO1FBQ1piLFNBQVM7WUFDTDJFO1lBQ0F5QjtZQUNBMFE7WUFDQXBQO1lBQ0FvQjtZQUNBakg7WUFDQWdSO1lBQ0F1RjtZQUNBclU7WUFDQTBQO1lBQ0F6UjtZQUNBa1I7WUFDQTdNO1lBQ0FzTjtZQUNBeUI7WUFDQTBEO1lBQ0FtQjtZQUNBN1Y7WUFDQWlVO1lBQ0EzUDtZQUNBbEk7WUFDQSxJQUFJK0UsV0FBVTtnQkFDVixPQUFPQTtZQUNYO1lBQ0EsSUFBSXJCLGVBQWM7Z0JBQ2QsT0FBT0E7WUFDWDtZQUNBLElBQUlnQyxVQUFTO2dCQUNULE9BQU9BO1lBQ1g7WUFDQSxJQUFJQSxRQUFPOUwsTUFBTztnQkFDZDhMLFNBQVM5TDtZQUNiO1lBQ0EsSUFBSWdHLGtCQUFpQjtnQkFDakIsT0FBT0E7WUFDWDtZQUNBLElBQUlnQyxVQUFTO2dCQUNULE9BQU9BO1lBQ1g7WUFDQSxJQUFJQSxRQUFPaEksTUFBTztnQkFDZGdJLFNBQVNoSTtZQUNiO1lBQ0EsSUFBSThHLGNBQWE7Z0JBQ2IsT0FBT0E7WUFDWDtZQUNBLElBQUk2RSxZQUFXO2dCQUNYLE9BQU9BO1lBQ1g7WUFDQSxJQUFJQSxVQUFTM0wsTUFBTztnQkFDaEIyTCxXQUFXO29CQUNQLEdBQUdBLFFBQVE7b0JBQ1gsR0FBRzNMLEtBQUs7Z0JBQ1o7WUFDSjtRQUNKO1FBQ0FvUDtRQUNBbU07UUFDQWhSO1FBQ0ErQztRQUNBbkY7UUFDQXVUO1FBQ0FYO1FBQ0FzRTtRQUNBWjtRQUNBOUI7UUFDQTNRO1FBQ0EwQztRQUNBaVI7UUFDQWpEO0lBQ0o7SUFDQSxPQUFPO1FBQ0gsR0FBR2pXLE9BQU87UUFDVnVaLGFBQWF2WjtJQUNqQjtBQUNKO0FBRUEsSUFBSXdaLGFBQWE7SUFDYixJQUFJLE9BQU9DLFdBQVcsZUFBZUEsT0FBT0MsVUFBVSxFQUFFO1FBQ3BELE9BQU9ELE9BQU9DLFVBQVU7SUFDNUI7SUFDQSxNQUFNQyxJQUFJLE9BQU9DLGdCQUFnQixjQUFjcGdCLEtBQUtxZ0IsR0FBRyxLQUFLRCxZQUFZQyxHQUFHLEtBQUs7SUFDaEYsT0FBTyx1Q0FBdUN6ZCxPQUFPLENBQUMsU0FBUyxDQUFDMGQ7UUFDNUQsTUFBTUMsSUFBSSxDQUFDQyxLQUFLQyxNQUFNLEtBQUssS0FBS04sQ0FBQUEsSUFBSyxLQUFLO1FBQzFDLE9BQU8sQ0FBQ0csS0FBSyxNQUFNQyxJQUFJLElBQUssTUFBTyxHQUFFLEVBQUdHLFFBQVEsQ0FBQztJQUNyRDtBQUNKO0FBRUEsSUFBSUMsb0JBQW9CLENBQUNoZ0IsTUFBTTJDLE9BQU8rTixVQUFVLENBQUMsQ0FBQyxHQUFLQSxRQUFRbUwsV0FBVyxJQUFJcGEsWUFBWWlQLFFBQVFtTCxXQUFXLElBQ3ZHbkwsUUFBUXVQLFNBQVMsSUFDZixDQUFDLEVBQUVqZ0IsS0FBSyxDQUFDLEVBQUV5QixZQUFZaVAsUUFBUXdQLFVBQVUsSUFBSXZkLFFBQVErTixRQUFRd1AsVUFBVSxDQUFDLENBQUMsQ0FBQyxHQUM1RTtBQUVOLElBQUlDLFdBQVcsQ0FBQ2xmLE1BQU03QixTQUFVO1dBQ3pCNkI7V0FDQW1OLHNCQUFzQmhQO0tBQzVCO0FBRUQsSUFBSWdoQixpQkFBaUIsQ0FBQ2hoQixTQUFVSyxNQUFNQyxPQUFPLENBQUNOLFVBQVNBLE9BQU1xSSxHQUFHLENBQUMsSUFBTTlGLGFBQWFBO0FBRXBGLFNBQVMwZSxPQUFPcGYsSUFBSSxFQUFFMEIsS0FBSyxFQUFFdkQsTUFBSztJQUM5QixPQUFPO1dBQ0E2QixLQUFLME8sS0FBSyxDQUFDLEdBQUdoTjtXQUNkeUwsc0JBQXNCaFA7V0FDdEI2QixLQUFLME8sS0FBSyxDQUFDaE47S0FDakI7QUFDTDtBQUVBLElBQUkyZCxjQUFjLENBQUNyZixNQUFNK1csTUFBTXVJO0lBQzNCLElBQUksQ0FBQzlnQixNQUFNQyxPQUFPLENBQUN1QixPQUFPO1FBQ3RCLE9BQU8sRUFBRTtJQUNiO0lBQ0EsSUFBSVEsWUFBWVIsSUFBSSxDQUFDc2YsR0FBRyxHQUFHO1FBQ3ZCdGYsSUFBSSxDQUFDc2YsR0FBRyxHQUFHNWU7SUFDZjtJQUNBVixLQUFLdWYsTUFBTSxDQUFDRCxJQUFJLEdBQUd0ZixLQUFLdWYsTUFBTSxDQUFDeEksTUFBTSxFQUFFLENBQUMsRUFBRTtJQUMxQyxPQUFPL1c7QUFDWDtBQUVBLElBQUl3ZixZQUFZLENBQUN4ZixNQUFNN0IsU0FBVTtXQUMxQmdQLHNCQUFzQmhQO1dBQ3RCZ1Asc0JBQXNCbk47S0FDNUI7QUFFRCxTQUFTeWYsZ0JBQWdCemYsSUFBSSxFQUFFMGYsT0FBTztJQUNsQyxJQUFJQyxJQUFJO0lBQ1IsTUFBTUMsT0FBTztXQUFJNWY7S0FBSztJQUN0QixLQUFLLE1BQU0wQixTQUFTZ2UsUUFBUztRQUN6QkUsS0FBS0wsTUFBTSxDQUFDN2QsUUFBUWllLEdBQUc7UUFDdkJBO0lBQ0o7SUFDQSxPQUFPaGYsUUFBUWlmLE1BQU1oZSxNQUFNLEdBQUdnZSxPQUFPLEVBQUU7QUFDM0M7QUFDQSxJQUFJQyxnQkFBZ0IsQ0FBQzdmLE1BQU0wQixRQUFVbEIsWUFBWWtCLFNBQzNDLEVBQUUsR0FDRitkLGdCQUFnQnpmLE1BQU1tTixzQkFBc0J6TCxPQUFPb2UsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDO0FBRTVFLElBQUlDLGNBQWMsQ0FBQ2pnQixNQUFNa2dCLFFBQVFDO0lBQzdCLENBQUNuZ0IsSUFBSSxDQUFDa2dCLE9BQU8sRUFBRWxnQixJQUFJLENBQUNtZ0IsT0FBTyxDQUFDLEdBQUc7UUFBQ25nQixJQUFJLENBQUNtZ0IsT0FBTztRQUFFbmdCLElBQUksQ0FBQ2tnQixPQUFPO0tBQUM7QUFDL0Q7QUFFQSxJQUFJRSxXQUFXLENBQUMvSSxhQUFhM1YsT0FBT3ZEO0lBQ2hDa1osV0FBVyxDQUFDM1YsTUFBTSxHQUFHdkQ7SUFDckIsT0FBT2taO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBb0NDLEdBQ0QsU0FBU2dKLGNBQWM1YyxLQUFLO0lBQ3hCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFUyxVQUFVYSxRQUFRYixPQUFPLEVBQUVoRixJQUFJLEVBQUV1aEIsVUFBVSxJQUFJLEVBQUVqWSxnQkFBZ0IsRUFBRU0sS0FBSyxFQUFHLEdBQUdsRjtJQUN0RixNQUFNLENBQUN3TCxRQUFRc1IsVUFBVSxHQUFHemlCLDJDQUFjLENBQUNpRyxRQUFRb1YsY0FBYyxDQUFDcGE7SUFDbEUsTUFBTXloQixNQUFNMWlCLHlDQUFZLENBQUNpRyxRQUFRb1YsY0FBYyxDQUFDcGEsTUFBTXlILEdBQUcsQ0FBQzRYO0lBQzFELE1BQU1xQyxZQUFZM2lCLHlDQUFZLENBQUNtUjtJQUMvQixNQUFNeVIsWUFBWTVpQix5Q0FBWSxDQUFDO0lBQy9CMmlCLFVBQVU1YSxPQUFPLEdBQUdvSjtJQUNwQmxMLFFBQVFvQyxNQUFNLENBQUNvQyxLQUFLLENBQUNoQyxHQUFHLENBQUN4SDtJQUN6QmpCLDBDQUFhLENBQUMsSUFBTTZLLFNBQ2hCNUUsUUFBUTJFLFFBQVEsQ0FBQzNKLE1BQU00SixRQUFRO1FBQUM1RTtRQUFTNEU7UUFBTzVKO0tBQUs7SUFDekR5RiwwQkFBMEIsSUFBTVQsUUFBUTBJLFNBQVMsQ0FBQ2xFLEtBQUssQ0FBQ2dGLFNBQVMsQ0FBQztZQUM5RFosTUFBTSxDQUFDLEVBQUUzRSxNQUFNLEVBQUVqSixNQUFNNGhCLGNBQWMsRUFBRztnQkFDcEMsSUFBSUEsbUJBQW1CNWhCLFFBQVEsQ0FBQzRoQixnQkFBZ0I7b0JBQzVDLE1BQU10SixjQUFjblcsSUFBSThHLFFBQVFqSjtvQkFDaEMsSUFBSVAsTUFBTUMsT0FBTyxDQUFDNFksY0FBYzt3QkFDNUJrSixVQUFVbEo7d0JBQ1ZtSixJQUFJM2EsT0FBTyxHQUFHd1IsWUFBWTdRLEdBQUcsQ0FBQzRYO29CQUNsQztnQkFDSjtZQUNKO1FBQ0osR0FBRzNRLFdBQVcsRUFBRTtRQUFDMUo7UUFBU2hGO0tBQUs7SUFDL0IsTUFBTTZoQixlQUFlOWlCLDhDQUFpQixDQUFDLENBQUMraUI7UUFDcENILFVBQVU3YSxPQUFPLEdBQUc7UUFDcEI5QixRQUFRa1QsY0FBYyxDQUFDbFksTUFBTThoQjtJQUNqQyxHQUFHO1FBQUM5YztRQUFTaEY7S0FBSztJQUNsQixNQUFNa04sU0FBUyxDQUFDOU4sUUFBT3NSO1FBQ25CLE1BQU1xUixjQUFjM1Qsc0JBQXNCcE4sWUFBWTVCO1FBQ3RELE1BQU0waUIsMEJBQTBCM0IsU0FBU25iLFFBQVFvVixjQUFjLENBQUNwYSxPQUFPK2hCO1FBQ3ZFL2MsUUFBUW9DLE1BQU0sQ0FBQ3FELEtBQUssR0FBR3VWLGtCQUFrQmhnQixNQUFNOGhCLHdCQUF3QmpmLE1BQU0sR0FBRyxHQUFHNk47UUFDbkYrUSxJQUFJM2EsT0FBTyxHQUFHcVosU0FBU3NCLElBQUkzYSxPQUFPLEVBQUVpYixZQUFZdGEsR0FBRyxDQUFDNFg7UUFDcER3QyxhQUFhQztRQUNiTixVQUFVTTtRQUNWOWMsUUFBUWtULGNBQWMsQ0FBQ2xZLE1BQU04aEIseUJBQXlCM0IsVUFBVTtZQUM1RDVILE1BQU02SCxlQUFlaGhCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNNGlCLFVBQVUsQ0FBQzVpQixRQUFPc1I7UUFDcEIsTUFBTXVSLGVBQWU3VCxzQkFBc0JwTixZQUFZNUI7UUFDdkQsTUFBTTBpQiwwQkFBMEJyQixVQUFVemIsUUFBUW9WLGNBQWMsQ0FBQ3BhLE9BQU9paUI7UUFDeEVqZCxRQUFRb0MsTUFBTSxDQUFDcUQsS0FBSyxHQUFHdVYsa0JBQWtCaGdCLE1BQU0sR0FBRzBRO1FBQ2xEK1EsSUFBSTNhLE9BQU8sR0FBRzJaLFVBQVVnQixJQUFJM2EsT0FBTyxFQUFFbWIsYUFBYXhhLEdBQUcsQ0FBQzRYO1FBQ3REd0MsYUFBYUM7UUFDYk4sVUFBVU07UUFDVjljLFFBQVFrVCxjQUFjLENBQUNsWSxNQUFNOGhCLHlCQUF5QnJCLFdBQVc7WUFDN0RsSSxNQUFNNkgsZUFBZWhoQjtRQUN6QjtJQUNKO0lBQ0EsTUFBTThpQixTQUFTLENBQUN2ZjtRQUNaLE1BQU1tZiwwQkFBMEJoQixjQUFjOWIsUUFBUW9WLGNBQWMsQ0FBQ3BhLE9BQU8yQztRQUM1RThlLElBQUkzYSxPQUFPLEdBQUdnYSxjQUFjVyxJQUFJM2EsT0FBTyxFQUFFbkU7UUFDekNrZixhQUFhQztRQUNiTixVQUFVTTtRQUNWLENBQUNyaUIsTUFBTUMsT0FBTyxDQUFDeUMsSUFBSTZDLFFBQVF1RixPQUFPLEVBQUV2SyxVQUNoQzBDLElBQUlzQyxRQUFRdUYsT0FBTyxFQUFFdkssTUFBTTJCO1FBQy9CcUQsUUFBUWtULGNBQWMsQ0FBQ2xZLE1BQU04aEIseUJBQXlCaEIsZUFBZTtZQUNqRXZJLE1BQU01VjtRQUNWO0lBQ0o7SUFDQSxNQUFNd2YsV0FBVyxDQUFDeGYsT0FBT3ZELFFBQU9zUjtRQUM1QixNQUFNMFIsY0FBY2hVLHNCQUFzQnBOLFlBQVk1QjtRQUN0RCxNQUFNMGlCLDBCQUEwQnpCLE9BQU9yYixRQUFRb1YsY0FBYyxDQUFDcGEsT0FBTzJDLE9BQU95ZjtRQUM1RXBkLFFBQVFvQyxNQUFNLENBQUNxRCxLQUFLLEdBQUd1VixrQkFBa0JoZ0IsTUFBTTJDLE9BQU8rTjtRQUN0RCtRLElBQUkzYSxPQUFPLEdBQUd1WixPQUFPb0IsSUFBSTNhLE9BQU8sRUFBRW5FLE9BQU95ZixZQUFZM2EsR0FBRyxDQUFDNFg7UUFDekR3QyxhQUFhQztRQUNiTixVQUFVTTtRQUNWOWMsUUFBUWtULGNBQWMsQ0FBQ2xZLE1BQU04aEIseUJBQXlCekIsUUFBUTtZQUMxRDlILE1BQU01VjtZQUNONlYsTUFBTTRILGVBQWVoaEI7UUFDekI7SUFDSjtJQUNBLE1BQU1pakIsT0FBTyxDQUFDbEIsUUFBUUM7UUFDbEIsTUFBTVUsMEJBQTBCOWMsUUFBUW9WLGNBQWMsQ0FBQ3BhO1FBQ3ZEa2hCLFlBQVlZLHlCQUF5QlgsUUFBUUM7UUFDN0NGLFlBQVlPLElBQUkzYSxPQUFPLEVBQUVxYSxRQUFRQztRQUNqQ1MsYUFBYUM7UUFDYk4sVUFBVU07UUFDVjljLFFBQVFrVCxjQUFjLENBQUNsWSxNQUFNOGhCLHlCQUF5QlosYUFBYTtZQUMvRDNJLE1BQU00STtZQUNOM0ksTUFBTTRJO1FBQ1YsR0FBRztJQUNQO0lBQ0EsTUFBTWtCLE9BQU8sQ0FBQ3RLLE1BQU11STtRQUNoQixNQUFNdUIsMEJBQTBCOWMsUUFBUW9WLGNBQWMsQ0FBQ3BhO1FBQ3ZEc2dCLFlBQVl3Qix5QkFBeUI5SixNQUFNdUk7UUFDM0NELFlBQVltQixJQUFJM2EsT0FBTyxFQUFFa1IsTUFBTXVJO1FBQy9Cc0IsYUFBYUM7UUFDYk4sVUFBVU07UUFDVjljLFFBQVFrVCxjQUFjLENBQUNsWSxNQUFNOGhCLHlCQUF5QnhCLGFBQWE7WUFDL0QvSCxNQUFNUDtZQUNOUSxNQUFNK0g7UUFDVixHQUFHO0lBQ1A7SUFDQSxNQUFNZ0MsU0FBUyxDQUFDNWYsT0FBT3ZEO1FBQ25CLE1BQU00SixjQUFjaEksWUFBWTVCO1FBQ2hDLE1BQU0waUIsMEJBQTBCVCxTQUFTcmMsUUFBUW9WLGNBQWMsQ0FBQ3BhLE9BQU8yQyxPQUFPcUc7UUFDOUV5WSxJQUFJM2EsT0FBTyxHQUFHO2VBQUlnYjtTQUF3QixDQUFDcmEsR0FBRyxDQUFDLENBQUMrYSxNQUFNNUIsSUFBTSxDQUFDNEIsUUFBUTVCLE1BQU1qZSxRQUFRMGMsZUFBZW9DLElBQUkzYSxPQUFPLENBQUM4WixFQUFFO1FBQ2hIaUIsYUFBYUM7UUFDYk4sVUFBVTtlQUFJTTtTQUF3QjtRQUN0QzljLFFBQVFrVCxjQUFjLENBQUNsWSxNQUFNOGhCLHlCQUF5QlQsVUFBVTtZQUM1RDlJLE1BQU01VjtZQUNONlYsTUFBTXhQO1FBQ1YsR0FBRyxNQUFNO0lBQ2I7SUFDQSxNQUFNL0csVUFBVSxDQUFDN0M7UUFDYixNQUFNMGlCLDBCQUEwQjFULHNCQUFzQnBOLFlBQVk1QjtRQUNsRXFpQixJQUFJM2EsT0FBTyxHQUFHZ2Isd0JBQXdCcmEsR0FBRyxDQUFDNFg7UUFDMUN3QyxhQUFhO2VBQUlDO1NBQXdCO1FBQ3pDTixVQUFVO2VBQUlNO1NBQXdCO1FBQ3RDOWMsUUFBUWtULGNBQWMsQ0FBQ2xZLE1BQU07ZUFBSThoQjtTQUF3QixFQUFFLENBQUM3Z0IsT0FBU0EsTUFBTSxDQUFDLEdBQUcsTUFBTTtJQUN6RjtJQUNBbEMsNENBQWUsQ0FBQztRQUNaaUcsUUFBUWtHLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHO1FBQ3hCNEgsVUFBVS9TLE1BQU1nRixRQUFRb0MsTUFBTSxLQUMxQnBDLFFBQVEwSSxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ3pCLEdBQUc1SSxRQUFRa0IsVUFBVTtRQUN6QjtRQUNKLElBQUl5YixVQUFVN2EsT0FBTyxJQUNoQixFQUFDb0wsbUJBQW1CbE4sUUFBUStGLFFBQVEsQ0FBQ29ILElBQUksRUFBRUMsVUFBVSxJQUNsRHBOLFFBQVFrQixVQUFVLENBQUMrTixXQUFXLEtBQ2xDLENBQUMvQixtQkFBbUJsTixRQUFRK0YsUUFBUSxDQUFDbUosY0FBYyxFQUFFOUIsVUFBVSxFQUFFO1lBQ2pFLElBQUlwTixRQUFRK0YsUUFBUSxDQUFDNk0sUUFBUSxFQUFFO2dCQUMzQjVTLFFBQVE2UyxVQUFVLENBQUM7b0JBQUM3WDtpQkFBSyxFQUFFa2YsSUFBSSxDQUFDLENBQUMzYztvQkFDN0IsTUFBTTJILFFBQVEvSCxJQUFJSSxPQUFPcUUsTUFBTSxFQUFFNUc7b0JBQ2pDLE1BQU15aUIsZ0JBQWdCdGdCLElBQUk2QyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU1RztvQkFDckQsSUFBSXlpQixnQkFDRSxDQUFFdlksU0FBU3VZLGNBQWN2akIsSUFBSSxJQUMxQmdMLFNBQ0l1WSxDQUFBQSxjQUFjdmpCLElBQUksS0FBS2dMLE1BQU1oTCxJQUFJLElBQzlCdWpCLGNBQWM3WCxPQUFPLEtBQUtWLE1BQU1VLE9BQU8sSUFDakRWLFNBQVNBLE1BQU1oTCxJQUFJLEVBQUU7d0JBQ3ZCZ0wsUUFDTXhILElBQUlzQyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU1RyxNQUFNa0ssU0FDckMyRixNQUFNN0ssUUFBUWtCLFVBQVUsQ0FBQ1UsTUFBTSxFQUFFNUc7d0JBQ3ZDZ0YsUUFBUTBJLFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7NEJBQ3pCaEgsUUFBUTVCLFFBQVFrQixVQUFVLENBQUNVLE1BQU07d0JBQ3JDO29CQUNKO2dCQUNKO1lBQ0osT0FDSztnQkFDRCxNQUFNMEQsUUFBUW5JLElBQUk2QyxRQUFRdUYsT0FBTyxFQUFFdks7Z0JBQ25DLElBQUlzSyxTQUNBQSxNQUFNRSxFQUFFLElBQ1IsQ0FBRTBILENBQUFBLG1CQUFtQmxOLFFBQVErRixRQUFRLENBQUNtSixjQUFjLEVBQUU5QixVQUFVLElBQzVERixtQkFBbUJsTixRQUFRK0YsUUFBUSxDQUFDb0gsSUFBSSxFQUFFQyxVQUFVLEdBQUc7b0JBQzNEdUMsY0FBY3JLLE9BQU90RixRQUFRb0MsTUFBTSxDQUFDdEIsUUFBUSxFQUFFZCxRQUFRa0UsV0FBVyxFQUFFbEUsUUFBUStGLFFBQVEsQ0FBQzRHLFlBQVksS0FBS3JPLGdCQUFnQkssR0FBRyxFQUFFcUIsUUFBUStGLFFBQVEsQ0FBQzZHLHlCQUF5QixFQUFFLE1BQU1zTixJQUFJLENBQUMsQ0FBQ2hWLFFBQVUsQ0FBQzJFLGNBQWMzRSxVQUN2TWxGLFFBQVEwSSxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDOzRCQUN6QmhILFFBQVF3TiwwQkFBMEJwUCxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUVzRCxPQUFPbEs7d0JBQ3hFO2dCQUNSO1lBQ0o7UUFDSjtRQUNBZ0YsUUFBUTBJLFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDekI1TjtZQUNBaUosUUFBUWpJLFlBQVlnRSxRQUFRa0UsV0FBVztRQUMzQztRQUNBbEUsUUFBUW9DLE1BQU0sQ0FBQ3FELEtBQUssSUFDaEIwSSxzQkFBc0JuTyxRQUFRdUYsT0FBTyxFQUFFLENBQUNILEtBQUs5STtZQUN6QyxJQUFJMEQsUUFBUW9DLE1BQU0sQ0FBQ3FELEtBQUssSUFDcEJuSixJQUFJNFIsVUFBVSxDQUFDbE8sUUFBUW9DLE1BQU0sQ0FBQ3FELEtBQUssS0FDbkNMLElBQUlLLEtBQUssRUFBRTtnQkFDWEwsSUFBSUssS0FBSztnQkFDVCxPQUFPO1lBQ1g7WUFDQTtRQUNKO1FBQ0p6RixRQUFRb0MsTUFBTSxDQUFDcUQsS0FBSyxHQUFHO1FBQ3ZCekYsUUFBUWdDLFNBQVM7UUFDakIyYSxVQUFVN2EsT0FBTyxHQUFHO0lBQ3hCLEdBQUc7UUFBQ29KO1FBQVFsUTtRQUFNZ0Y7S0FBUTtJQUMxQmpHLDRDQUFlLENBQUM7UUFDWixDQUFDb0QsSUFBSTZDLFFBQVFrRSxXQUFXLEVBQUVsSixTQUFTZ0YsUUFBUWtULGNBQWMsQ0FBQ2xZO1FBQzFELE9BQU87WUFDSCxNQUFNZ0wsZ0JBQWdCLENBQUNoTCxNQUFNWjtnQkFDekIsTUFBTWtMLFFBQVFuSSxJQUFJNkMsUUFBUXVGLE9BQU8sRUFBRXZLO2dCQUNuQyxJQUFJc0ssU0FBU0EsTUFBTUUsRUFBRSxFQUFFO29CQUNuQkYsTUFBTUUsRUFBRSxDQUFDUyxLQUFLLEdBQUc3TDtnQkFDckI7WUFDSjtZQUNBNEYsUUFBUStGLFFBQVEsQ0FBQ3pCLGdCQUFnQixJQUFJQSxtQkFDL0J0RSxRQUFRb0csVUFBVSxDQUFDcEwsUUFDbkJnTCxjQUFjaEwsTUFBTTtRQUM5QjtJQUNKLEdBQUc7UUFBQ0E7UUFBTWdGO1FBQVN1YztRQUFTalk7S0FBaUI7SUFDN0MsT0FBTztRQUNIK1ksTUFBTXRqQiw4Q0FBaUIsQ0FBQ3NqQixNQUFNO1lBQUNSO1lBQWM3aEI7WUFBTWdGO1NBQVE7UUFDM0RzZCxNQUFNdmpCLDhDQUFpQixDQUFDdWpCLE1BQU07WUFBQ1Q7WUFBYzdoQjtZQUFNZ0Y7U0FBUTtRQUMzRGdkLFNBQVNqakIsOENBQWlCLENBQUNpakIsU0FBUztZQUFDSDtZQUFjN2hCO1lBQU1nRjtTQUFRO1FBQ2pFa0ksUUFBUW5PLDhDQUFpQixDQUFDbU8sUUFBUTtZQUFDMlU7WUFBYzdoQjtZQUFNZ0Y7U0FBUTtRQUMvRGtkLFFBQVFuakIsOENBQWlCLENBQUNtakIsUUFBUTtZQUFDTDtZQUFjN2hCO1lBQU1nRjtTQUFRO1FBQy9EcWIsUUFBUXRoQiw4Q0FBaUIsQ0FBQ29qQixVQUFVO1lBQUNOO1lBQWM3aEI7WUFBTWdGO1NBQVE7UUFDakV1ZCxRQUFReGpCLDhDQUFpQixDQUFDd2pCLFFBQVE7WUFBQ1Y7WUFBYzdoQjtZQUFNZ0Y7U0FBUTtRQUMvRC9DLFNBQVNsRCw4Q0FBaUIsQ0FBQ2tELFNBQVM7WUFBQzRmO1lBQWM3aEI7WUFBTWdGO1NBQVE7UUFDakVrTCxRQUFRblIsMENBQWEsQ0FBQyxJQUFNbVIsT0FBT3pJLEdBQUcsQ0FBQyxDQUFDNkMsT0FBTzNILFFBQVc7b0JBQ3RELEdBQUcySCxLQUFLO29CQUNSLENBQUNpWCxRQUFRLEVBQUVFLElBQUkzYSxPQUFPLENBQUNuRSxNQUFNLElBQUkwYztnQkFDckMsS0FBSztZQUFDblA7WUFBUXFSO1NBQVE7SUFDMUI7QUFDSjtBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNEJDLEdBQ0QsU0FBU21CLFFBQVFoZSxRQUFRLENBQUMsQ0FBQztJQUN2QixNQUFNaWUsZUFBZTVqQix5Q0FBWSxDQUFDNEM7SUFDbEMsTUFBTWloQixVQUFVN2pCLHlDQUFZLENBQUM0QztJQUM3QixNQUFNLENBQUNvRCxXQUFXaUIsZ0JBQWdCLEdBQUdqSCwyQ0FBYyxDQUFDO1FBQ2hEc0gsU0FBUztRQUNUSyxjQUFjO1FBQ2RKLFdBQVd5SSxXQUFXckssTUFBTVMsYUFBYTtRQUN6QzhPLGFBQWE7UUFDYitDLGNBQWM7UUFDZG5KLG9CQUFvQjtRQUNwQmxILFNBQVM7UUFDVG1RLGFBQWE7UUFDYnZRLGFBQWEsQ0FBQztRQUNkQyxlQUFlLENBQUM7UUFDaEJDLGtCQUFrQixDQUFDO1FBQ25CRyxRQUFRbEMsTUFBTWtDLE1BQU0sSUFBSSxDQUFDO1FBQ3pCZCxVQUFVcEIsTUFBTW9CLFFBQVEsSUFBSTtRQUM1QmlSLFNBQVM7UUFDVDVSLGVBQWU0SixXQUFXckssTUFBTVMsYUFBYSxJQUN2Q3hELFlBQ0ErQyxNQUFNUyxhQUFhO0lBQzdCO0lBQ0EsSUFBSSxDQUFDd2QsYUFBYTdiLE9BQU8sRUFBRTtRQUN2QixJQUFJcEMsTUFBTTBhLFdBQVcsRUFBRTtZQUNuQnVELGFBQWE3YixPQUFPLEdBQUc7Z0JBQ25CLEdBQUdwQyxNQUFNMGEsV0FBVztnQkFDcEJyYTtZQUNKO1lBQ0EsSUFBSUwsTUFBTVMsYUFBYSxJQUFJLENBQUM0SixXQUFXckssTUFBTVMsYUFBYSxHQUFHO2dCQUN6RFQsTUFBTTBhLFdBQVcsQ0FBQ1gsS0FBSyxDQUFDL1osTUFBTVMsYUFBYSxFQUFFVCxNQUFNeWEsWUFBWTtZQUNuRTtRQUNKLE9BQ0s7WUFDRCxNQUFNLEVBQUVDLFdBQVcsRUFBRSxHQUFHN1MsTUFBTSxHQUFHc0ssa0JBQWtCblM7WUFDbkRpZSxhQUFhN2IsT0FBTyxHQUFHO2dCQUNuQixHQUFHeUYsSUFBSTtnQkFDUHhIO1lBQ0o7UUFDSjtJQUNKO0lBQ0EsTUFBTUMsVUFBVTJkLGFBQWE3YixPQUFPLENBQUM5QixPQUFPO0lBQzVDQSxRQUFRK0YsUUFBUSxHQUFHckc7SUFDbkJlLDBCQUEwQjtRQUN0QixNQUFNb2QsTUFBTTdkLFFBQVE2QixVQUFVLENBQUM7WUFDM0I5QixXQUFXQyxRQUFRUSxlQUFlO1lBQ2xDdUIsVUFBVSxJQUFNZixnQkFBZ0I7b0JBQUUsR0FBR2hCLFFBQVFrQixVQUFVO2dCQUFDO1lBQ3hEb1csY0FBYztRQUNsQjtRQUNBdFcsZ0JBQWdCLENBQUMvRSxPQUFVO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQOFYsU0FBUztZQUNiO1FBQ0EvUixRQUFRa0IsVUFBVSxDQUFDNlEsT0FBTyxHQUFHO1FBQzdCLE9BQU84TDtJQUNYLEdBQUc7UUFBQzdkO0tBQVE7SUFDWmpHLDRDQUFlLENBQUMsSUFBTWlHLFFBQVFxWSxZQUFZLENBQUMzWSxNQUFNb0IsUUFBUSxHQUFHO1FBQUNkO1FBQVNOLE1BQU1vQixRQUFRO0tBQUM7SUFDckYvRyw0Q0FBZSxDQUFDO1FBQ1osSUFBSTJGLE1BQU15TixJQUFJLEVBQUU7WUFDWm5OLFFBQVErRixRQUFRLENBQUNvSCxJQUFJLEdBQUd6TixNQUFNeU4sSUFBSTtRQUN0QztRQUNBLElBQUl6TixNQUFNd1AsY0FBYyxFQUFFO1lBQ3RCbFAsUUFBUStGLFFBQVEsQ0FBQ21KLGNBQWMsR0FBR3hQLE1BQU13UCxjQUFjO1FBQzFEO0lBQ0osR0FBRztRQUFDbFA7UUFBU04sTUFBTXlOLElBQUk7UUFBRXpOLE1BQU13UCxjQUFjO0tBQUM7SUFDOUNuViw0Q0FBZSxDQUFDO1FBQ1osSUFBSTJGLE1BQU1rQyxNQUFNLEVBQUU7WUFDZDVCLFFBQVEyVCxVQUFVLENBQUNqVSxNQUFNa0MsTUFBTTtZQUMvQjVCLFFBQVFvWSxXQUFXO1FBQ3ZCO0lBQ0osR0FBRztRQUFDcFk7UUFBU04sTUFBTWtDLE1BQU07S0FBQztJQUMxQjdILDRDQUFlLENBQUM7UUFDWjJGLE1BQU00RSxnQkFBZ0IsSUFDbEJ0RSxRQUFRMEksU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUN6QjNFLFFBQVFqRSxRQUFRK0QsU0FBUztRQUM3QjtJQUNSLEdBQUc7UUFBQy9EO1FBQVNOLE1BQU00RSxnQkFBZ0I7S0FBQztJQUNwQ3ZLLDRDQUFlLENBQUM7UUFDWixJQUFJaUcsUUFBUVEsZUFBZSxDQUFDYSxPQUFPLEVBQUU7WUFDakMsTUFBTUEsVUFBVXJCLFFBQVF5VCxTQUFTO1lBQ2pDLElBQUlwUyxZQUFZdEIsVUFBVXNCLE9BQU8sRUFBRTtnQkFDL0JyQixRQUFRMEksU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDekJ2SDtnQkFDSjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUNyQjtRQUFTRCxVQUFVc0IsT0FBTztLQUFDO0lBQy9CdEgsNENBQWUsQ0FBQztRQUNaLElBQUkyRixNQUFNdUUsTUFBTSxJQUFJLENBQUNwQixVQUFVbkQsTUFBTXVFLE1BQU0sRUFBRTJaLFFBQVE5YixPQUFPLEdBQUc7WUFDM0Q5QixRQUFROFksTUFBTSxDQUFDcFosTUFBTXVFLE1BQU0sRUFBRTtnQkFDekJ5VixlQUFlO2dCQUNmLEdBQUcxWixRQUFRK0YsUUFBUSxDQUFDb1UsWUFBWTtZQUNwQztZQUNBeUQsUUFBUTliLE9BQU8sR0FBR3BDLE1BQU11RSxNQUFNO1lBQzlCakQsZ0JBQWdCLENBQUMySCxRQUFXO29CQUFFLEdBQUdBLEtBQUs7Z0JBQUM7UUFDM0MsT0FDSztZQUNEM0ksUUFBUWlhLG1CQUFtQjtRQUMvQjtJQUNKLEdBQUc7UUFBQ2phO1FBQVNOLE1BQU11RSxNQUFNO0tBQUM7SUFDMUJsSyw0Q0FBZSxDQUFDO1FBQ1osSUFBSSxDQUFDaUcsUUFBUWtHLE1BQU0sQ0FBQ0QsS0FBSyxFQUFFO1lBQ3ZCakcsUUFBUWdDLFNBQVM7WUFDakJoQyxRQUFRa0csTUFBTSxDQUFDRCxLQUFLLEdBQUc7UUFDM0I7UUFDQSxJQUFJakcsUUFBUWtHLE1BQU0sQ0FBQzNELEtBQUssRUFBRTtZQUN0QnZDLFFBQVFrRyxNQUFNLENBQUMzRCxLQUFLLEdBQUc7WUFDdkJ2QyxRQUFRMEksU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFBRSxHQUFHNUksUUFBUWtCLFVBQVU7WUFBQztRQUN6RDtRQUNBbEIsUUFBUW9FLGdCQUFnQjtJQUM1QjtJQUNBdVosYUFBYTdiLE9BQU8sQ0FBQy9CLFNBQVMsR0FBR0Qsa0JBQWtCQyxXQUFXQztJQUM5RCxPQUFPMmQsYUFBYTdiLE9BQU87QUFDL0I7QUFFb0ssQ0FDcEssc0NBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY2FwLWNsaWVudC1wb3J0YWwvLi9ub2RlX21vZHVsZXMvcmVhY3QtaG9vay1mb3JtL2Rpc3QvaW5kZXguZXNtLm1qcz85ZGMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbnZhciBpc0NoZWNrQm94SW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAnY2hlY2tib3gnO1xuXG52YXIgaXNEYXRlT2JqZWN0ID0gKHZhbHVlKSA9PiB2YWx1ZSBpbnN0YW5jZW9mIERhdGU7XG5cbnZhciBpc051bGxPclVuZGVmaW5lZCA9ICh2YWx1ZSkgPT4gdmFsdWUgPT0gbnVsbDtcblxuY29uc3QgaXNPYmplY3RUeXBlID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnO1xudmFyIGlzT2JqZWN0ID0gKHZhbHVlKSA9PiAhaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpICYmXG4gICAgIUFycmF5LmlzQXJyYXkodmFsdWUpICYmXG4gICAgaXNPYmplY3RUeXBlKHZhbHVlKSAmJlxuICAgICFpc0RhdGVPYmplY3QodmFsdWUpO1xuXG52YXIgZ2V0RXZlbnRWYWx1ZSA9IChldmVudCkgPT4gaXNPYmplY3QoZXZlbnQpICYmIGV2ZW50LnRhcmdldFxuICAgID8gaXNDaGVja0JveElucHV0KGV2ZW50LnRhcmdldClcbiAgICAgICAgPyBldmVudC50YXJnZXQuY2hlY2tlZFxuICAgICAgICA6IGV2ZW50LnRhcmdldC52YWx1ZVxuICAgIDogZXZlbnQ7XG5cbnZhciBnZXROb2RlUGFyZW50TmFtZSA9IChuYW1lKSA9PiBuYW1lLnN1YnN0cmluZygwLCBuYW1lLnNlYXJjaCgvXFwuXFxkKyhcXC58JCkvKSkgfHwgbmFtZTtcblxudmFyIGlzTmFtZUluRmllbGRBcnJheSA9IChuYW1lcywgbmFtZSkgPT4gbmFtZXMuaGFzKGdldE5vZGVQYXJlbnROYW1lKG5hbWUpKTtcblxudmFyIGlzUGxhaW5PYmplY3QgPSAodGVtcE9iamVjdCkgPT4ge1xuICAgIGNvbnN0IHByb3RvdHlwZUNvcHkgPSB0ZW1wT2JqZWN0LmNvbnN0cnVjdG9yICYmIHRlbXBPYmplY3QuY29uc3RydWN0b3IucHJvdG90eXBlO1xuICAgIHJldHVybiAoaXNPYmplY3QocHJvdG90eXBlQ29weSkgJiYgcHJvdG90eXBlQ29weS5oYXNPd25Qcm9wZXJ0eSgnaXNQcm90b3R5cGVPZicpKTtcbn07XG5cbnZhciBpc1dlYiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmXG4gICAgdHlwZW9mIHdpbmRvdy5IVE1MRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCcgJiZcbiAgICB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnO1xuXG5mdW5jdGlvbiBjbG9uZU9iamVjdChkYXRhKSB7XG4gICAgbGV0IGNvcHk7XG4gICAgY29uc3QgaXNBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgY29uc3QgaXNGaWxlTGlzdEluc3RhbmNlID0gdHlwZW9mIEZpbGVMaXN0ICE9PSAndW5kZWZpbmVkJyA/IGRhdGEgaW5zdGFuY2VvZiBGaWxlTGlzdCA6IGZhbHNlO1xuICAgIGlmIChkYXRhIGluc3RhbmNlb2YgRGF0ZSkge1xuICAgICAgICBjb3B5ID0gbmV3IERhdGUoZGF0YSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKCEoaXNXZWIgJiYgKGRhdGEgaW5zdGFuY2VvZiBCbG9iIHx8IGlzRmlsZUxpc3RJbnN0YW5jZSkpICYmXG4gICAgICAgIChpc0FycmF5IHx8IGlzT2JqZWN0KGRhdGEpKSkge1xuICAgICAgICBjb3B5ID0gaXNBcnJheSA/IFtdIDoge307XG4gICAgICAgIGlmICghaXNBcnJheSAmJiAhaXNQbGFpbk9iamVjdChkYXRhKSkge1xuICAgICAgICAgICAgY29weSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICBjb3B5W2tleV0gPSBjbG9uZU9iamVjdChkYXRhW2tleV0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgfVxuICAgIHJldHVybiBjb3B5O1xufVxuXG52YXIgaXNLZXkgPSAodmFsdWUpID0+IC9eXFx3KiQvLnRlc3QodmFsdWUpO1xuXG52YXIgaXNVbmRlZmluZWQgPSAodmFsKSA9PiB2YWwgPT09IHVuZGVmaW5lZDtcblxudmFyIGNvbXBhY3QgPSAodmFsdWUpID0+IEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUuZmlsdGVyKEJvb2xlYW4pIDogW107XG5cbnZhciBzdHJpbmdUb1BhdGggPSAoaW5wdXQpID0+IGNvbXBhY3QoaW5wdXQucmVwbGFjZSgvW1wifCddfFxcXS9nLCAnJykuc3BsaXQoL1xcLnxcXFsvKSk7XG5cbnZhciBnZXQgPSAob2JqZWN0LCBwYXRoLCBkZWZhdWx0VmFsdWUpID0+IHtcbiAgICBpZiAoIXBhdGggfHwgIWlzT2JqZWN0KG9iamVjdCkpIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHRWYWx1ZTtcbiAgICB9XG4gICAgY29uc3QgcmVzdWx0ID0gKGlzS2V5KHBhdGgpID8gW3BhdGhdIDogc3RyaW5nVG9QYXRoKHBhdGgpKS5yZWR1Y2UoKHJlc3VsdCwga2V5KSA9PiBpc051bGxPclVuZGVmaW5lZChyZXN1bHQpID8gcmVzdWx0IDogcmVzdWx0W2tleV0sIG9iamVjdCk7XG4gICAgcmV0dXJuIGlzVW5kZWZpbmVkKHJlc3VsdCkgfHwgcmVzdWx0ID09PSBvYmplY3RcbiAgICAgICAgPyBpc1VuZGVmaW5lZChvYmplY3RbcGF0aF0pXG4gICAgICAgICAgICA/IGRlZmF1bHRWYWx1ZVxuICAgICAgICAgICAgOiBvYmplY3RbcGF0aF1cbiAgICAgICAgOiByZXN1bHQ7XG59O1xuXG52YXIgaXNCb29sZWFuID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJztcblxudmFyIHNldCA9IChvYmplY3QsIHBhdGgsIHZhbHVlKSA9PiB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgY29uc3QgdGVtcFBhdGggPSBpc0tleShwYXRoKSA/IFtwYXRoXSA6IHN0cmluZ1RvUGF0aChwYXRoKTtcbiAgICBjb25zdCBsZW5ndGggPSB0ZW1wUGF0aC5sZW5ndGg7XG4gICAgY29uc3QgbGFzdEluZGV4ID0gbGVuZ3RoIC0gMTtcbiAgICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICBjb25zdCBrZXkgPSB0ZW1wUGF0aFtpbmRleF07XG4gICAgICAgIGxldCBuZXdWYWx1ZSA9IHZhbHVlO1xuICAgICAgICBpZiAoaW5kZXggIT09IGxhc3RJbmRleCkge1xuICAgICAgICAgICAgY29uc3Qgb2JqVmFsdWUgPSBvYmplY3Rba2V5XTtcbiAgICAgICAgICAgIG5ld1ZhbHVlID1cbiAgICAgICAgICAgICAgICBpc09iamVjdChvYmpWYWx1ZSkgfHwgQXJyYXkuaXNBcnJheShvYmpWYWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgPyBvYmpWYWx1ZVxuICAgICAgICAgICAgICAgICAgICA6ICFpc05hTigrdGVtcFBhdGhbaW5kZXggKyAxXSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gW11cbiAgICAgICAgICAgICAgICAgICAgICAgIDoge307XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InIHx8IGtleSA9PT0gJ3Byb3RvdHlwZScpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBvYmplY3Rba2V5XSA9IG5ld1ZhbHVlO1xuICAgICAgICBvYmplY3QgPSBvYmplY3Rba2V5XTtcbiAgICB9XG59O1xuXG5jb25zdCBFVkVOVFMgPSB7XG4gICAgQkxVUjogJ2JsdXInLFxuICAgIEZPQ1VTX09VVDogJ2ZvY3Vzb3V0JyxcbiAgICBDSEFOR0U6ICdjaGFuZ2UnLFxufTtcbmNvbnN0IFZBTElEQVRJT05fTU9ERSA9IHtcbiAgICBvbkJsdXI6ICdvbkJsdXInLFxuICAgIG9uQ2hhbmdlOiAnb25DaGFuZ2UnLFxuICAgIG9uU3VibWl0OiAnb25TdWJtaXQnLFxuICAgIG9uVG91Y2hlZDogJ29uVG91Y2hlZCcsXG4gICAgYWxsOiAnYWxsJyxcbn07XG5jb25zdCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTID0ge1xuICAgIG1heDogJ21heCcsXG4gICAgbWluOiAnbWluJyxcbiAgICBtYXhMZW5ndGg6ICdtYXhMZW5ndGgnLFxuICAgIG1pbkxlbmd0aDogJ21pbkxlbmd0aCcsXG4gICAgcGF0dGVybjogJ3BhdHRlcm4nLFxuICAgIHJlcXVpcmVkOiAncmVxdWlyZWQnLFxuICAgIHZhbGlkYXRlOiAndmFsaWRhdGUnLFxufTtcblxuY29uc3QgSG9va0Zvcm1Db250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dChudWxsKTtcbkhvb2tGb3JtQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdIb29rRm9ybUNvbnRleHQnO1xuLyoqXG4gKiBUaGlzIGN1c3RvbSBob29rIGFsbG93cyB5b3UgdG8gYWNjZXNzIHRoZSBmb3JtIGNvbnRleHQuIHVzZUZvcm1Db250ZXh0IGlzIGludGVuZGVkIHRvIGJlIHVzZWQgaW4gZGVlcGx5IG5lc3RlZCBzdHJ1Y3R1cmVzLCB3aGVyZSBpdCB3b3VsZCBiZWNvbWUgaW5jb252ZW5pZW50IHRvIHBhc3MgdGhlIGNvbnRleHQgYXMgYSBwcm9wLiBUbyBiZSB1c2VkIHdpdGgge0BsaW5rIEZvcm1Qcm92aWRlcn0uXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWZvcm1jb250ZXh0KSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdjctZm9ybS1jb250ZXh0LXl0dWRpKVxuICpcbiAqIEByZXR1cm5zIHJldHVybiBhbGwgdXNlRm9ybSBtZXRob2RzXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCBtZXRob2RzID0gdXNlRm9ybSgpO1xuICogICBjb25zdCBvblN1Ym1pdCA9IGRhdGEgPT4gY29uc29sZS5sb2coZGF0YSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxGb3JtUHJvdmlkZXIgey4uLm1ldGhvZHN9ID5cbiAqICAgICAgIDxmb3JtIG9uU3VibWl0PXttZXRob2RzLmhhbmRsZVN1Ym1pdChvblN1Ym1pdCl9PlxuICogICAgICAgICA8TmVzdGVkSW5wdXQgLz5cbiAqICAgICAgICAgPGlucHV0IHR5cGU9XCJzdWJtaXRcIiAvPlxuICogICAgICAgPC9mb3JtPlxuICogICAgIDwvRm9ybVByb3ZpZGVyPlxuICogICApO1xuICogfVxuICpcbiAqICBmdW5jdGlvbiBOZXN0ZWRJbnB1dCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciB9ID0gdXNlRm9ybUNvbnRleHQoKTsgLy8gcmV0cmlldmUgYWxsIGhvb2sgbWV0aG9kc1xuICogICByZXR1cm4gPGlucHV0IHsuLi5yZWdpc3RlcihcInRlc3RcIil9IC8+O1xuICogfVxuICogYGBgXG4gKi9cbmNvbnN0IHVzZUZvcm1Db250ZXh0ID0gKCkgPT4gUmVhY3QudXNlQ29udGV4dChIb29rRm9ybUNvbnRleHQpO1xuLyoqXG4gKiBBIHByb3ZpZGVyIGNvbXBvbmVudCB0aGF0IHByb3BhZ2F0ZXMgdGhlIGB1c2VGb3JtYCBtZXRob2RzIHRvIGFsbCBjaGlsZHJlbiBjb21wb25lbnRzIHZpYSBbUmVhY3QgQ29udGV4dF0oaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL2NvbnRleHQuaHRtbCkgQVBJLiBUbyBiZSB1c2VkIHdpdGgge0BsaW5rIHVzZUZvcm1Db250ZXh0fS5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybWNvbnRleHQpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS12Ny1mb3JtLWNvbnRleHQteXR1ZGkpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gYWxsIHVzZUZvcm0gbWV0aG9kc1xuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm0oKTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSBkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Rm9ybVByb3ZpZGVyIHsuLi5tZXRob2RzfSA+XG4gKiAgICAgICA8Zm9ybSBvblN1Ym1pdD17bWV0aG9kcy5oYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgICAgPE5lc3RlZElucHV0IC8+XG4gKiAgICAgICAgIDxpbnB1dCB0eXBlPVwic3VibWl0XCIgLz5cbiAqICAgICAgIDwvZm9ybT5cbiAqICAgICA8L0Zvcm1Qcm92aWRlcj5cbiAqICAgKTtcbiAqIH1cbiAqXG4gKiAgZnVuY3Rpb24gTmVzdGVkSW5wdXQoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIgfSA9IHVzZUZvcm1Db250ZXh0KCk7IC8vIHJldHJpZXZlIGFsbCBob29rIG1ldGhvZHNcbiAqICAgcmV0dXJuIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJ0ZXN0XCIpfSAvPjtcbiAqIH1cbiAqIGBgYFxuICovXG5jb25zdCBGb3JtUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICBjb25zdCB7IGNoaWxkcmVuLCAuLi5kYXRhIH0gPSBwcm9wcztcbiAgICByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoSG9va0Zvcm1Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkYXRhIH0sIGNoaWxkcmVuKSk7XG59O1xuXG52YXIgZ2V0UHJveHlGb3JtU3RhdGUgPSAoZm9ybVN0YXRlLCBjb250cm9sLCBsb2NhbFByb3h5Rm9ybVN0YXRlLCBpc1Jvb3QgPSB0cnVlKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgICBkZWZhdWx0VmFsdWVzOiBjb250cm9sLl9kZWZhdWx0VmFsdWVzLFxuICAgIH07XG4gICAgZm9yIChjb25zdCBrZXkgaW4gZm9ybVN0YXRlKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShyZXN1bHQsIGtleSwge1xuICAgICAgICAgICAgZ2V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgX2tleSA9IGtleTtcbiAgICAgICAgICAgICAgICBpZiAoY29udHJvbC5fcHJveHlGb3JtU3RhdGVbX2tleV0gIT09IFZBTElEQVRJT05fTU9ERS5hbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbC5fcHJveHlGb3JtU3RhdGVbX2tleV0gPSAhaXNSb290IHx8IFZBTElEQVRJT05fTU9ERS5hbGw7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxvY2FsUHJveHlGb3JtU3RhdGUgJiYgKGxvY2FsUHJveHlGb3JtU3RhdGVbX2tleV0gPSB0cnVlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZm9ybVN0YXRlW19rZXldO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59O1xuXG5jb25zdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiBSZWFjdC51c2VFZmZlY3Q7XG5cbi8qKlxuICogVGhpcyBjdXN0b20gaG9vayBhbGxvd3MgeW91IHRvIHN1YnNjcmliZSB0byBlYWNoIGZvcm0gc3RhdGUsIGFuZCBpc29sYXRlIHRoZSByZS1yZW5kZXIgYXQgdGhlIGN1c3RvbSBob29rIGxldmVsLiBJdCBoYXMgaXRzIHNjb3BlIGluIHRlcm1zIG9mIGZvcm0gc3RhdGUgc3Vic2NyaXB0aW9uLCBzbyBpdCB3b3VsZCBub3QgYWZmZWN0IG90aGVyIHVzZUZvcm1TdGF0ZSBhbmQgdXNlRm9ybS4gVXNpbmcgdGhpcyBob29rIGNhbiByZWR1Y2UgdGhlIHJlLXJlbmRlciBpbXBhY3Qgb24gbGFyZ2UgYW5kIGNvbXBsZXggZm9ybSBhcHBsaWNhdGlvbi5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybXN0YXRlKSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy91c2Vmb3Jtc3RhdGUtNzV4bHkpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gaW5jbHVkZSBvcHRpb25zIG9uIHNwZWNpZnkgZmllbGRzIHRvIHN1YnNjcmliZS4ge0BsaW5rIFVzZUZvcm1TdGF0ZVJldHVybn1cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIsIGhhbmRsZVN1Ym1pdCwgY29udHJvbCB9ID0gdXNlRm9ybSh7XG4gKiAgICAgZGVmYXVsdFZhbHVlczoge1xuICogICAgIGZpcnN0TmFtZTogXCJmaXJzdE5hbWVcIlxuICogICB9fSk7XG4gKiAgIGNvbnN0IHsgZGlydHlGaWVsZHMgfSA9IHVzZUZvcm1TdGF0ZSh7XG4gKiAgICAgY29udHJvbFxuICogICB9KTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSAoZGF0YSkgPT4gY29uc29sZS5sb2coZGF0YSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJmaXJzdE5hbWVcIil9IHBsYWNlaG9sZGVyPVwiRmlyc3QgTmFtZVwiIC8+XG4gKiAgICAgICB7ZGlydHlGaWVsZHMuZmlyc3ROYW1lICYmIDxwPkZpZWxkIGlzIGRpcnR5LjwvcD59XG4gKiAgICAgICA8aW5wdXQgdHlwZT1cInN1Ym1pdFwiIC8+XG4gKiAgICAgPC9mb3JtPlxuICogICApO1xuICogfVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZUZvcm1TdGF0ZShwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgZGlzYWJsZWQsIG5hbWUsIGV4YWN0IH0gPSBwcm9wcyB8fCB7fTtcbiAgICBjb25zdCBbZm9ybVN0YXRlLCB1cGRhdGVGb3JtU3RhdGVdID0gUmVhY3QudXNlU3RhdGUoY29udHJvbC5fZm9ybVN0YXRlKTtcbiAgICBjb25zdCBfbG9jYWxQcm94eUZvcm1TdGF0ZSA9IFJlYWN0LnVzZVJlZih7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICBkaXJ0eUZpZWxkczogZmFsc2UsXG4gICAgICAgIHRvdWNoZWRGaWVsZHM6IGZhbHNlLFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIGVycm9yczogZmFsc2UsXG4gICAgfSk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiBjb250cm9sLl9zdWJzY3JpYmUoe1xuICAgICAgICBuYW1lLFxuICAgICAgICBmb3JtU3RhdGU6IF9sb2NhbFByb3h5Rm9ybVN0YXRlLmN1cnJlbnQsXG4gICAgICAgIGV4YWN0LFxuICAgICAgICBjYWxsYmFjazogKGZvcm1TdGF0ZSkgPT4ge1xuICAgICAgICAgICAgIWRpc2FibGVkICYmXG4gICAgICAgICAgICAgICAgdXBkYXRlRm9ybVN0YXRlKHtcbiAgICAgICAgICAgICAgICAgICAgLi4uY29udHJvbC5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgICAgICAuLi5mb3JtU3RhdGUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgfSksIFtuYW1lLCBkaXNhYmxlZCwgZXhhY3RdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBfbG9jYWxQcm94eUZvcm1TdGF0ZS5jdXJyZW50LmlzVmFsaWQgJiYgY29udHJvbC5fc2V0VmFsaWQodHJ1ZSk7XG4gICAgfSwgW2NvbnRyb2xdKTtcbiAgICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiBnZXRQcm94eUZvcm1TdGF0ZShmb3JtU3RhdGUsIGNvbnRyb2wsIF9sb2NhbFByb3h5Rm9ybVN0YXRlLmN1cnJlbnQsIGZhbHNlKSwgW2Zvcm1TdGF0ZSwgY29udHJvbF0pO1xufVxuXG52YXIgaXNTdHJpbmcgPSAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZyc7XG5cbnZhciBnZW5lcmF0ZVdhdGNoT3V0cHV0ID0gKG5hbWVzLCBfbmFtZXMsIGZvcm1WYWx1ZXMsIGlzR2xvYmFsLCBkZWZhdWx0VmFsdWUpID0+IHtcbiAgICBpZiAoaXNTdHJpbmcobmFtZXMpKSB7XG4gICAgICAgIGlzR2xvYmFsICYmIF9uYW1lcy53YXRjaC5hZGQobmFtZXMpO1xuICAgICAgICByZXR1cm4gZ2V0KGZvcm1WYWx1ZXMsIG5hbWVzLCBkZWZhdWx0VmFsdWUpO1xuICAgIH1cbiAgICBpZiAoQXJyYXkuaXNBcnJheShuYW1lcykpIHtcbiAgICAgICAgcmV0dXJuIG5hbWVzLm1hcCgoZmllbGROYW1lKSA9PiAoaXNHbG9iYWwgJiYgX25hbWVzLndhdGNoLmFkZChmaWVsZE5hbWUpLFxuICAgICAgICAgICAgZ2V0KGZvcm1WYWx1ZXMsIGZpZWxkTmFtZSkpKTtcbiAgICB9XG4gICAgaXNHbG9iYWwgJiYgKF9uYW1lcy53YXRjaEFsbCA9IHRydWUpO1xuICAgIHJldHVybiBmb3JtVmFsdWVzO1xufTtcblxudmFyIGlzUHJpbWl0aXZlID0gKHZhbHVlKSA9PiBpc051bGxPclVuZGVmaW5lZCh2YWx1ZSkgfHwgIWlzT2JqZWN0VHlwZSh2YWx1ZSk7XG5cbmZ1bmN0aW9uIGRlZXBFcXVhbChvYmplY3QxLCBvYmplY3QyLCBfaW50ZXJuYWxfdmlzaXRlZCA9IG5ldyBXZWFrU2V0KCkpIHtcbiAgICBpZiAoaXNQcmltaXRpdmUob2JqZWN0MSkgfHwgaXNQcmltaXRpdmUob2JqZWN0MikpIHtcbiAgICAgICAgcmV0dXJuIG9iamVjdDEgPT09IG9iamVjdDI7XG4gICAgfVxuICAgIGlmIChpc0RhdGVPYmplY3Qob2JqZWN0MSkgJiYgaXNEYXRlT2JqZWN0KG9iamVjdDIpKSB7XG4gICAgICAgIHJldHVybiBvYmplY3QxLmdldFRpbWUoKSA9PT0gb2JqZWN0Mi5nZXRUaW1lKCk7XG4gICAgfVxuICAgIGNvbnN0IGtleXMxID0gT2JqZWN0LmtleXMob2JqZWN0MSk7XG4gICAgY29uc3Qga2V5czIgPSBPYmplY3Qua2V5cyhvYmplY3QyKTtcbiAgICBpZiAoa2V5czEubGVuZ3RoICE9PSBrZXlzMi5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoX2ludGVybmFsX3Zpc2l0ZWQuaGFzKG9iamVjdDEpIHx8IF9pbnRlcm5hbF92aXNpdGVkLmhhcyhvYmplY3QyKSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgX2ludGVybmFsX3Zpc2l0ZWQuYWRkKG9iamVjdDEpO1xuICAgIF9pbnRlcm5hbF92aXNpdGVkLmFkZChvYmplY3QyKTtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBrZXlzMSkge1xuICAgICAgICBjb25zdCB2YWwxID0gb2JqZWN0MVtrZXldO1xuICAgICAgICBpZiAoIWtleXMyLmluY2x1ZGVzKGtleSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoa2V5ICE9PSAncmVmJykge1xuICAgICAgICAgICAgY29uc3QgdmFsMiA9IG9iamVjdDJba2V5XTtcbiAgICAgICAgICAgIGlmICgoaXNEYXRlT2JqZWN0KHZhbDEpICYmIGlzRGF0ZU9iamVjdCh2YWwyKSkgfHxcbiAgICAgICAgICAgICAgICAoaXNPYmplY3QodmFsMSkgJiYgaXNPYmplY3QodmFsMikpIHx8XG4gICAgICAgICAgICAgICAgKEFycmF5LmlzQXJyYXkodmFsMSkgJiYgQXJyYXkuaXNBcnJheSh2YWwyKSlcbiAgICAgICAgICAgICAgICA/ICFkZWVwRXF1YWwodmFsMSwgdmFsMiwgX2ludGVybmFsX3Zpc2l0ZWQpXG4gICAgICAgICAgICAgICAgOiB2YWwxICE9PSB2YWwyKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuXG4vKipcbiAqIEN1c3RvbSBob29rIHRvIHN1YnNjcmliZSB0byBmaWVsZCBjaGFuZ2UgYW5kIGlzb2xhdGUgcmUtcmVuZGVyaW5nIGF0IHRoZSBjb21wb25lbnQgbGV2ZWwuXG4gKlxuICogQHJlbWFya3NcbiAqXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2V3YXRjaCkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXY3LXRzLXVzZXdhdGNoLWg5aTVlKVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGNvbnN0IHsgY29udHJvbCB9ID0gdXNlRm9ybSgpO1xuICogY29uc3QgdmFsdWVzID0gdXNlV2F0Y2goe1xuICogICBuYW1lOiBcImZpZWxkTmFtZVwiXG4gKiAgIGNvbnRyb2wsXG4gKiB9KVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZVdhdGNoKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBjb250cm9sID0gbWV0aG9kcy5jb250cm9sLCBuYW1lLCBkZWZhdWx0VmFsdWUsIGRpc2FibGVkLCBleGFjdCwgY29tcHV0ZSwgfSA9IHByb3BzIHx8IHt9O1xuICAgIGNvbnN0IF9kZWZhdWx0VmFsdWUgPSBSZWFjdC51c2VSZWYoZGVmYXVsdFZhbHVlKTtcbiAgICBjb25zdCBfY29tcHV0ZSA9IFJlYWN0LnVzZVJlZihjb21wdXRlKTtcbiAgICBjb25zdCBfY29tcHV0ZUZvcm1WYWx1ZXMgPSBSZWFjdC51c2VSZWYodW5kZWZpbmVkKTtcbiAgICBfY29tcHV0ZS5jdXJyZW50ID0gY29tcHV0ZTtcbiAgICBjb25zdCBkZWZhdWx0VmFsdWVNZW1vID0gUmVhY3QudXNlTWVtbygoKSA9PiBjb250cm9sLl9nZXRXYXRjaChuYW1lLCBfZGVmYXVsdFZhbHVlLmN1cnJlbnQpLCBbY29udHJvbCwgbmFtZV0pO1xuICAgIGNvbnN0IFt2YWx1ZSwgdXBkYXRlVmFsdWVdID0gUmVhY3QudXNlU3RhdGUoX2NvbXB1dGUuY3VycmVudCA/IF9jb21wdXRlLmN1cnJlbnQoZGVmYXVsdFZhbHVlTWVtbykgOiBkZWZhdWx0VmFsdWVNZW1vKTtcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IGNvbnRyb2wuX3N1YnNjcmliZSh7XG4gICAgICAgIG5hbWUsXG4gICAgICAgIGZvcm1TdGF0ZToge1xuICAgICAgICAgICAgdmFsdWVzOiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgICBleGFjdCxcbiAgICAgICAgY2FsbGJhY2s6IChmb3JtU3RhdGUpID0+IHtcbiAgICAgICAgICAgIGlmICghZGlzYWJsZWQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmb3JtVmFsdWVzID0gZ2VuZXJhdGVXYXRjaE91dHB1dChuYW1lLCBjb250cm9sLl9uYW1lcywgZm9ybVN0YXRlLnZhbHVlcyB8fCBjb250cm9sLl9mb3JtVmFsdWVzLCBmYWxzZSwgX2RlZmF1bHRWYWx1ZS5jdXJyZW50KTtcbiAgICAgICAgICAgICAgICBpZiAoX2NvbXB1dGUuY3VycmVudCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjb21wdXRlZEZvcm1WYWx1ZXMgPSBfY29tcHV0ZS5jdXJyZW50KGZvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWRlZXBFcXVhbChjb21wdXRlZEZvcm1WYWx1ZXMsIF9jb21wdXRlRm9ybVZhbHVlcy5jdXJyZW50KSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlVmFsdWUoY29tcHV0ZWRGb3JtVmFsdWVzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9jb21wdXRlRm9ybVZhbHVlcy5jdXJyZW50ID0gY29tcHV0ZWRGb3JtVmFsdWVzO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVWYWx1ZShmb3JtVmFsdWVzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgfSksIFtjb250cm9sLCBkaXNhYmxlZCwgbmFtZSwgZXhhY3RdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4gY29udHJvbC5fcmVtb3ZlVW5tb3VudGVkKCkpO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuLyoqXG4gKiBDdXN0b20gaG9vayB0byB3b3JrIHdpdGggY29udHJvbGxlZCBjb21wb25lbnQsIHRoaXMgZnVuY3Rpb24gcHJvdmlkZSB5b3Ugd2l0aCBib3RoIGZvcm0gYW5kIGZpZWxkIGxldmVsIHN0YXRlLiBSZS1yZW5kZXIgaXMgaXNvbGF0ZWQgYXQgdGhlIGhvb2sgbGV2ZWwuXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWNvbnRyb2xsZXIpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3VzZWNvbnRyb2xsZXItMG84cHgpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdGhlIHBhdGggbmFtZSB0byB0aGUgZm9ybSBmaWVsZCB2YWx1ZSwgYW5kIHZhbGlkYXRpb24gcnVsZXMuXG4gKlxuICogQHJldHVybnMgZmllbGQgcHJvcGVydGllcywgZmllbGQgYW5kIGZvcm0gc3RhdGUuIHtAbGluayBVc2VDb250cm9sbGVyUmV0dXJufVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIElucHV0KHByb3BzKSB7XG4gKiAgIGNvbnN0IHsgZmllbGQsIGZpZWxkU3RhdGUsIGZvcm1TdGF0ZSB9ID0gdXNlQ29udHJvbGxlcihwcm9wcyk7XG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGRpdj5cbiAqICAgICAgIDxpbnB1dCB7Li4uZmllbGR9IHBsYWNlaG9sZGVyPXtwcm9wcy5uYW1lfSAvPlxuICogICAgICAgPHA+e2ZpZWxkU3RhdGUuaXNUb3VjaGVkICYmIFwiVG91Y2hlZFwifTwvcD5cbiAqICAgICAgIDxwPntmb3JtU3RhdGUuaXNTdWJtaXR0ZWQgPyBcInN1Ym1pdHRlZFwiIDogXCJcIn08L3A+XG4gKiAgICAgPC9kaXY+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gdXNlQ29udHJvbGxlcihwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgbmFtZSwgZGlzYWJsZWQsIGNvbnRyb2wgPSBtZXRob2RzLmNvbnRyb2wsIHNob3VsZFVucmVnaXN0ZXIsIGRlZmF1bHRWYWx1ZSwgfSA9IHByb3BzO1xuICAgIGNvbnN0IGlzQXJyYXlGaWVsZCA9IGlzTmFtZUluRmllbGRBcnJheShjb250cm9sLl9uYW1lcy5hcnJheSwgbmFtZSk7XG4gICAgY29uc3QgZGVmYXVsdFZhbHVlTWVtbyA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gZ2V0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIG5hbWUsIGdldChjb250cm9sLl9kZWZhdWx0VmFsdWVzLCBuYW1lLCBkZWZhdWx0VmFsdWUpKSwgW2NvbnRyb2wsIG5hbWUsIGRlZmF1bHRWYWx1ZV0pO1xuICAgIGNvbnN0IHZhbHVlID0gdXNlV2F0Y2goe1xuICAgICAgICBjb250cm9sLFxuICAgICAgICBuYW1lLFxuICAgICAgICBkZWZhdWx0VmFsdWU6IGRlZmF1bHRWYWx1ZU1lbW8sXG4gICAgICAgIGV4YWN0OiB0cnVlLFxuICAgIH0pO1xuICAgIGNvbnN0IGZvcm1TdGF0ZSA9IHVzZUZvcm1TdGF0ZSh7XG4gICAgICAgIGNvbnRyb2wsXG4gICAgICAgIG5hbWUsXG4gICAgICAgIGV4YWN0OiB0cnVlLFxuICAgIH0pO1xuICAgIGNvbnN0IF9wcm9wcyA9IFJlYWN0LnVzZVJlZihwcm9wcyk7XG4gICAgY29uc3QgX3JlZ2lzdGVyUHJvcHMgPSBSZWFjdC51c2VSZWYoY29udHJvbC5yZWdpc3RlcihuYW1lLCB7XG4gICAgICAgIC4uLnByb3BzLnJ1bGVzLFxuICAgICAgICB2YWx1ZSxcbiAgICAgICAgLi4uKGlzQm9vbGVhbihwcm9wcy5kaXNhYmxlZCkgPyB7IGRpc2FibGVkOiBwcm9wcy5kaXNhYmxlZCB9IDoge30pLFxuICAgIH0pKTtcbiAgICBfcHJvcHMuY3VycmVudCA9IHByb3BzO1xuICAgIGNvbnN0IGZpZWxkU3RhdGUgPSBSZWFjdC51c2VNZW1vKCgpID0+IE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHt9LCB7XG4gICAgICAgIGludmFsaWQ6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpLFxuICAgICAgICB9LFxuICAgICAgICBpc0RpcnR5OiB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiAhIWdldChmb3JtU3RhdGUuZGlydHlGaWVsZHMsIG5hbWUpLFxuICAgICAgICB9LFxuICAgICAgICBpc1RvdWNoZWQ6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKSxcbiAgICAgICAgfSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiAhIWdldChmb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiBnZXQoZm9ybVN0YXRlLmVycm9ycywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgfSksIFtmb3JtU3RhdGUsIG5hbWVdKTtcbiAgICBjb25zdCBvbkNoYW5nZSA9IFJlYWN0LnVzZUNhbGxiYWNrKChldmVudCkgPT4gX3JlZ2lzdGVyUHJvcHMuY3VycmVudC5vbkNoYW5nZSh7XG4gICAgICAgIHRhcmdldDoge1xuICAgICAgICAgICAgdmFsdWU6IGdldEV2ZW50VmFsdWUoZXZlbnQpLFxuICAgICAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgfSxcbiAgICAgICAgdHlwZTogRVZFTlRTLkNIQU5HRSxcbiAgICB9KSwgW25hbWVdKTtcbiAgICBjb25zdCBvbkJsdXIgPSBSZWFjdC51c2VDYWxsYmFjaygoKSA9PiBfcmVnaXN0ZXJQcm9wcy5jdXJyZW50Lm9uQmx1cih7XG4gICAgICAgIHRhcmdldDoge1xuICAgICAgICAgICAgdmFsdWU6IGdldChjb250cm9sLl9mb3JtVmFsdWVzLCBuYW1lKSxcbiAgICAgICAgICAgIG5hbWU6IG5hbWUsXG4gICAgICAgIH0sXG4gICAgICAgIHR5cGU6IEVWRU5UUy5CTFVSLFxuICAgIH0pLCBbbmFtZSwgY29udHJvbC5fZm9ybVZhbHVlc10pO1xuICAgIGNvbnN0IHJlZiA9IFJlYWN0LnVzZUNhbGxiYWNrKChlbG0pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoY29udHJvbC5fZmllbGRzLCBuYW1lKTtcbiAgICAgICAgaWYgKGZpZWxkICYmIGVsbSkge1xuICAgICAgICAgICAgZmllbGQuX2YucmVmID0ge1xuICAgICAgICAgICAgICAgIGZvY3VzOiAoKSA9PiBlbG0uZm9jdXMgJiYgZWxtLmZvY3VzKCksXG4gICAgICAgICAgICAgICAgc2VsZWN0OiAoKSA9PiBlbG0uc2VsZWN0ICYmIGVsbS5zZWxlY3QoKSxcbiAgICAgICAgICAgICAgICBzZXRDdXN0b21WYWxpZGl0eTogKG1lc3NhZ2UpID0+IGVsbS5zZXRDdXN0b21WYWxpZGl0eShtZXNzYWdlKSxcbiAgICAgICAgICAgICAgICByZXBvcnRWYWxpZGl0eTogKCkgPT4gZWxtLnJlcG9ydFZhbGlkaXR5KCksXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSwgW2NvbnRyb2wuX2ZpZWxkcywgbmFtZV0pO1xuICAgIGNvbnN0IGZpZWxkID0gUmVhY3QudXNlTWVtbygoKSA9PiAoe1xuICAgICAgICBuYW1lLFxuICAgICAgICB2YWx1ZSxcbiAgICAgICAgLi4uKGlzQm9vbGVhbihkaXNhYmxlZCkgfHwgZm9ybVN0YXRlLmRpc2FibGVkXG4gICAgICAgICAgICA/IHsgZGlzYWJsZWQ6IGZvcm1TdGF0ZS5kaXNhYmxlZCB8fCBkaXNhYmxlZCB9XG4gICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgb25DaGFuZ2UsXG4gICAgICAgIG9uQmx1cixcbiAgICAgICAgcmVmLFxuICAgIH0pLCBbbmFtZSwgZGlzYWJsZWQsIGZvcm1TdGF0ZS5kaXNhYmxlZCwgb25DaGFuZ2UsIG9uQmx1ciwgcmVmLCB2YWx1ZV0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IF9zaG91bGRVbnJlZ2lzdGVyRmllbGQgPSBjb250cm9sLl9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXIgfHwgc2hvdWxkVW5yZWdpc3RlcjtcbiAgICAgICAgY29udHJvbC5yZWdpc3RlcihuYW1lLCB7XG4gICAgICAgICAgICAuLi5fcHJvcHMuY3VycmVudC5ydWxlcyxcbiAgICAgICAgICAgIC4uLihpc0Jvb2xlYW4oX3Byb3BzLmN1cnJlbnQuZGlzYWJsZWQpXG4gICAgICAgICAgICAgICAgPyB7IGRpc2FibGVkOiBfcHJvcHMuY3VycmVudC5kaXNhYmxlZCB9XG4gICAgICAgICAgICAgICAgOiB7fSksXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCB1cGRhdGVNb3VudGVkID0gKG5hbWUsIHZhbHVlKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZCA9IGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2YubW91bnQgPSB2YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdXBkYXRlTW91bnRlZChuYW1lLCB0cnVlKTtcbiAgICAgICAgaWYgKF9zaG91bGRVbnJlZ2lzdGVyRmllbGQpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gY2xvbmVPYmplY3QoZ2V0KGNvbnRyb2wuX29wdGlvbnMuZGVmYXVsdFZhbHVlcywgbmFtZSkpO1xuICAgICAgICAgICAgc2V0KGNvbnRyb2wuX2RlZmF1bHRWYWx1ZXMsIG5hbWUsIHZhbHVlKTtcbiAgICAgICAgICAgIGlmIChpc1VuZGVmaW5lZChnZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSkpKSB7XG4gICAgICAgICAgICAgICAgc2V0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIG5hbWUsIHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAhaXNBcnJheUZpZWxkICYmIGNvbnRyb2wucmVnaXN0ZXIobmFtZSk7XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAoaXNBcnJheUZpZWxkXG4gICAgICAgICAgICAgICAgPyBfc2hvdWxkVW5yZWdpc3RlckZpZWxkICYmICFjb250cm9sLl9zdGF0ZS5hY3Rpb25cbiAgICAgICAgICAgICAgICA6IF9zaG91bGRVbnJlZ2lzdGVyRmllbGQpXG4gICAgICAgICAgICAgICAgPyBjb250cm9sLnVucmVnaXN0ZXIobmFtZSlcbiAgICAgICAgICAgICAgICA6IHVwZGF0ZU1vdW50ZWQobmFtZSwgZmFsc2UpO1xuICAgICAgICB9O1xuICAgIH0sIFtuYW1lLCBjb250cm9sLCBpc0FycmF5RmllbGQsIHNob3VsZFVucmVnaXN0ZXJdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBjb250cm9sLl9zZXREaXNhYmxlZEZpZWxkKHtcbiAgICAgICAgICAgIGRpc2FibGVkLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfSk7XG4gICAgfSwgW2Rpc2FibGVkLCBuYW1lLCBjb250cm9sXSk7XG4gICAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKHtcbiAgICAgICAgZmllbGQsXG4gICAgICAgIGZvcm1TdGF0ZSxcbiAgICAgICAgZmllbGRTdGF0ZSxcbiAgICB9KSwgW2ZpZWxkLCBmb3JtU3RhdGUsIGZpZWxkU3RhdGVdKTtcbn1cblxuLyoqXG4gKiBDb21wb25lbnQgYmFzZWQgb24gYHVzZUNvbnRyb2xsZXJgIGhvb2sgdG8gd29yayB3aXRoIGNvbnRyb2xsZWQgY29tcG9uZW50LlxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2Vjb250cm9sbGVyL2NvbnRyb2xsZXIpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS12Ni1jb250cm9sbGVyLXRzLWp3eXp3KSDigKIgW1ZpZGVvXShodHRwczovL3d3dy55b3V0dWJlLmNvbS93YXRjaD92PU4yVU5rX1VDVnlBKVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIHRoZSBwYXRoIG5hbWUgdG8gdGhlIGZvcm0gZmllbGQgdmFsdWUsIGFuZCB2YWxpZGF0aW9uIHJ1bGVzLlxuICpcbiAqIEByZXR1cm5zIHByb3ZpZGUgZmllbGQgaGFuZGxlciBmdW5jdGlvbnMsIGZpZWxkIGFuZCBmb3JtIHN0YXRlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyBjb250cm9sIH0gPSB1c2VGb3JtPEZvcm1WYWx1ZXM+KHtcbiAqICAgICBkZWZhdWx0VmFsdWVzOiB7XG4gKiAgICAgICB0ZXN0OiBcIlwiXG4gKiAgICAgfVxuICogICB9KTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGZvcm0+XG4gKiAgICAgICA8Q29udHJvbGxlclxuICogICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICogICAgICAgICBuYW1lPVwidGVzdFwiXG4gKiAgICAgICAgIHJlbmRlcj17KHsgZmllbGQ6IHsgb25DaGFuZ2UsIG9uQmx1ciwgdmFsdWUsIHJlZiB9LCBmb3JtU3RhdGUsIGZpZWxkU3RhdGUgfSkgPT4gKFxuICogICAgICAgICAgIDw+XG4gKiAgICAgICAgICAgICA8aW5wdXRcbiAqICAgICAgICAgICAgICAgb25DaGFuZ2U9e29uQ2hhbmdlfSAvLyBzZW5kIHZhbHVlIHRvIGhvb2sgZm9ybVxuICogICAgICAgICAgICAgICBvbkJsdXI9e29uQmx1cn0gLy8gbm90aWZ5IHdoZW4gaW5wdXQgaXMgdG91Y2hlZFxuICogICAgICAgICAgICAgICB2YWx1ZT17dmFsdWV9IC8vIHJldHVybiB1cGRhdGVkIHZhbHVlXG4gKiAgICAgICAgICAgICAgIHJlZj17cmVmfSAvLyBzZXQgcmVmIGZvciBmb2N1cyBtYW5hZ2VtZW50XG4gKiAgICAgICAgICAgICAvPlxuICogICAgICAgICAgICAgPHA+e2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZCA/IFwic3VibWl0dGVkXCIgOiBcIlwifTwvcD5cbiAqICAgICAgICAgICAgIDxwPntmaWVsZFN0YXRlLmlzVG91Y2hlZCA/IFwidG91Y2hlZFwiIDogXCJcIn08L3A+XG4gKiAgICAgICAgICAgPC8+XG4gKiAgICAgICAgICl9XG4gKiAgICAgICAvPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5jb25zdCBDb250cm9sbGVyID0gKHByb3BzKSA9PiBwcm9wcy5yZW5kZXIodXNlQ29udHJvbGxlcihwcm9wcykpO1xuXG5jb25zdCBmbGF0dGVuID0gKG9iaikgPT4ge1xuICAgIGNvbnN0IG91dHB1dCA9IHt9O1xuICAgIGZvciAoY29uc3Qga2V5IG9mIE9iamVjdC5rZXlzKG9iaikpIHtcbiAgICAgICAgaWYgKGlzT2JqZWN0VHlwZShvYmpba2V5XSkgJiYgb2JqW2tleV0gIT09IG51bGwpIHtcbiAgICAgICAgICAgIGNvbnN0IG5lc3RlZCA9IGZsYXR0ZW4ob2JqW2tleV0pO1xuICAgICAgICAgICAgZm9yIChjb25zdCBuZXN0ZWRLZXkgb2YgT2JqZWN0LmtleXMobmVzdGVkKSkge1xuICAgICAgICAgICAgICAgIG91dHB1dFtgJHtrZXl9LiR7bmVzdGVkS2V5fWBdID0gbmVzdGVkW25lc3RlZEtleV07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBvdXRwdXRba2V5XSA9IG9ialtrZXldO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBvdXRwdXQ7XG59O1xuXG5jb25zdCBQT1NUX1JFUVVFU1QgPSAncG9zdCc7XG4vKipcbiAqIEZvcm0gY29tcG9uZW50IHRvIG1hbmFnZSBzdWJtaXNzaW9uLlxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIHRvIHNldHVwIHN1Ym1pc3Npb24gZGV0YWlsLiB7QGxpbmsgRm9ybVByb3BzfVxuICpcbiAqIEByZXR1cm5zIGZvcm0gY29tcG9uZW50IG9yIGhlYWRsZXNzIHJlbmRlciBwcm9wLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyBjb250cm9sLCBmb3JtU3RhdGU6IHsgZXJyb3JzIH0gfSA9IHVzZUZvcm0oKTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPEZvcm0gYWN0aW9uPVwiL2FwaVwiIGNvbnRyb2w9e2NvbnRyb2x9PlxuICogICAgICAgPGlucHV0IHsuLi5yZWdpc3RlcihcIm5hbWVcIil9IC8+XG4gKiAgICAgICA8cD57ZXJyb3JzPy5yb290Py5zZXJ2ZXIgJiYgJ1NlcnZlciBlcnJvcid9PC9wPlxuICogICAgICAgPGJ1dHRvbj5TdWJtaXQ8L2J1dHRvbj5cbiAqICAgICA8L0Zvcm0+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gRm9ybShwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCB7IGNvbnRyb2wgPSBtZXRob2RzLmNvbnRyb2wsIG9uU3VibWl0LCBjaGlsZHJlbiwgYWN0aW9uLCBtZXRob2QgPSBQT1NUX1JFUVVFU1QsIGhlYWRlcnMsIGVuY1R5cGUsIG9uRXJyb3IsIHJlbmRlciwgb25TdWNjZXNzLCB2YWxpZGF0ZVN0YXR1cywgLi4ucmVzdCB9ID0gcHJvcHM7XG4gICAgY29uc3Qgc3VibWl0ID0gYXN5bmMgKGV2ZW50KSA9PiB7XG4gICAgICAgIGxldCBoYXNFcnJvciA9IGZhbHNlO1xuICAgICAgICBsZXQgdHlwZSA9ICcnO1xuICAgICAgICBhd2FpdCBjb250cm9sLmhhbmRsZVN1Ym1pdChhc3luYyAoZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcbiAgICAgICAgICAgIGxldCBmb3JtRGF0YUpzb24gPSAnJztcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgZm9ybURhdGFKc29uID0gSlNPTi5zdHJpbmdpZnkoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoX2EpIHsgfVxuICAgICAgICAgICAgY29uc3QgZmxhdHRlbkZvcm1WYWx1ZXMgPSBmbGF0dGVuKGNvbnRyb2wuX2Zvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZmxhdHRlbkZvcm1WYWx1ZXMpIHtcbiAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCBmbGF0dGVuRm9ybVZhbHVlc1trZXldKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChvblN1Ym1pdCkge1xuICAgICAgICAgICAgICAgIGF3YWl0IG9uU3VibWl0KHtcbiAgICAgICAgICAgICAgICAgICAgZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgZXZlbnQsXG4gICAgICAgICAgICAgICAgICAgIG1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEsXG4gICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhSnNvbixcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChhY3Rpb24pIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzaG91bGRTdHJpbmdpZnlTdWJtaXNzaW9uRGF0YSA9IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnMgJiYgaGVhZGVyc1snQ29udGVudC1UeXBlJ10sXG4gICAgICAgICAgICAgICAgICAgICAgICBlbmNUeXBlLFxuICAgICAgICAgICAgICAgICAgICBdLnNvbWUoKHZhbHVlKSA9PiB2YWx1ZSAmJiB2YWx1ZS5pbmNsdWRlcygnanNvbicpKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChTdHJpbmcoYWN0aW9uKSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgbWV0aG9kLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uKGVuY1R5cGUgJiYgZW5jVHlwZSAhPT0gJ211bHRpcGFydC9mb3JtLWRhdGEnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8geyAnQ29udGVudC1UeXBlJzogZW5jVHlwZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDoge30pLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvZHk6IHNob3VsZFN0cmluZ2lmeVN1Ym1pc3Npb25EYXRhID8gZm9ybURhdGFKc29uIDogZm9ybURhdGEsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICh2YWxpZGF0ZVN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gIXZhbGlkYXRlU3RhdHVzKHJlc3BvbnNlLnN0YXR1cylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJlc3BvbnNlLnN0YXR1cyA8IDIwMCB8fCByZXNwb25zZS5zdGF0dXMgPj0gMzAwKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvciAmJiBvbkVycm9yKHsgcmVzcG9uc2UgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlID0gU3RyaW5nKHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBvblN1Y2Nlc3MgJiYgb25TdWNjZXNzKHsgcmVzcG9uc2UgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIGhhc0Vycm9yID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgb25FcnJvciAmJiBvbkVycm9yKHsgZXJyb3IgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9KShldmVudCk7XG4gICAgICAgIGlmIChoYXNFcnJvciAmJiBwcm9wcy5jb250cm9sKSB7XG4gICAgICAgICAgICBwcm9wcy5jb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGZhbHNlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBwcm9wcy5jb250cm9sLnNldEVycm9yKCdyb290LnNlcnZlcicsIHtcbiAgICAgICAgICAgICAgICB0eXBlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gICAgfSwgW10pO1xuICAgIHJldHVybiByZW5kZXIgPyAoUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCwgcmVuZGVyKHtcbiAgICAgICAgc3VibWl0LFxuICAgIH0pKSkgOiAoUmVhY3QuY3JlYXRlRWxlbWVudChcImZvcm1cIiwgeyBub1ZhbGlkYXRlOiBtb3VudGVkLCBhY3Rpb246IGFjdGlvbiwgbWV0aG9kOiBtZXRob2QsIGVuY1R5cGU6IGVuY1R5cGUsIG9uU3VibWl0OiBzdWJtaXQsIC4uLnJlc3QgfSwgY2hpbGRyZW4pKTtcbn1cblxudmFyIGFwcGVuZEVycm9ycyA9IChuYW1lLCB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEsIGVycm9ycywgdHlwZSwgbWVzc2FnZSkgPT4gdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhXG4gICAgPyB7XG4gICAgICAgIC4uLmVycm9yc1tuYW1lXSxcbiAgICAgICAgdHlwZXM6IHtcbiAgICAgICAgICAgIC4uLihlcnJvcnNbbmFtZV0gJiYgZXJyb3JzW25hbWVdLnR5cGVzID8gZXJyb3JzW25hbWVdLnR5cGVzIDoge30pLFxuICAgICAgICAgICAgW3R5cGVdOiBtZXNzYWdlIHx8IHRydWUsXG4gICAgICAgIH0sXG4gICAgfVxuICAgIDoge307XG5cbnZhciBjb252ZXJ0VG9BcnJheVBheWxvYWQgPSAodmFsdWUpID0+IChBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlIDogW3ZhbHVlXSk7XG5cbnZhciBjcmVhdGVTdWJqZWN0ID0gKCkgPT4ge1xuICAgIGxldCBfb2JzZXJ2ZXJzID0gW107XG4gICAgY29uc3QgbmV4dCA9ICh2YWx1ZSkgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IG9ic2VydmVyIG9mIF9vYnNlcnZlcnMpIHtcbiAgICAgICAgICAgIG9ic2VydmVyLm5leHQgJiYgb2JzZXJ2ZXIubmV4dCh2YWx1ZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHN1YnNjcmliZSA9IChvYnNlcnZlcikgPT4ge1xuICAgICAgICBfb2JzZXJ2ZXJzLnB1c2gob2JzZXJ2ZXIpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdW5zdWJzY3JpYmU6ICgpID0+IHtcbiAgICAgICAgICAgICAgICBfb2JzZXJ2ZXJzID0gX29ic2VydmVycy5maWx0ZXIoKG8pID0+IG8gIT09IG9ic2VydmVyKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgfTtcbiAgICBjb25zdCB1bnN1YnNjcmliZSA9ICgpID0+IHtcbiAgICAgICAgX29ic2VydmVycyA9IFtdO1xuICAgIH07XG4gICAgcmV0dXJuIHtcbiAgICAgICAgZ2V0IG9ic2VydmVycygpIHtcbiAgICAgICAgICAgIHJldHVybiBfb2JzZXJ2ZXJzO1xuICAgICAgICB9LFxuICAgICAgICBuZXh0LFxuICAgICAgICBzdWJzY3JpYmUsXG4gICAgICAgIHVuc3Vic2NyaWJlLFxuICAgIH07XG59O1xuXG52YXIgaXNFbXB0eU9iamVjdCA9ICh2YWx1ZSkgPT4gaXNPYmplY3QodmFsdWUpICYmICFPYmplY3Qua2V5cyh2YWx1ZSkubGVuZ3RoO1xuXG52YXIgaXNGaWxlSW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAnZmlsZSc7XG5cbnZhciBpc0Z1bmN0aW9uID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbic7XG5cbnZhciBpc0hUTUxFbGVtZW50ID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKCFpc1dlYikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IG93bmVyID0gdmFsdWUgPyB2YWx1ZS5vd25lckRvY3VtZW50IDogMDtcbiAgICByZXR1cm4gKHZhbHVlIGluc3RhbmNlb2ZcbiAgICAgICAgKG93bmVyICYmIG93bmVyLmRlZmF1bHRWaWV3ID8gb3duZXIuZGVmYXVsdFZpZXcuSFRNTEVsZW1lbnQgOiBIVE1MRWxlbWVudCkpO1xufTtcblxudmFyIGlzTXVsdGlwbGVTZWxlY3QgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSBgc2VsZWN0LW11bHRpcGxlYDtcblxudmFyIGlzUmFkaW9JbnB1dCA9IChlbGVtZW50KSA9PiBlbGVtZW50LnR5cGUgPT09ICdyYWRpbyc7XG5cbnZhciBpc1JhZGlvT3JDaGVja2JveCA9IChyZWYpID0+IGlzUmFkaW9JbnB1dChyZWYpIHx8IGlzQ2hlY2tCb3hJbnB1dChyZWYpO1xuXG52YXIgbGl2ZSA9IChyZWYpID0+IGlzSFRNTEVsZW1lbnQocmVmKSAmJiByZWYuaXNDb25uZWN0ZWQ7XG5cbmZ1bmN0aW9uIGJhc2VHZXQob2JqZWN0LCB1cGRhdGVQYXRoKSB7XG4gICAgY29uc3QgbGVuZ3RoID0gdXBkYXRlUGF0aC5zbGljZSgwLCAtMSkubGVuZ3RoO1xuICAgIGxldCBpbmRleCA9IDA7XG4gICAgd2hpbGUgKGluZGV4IDwgbGVuZ3RoKSB7XG4gICAgICAgIG9iamVjdCA9IGlzVW5kZWZpbmVkKG9iamVjdCkgPyBpbmRleCsrIDogb2JqZWN0W3VwZGF0ZVBhdGhbaW5kZXgrK11dO1xuICAgIH1cbiAgICByZXR1cm4gb2JqZWN0O1xufVxuZnVuY3Rpb24gaXNFbXB0eUFycmF5KG9iaikge1xuICAgIGZvciAoY29uc3Qga2V5IGluIG9iaikge1xuICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KGtleSkgJiYgIWlzVW5kZWZpbmVkKG9ialtrZXldKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuZnVuY3Rpb24gdW5zZXQob2JqZWN0LCBwYXRoKSB7XG4gICAgY29uc3QgcGF0aHMgPSBBcnJheS5pc0FycmF5KHBhdGgpXG4gICAgICAgID8gcGF0aFxuICAgICAgICA6IGlzS2V5KHBhdGgpXG4gICAgICAgICAgICA/IFtwYXRoXVxuICAgICAgICAgICAgOiBzdHJpbmdUb1BhdGgocGF0aCk7XG4gICAgY29uc3QgY2hpbGRPYmplY3QgPSBwYXRocy5sZW5ndGggPT09IDEgPyBvYmplY3QgOiBiYXNlR2V0KG9iamVjdCwgcGF0aHMpO1xuICAgIGNvbnN0IGluZGV4ID0gcGF0aHMubGVuZ3RoIC0gMTtcbiAgICBjb25zdCBrZXkgPSBwYXRoc1tpbmRleF07XG4gICAgaWYgKGNoaWxkT2JqZWN0KSB7XG4gICAgICAgIGRlbGV0ZSBjaGlsZE9iamVjdFtrZXldO1xuICAgIH1cbiAgICBpZiAoaW5kZXggIT09IDAgJiZcbiAgICAgICAgKChpc09iamVjdChjaGlsZE9iamVjdCkgJiYgaXNFbXB0eU9iamVjdChjaGlsZE9iamVjdCkpIHx8XG4gICAgICAgICAgICAoQXJyYXkuaXNBcnJheShjaGlsZE9iamVjdCkgJiYgaXNFbXB0eUFycmF5KGNoaWxkT2JqZWN0KSkpKSB7XG4gICAgICAgIHVuc2V0KG9iamVjdCwgcGF0aHMuc2xpY2UoMCwgLTEpKTtcbiAgICB9XG4gICAgcmV0dXJuIG9iamVjdDtcbn1cblxudmFyIG9iamVjdEhhc0Z1bmN0aW9uID0gKGRhdGEpID0+IHtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgIGlmIChpc0Z1bmN0aW9uKGRhdGFba2V5XSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn07XG5cbmZ1bmN0aW9uIG1hcmtGaWVsZHNEaXJ0eShkYXRhLCBmaWVsZHMgPSB7fSkge1xuICAgIGNvbnN0IGlzUGFyZW50Tm9kZUFycmF5ID0gQXJyYXkuaXNBcnJheShkYXRhKTtcbiAgICBpZiAoaXNPYmplY3QoZGF0YSkgfHwgaXNQYXJlbnROb2RlQXJyYXkpIHtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YVtrZXldKSB8fFxuICAgICAgICAgICAgICAgIChpc09iamVjdChkYXRhW2tleV0pICYmICFvYmplY3RIYXNGdW5jdGlvbihkYXRhW2tleV0pKSkge1xuICAgICAgICAgICAgICAgIGZpZWxkc1trZXldID0gQXJyYXkuaXNBcnJheShkYXRhW2tleV0pID8gW10gOiB7fTtcbiAgICAgICAgICAgICAgICBtYXJrRmllbGRzRGlydHkoZGF0YVtrZXldLCBmaWVsZHNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICghaXNOdWxsT3JVbmRlZmluZWQoZGF0YVtrZXldKSkge1xuICAgICAgICAgICAgICAgIGZpZWxkc1trZXldID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZmllbGRzO1xufVxuZnVuY3Rpb24gZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyhkYXRhLCBmb3JtVmFsdWVzLCBkaXJ0eUZpZWxkc0Zyb21WYWx1ZXMpIHtcbiAgICBjb25zdCBpc1BhcmVudE5vZGVBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgaWYgKGlzT2JqZWN0KGRhdGEpIHx8IGlzUGFyZW50Tm9kZUFycmF5KSB7XG4gICAgICAgIGZvciAoY29uc3Qga2V5IGluIGRhdGEpIHtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGFba2V5XSkgfHxcbiAgICAgICAgICAgICAgICAoaXNPYmplY3QoZGF0YVtrZXldKSAmJiAhb2JqZWN0SGFzRnVuY3Rpb24oZGF0YVtrZXldKSkpIHtcbiAgICAgICAgICAgICAgICBpZiAoaXNVbmRlZmluZWQoZm9ybVZhbHVlcykgfHxcbiAgICAgICAgICAgICAgICAgICAgaXNQcmltaXRpdmUoZGlydHlGaWVsZHNGcm9tVmFsdWVzW2tleV0pKSB7XG4gICAgICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldID0gQXJyYXkuaXNBcnJheShkYXRhW2tleV0pXG4gICAgICAgICAgICAgICAgICAgICAgICA/IG1hcmtGaWVsZHNEaXJ0eShkYXRhW2tleV0sIFtdKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB7IC4uLm1hcmtGaWVsZHNEaXJ0eShkYXRhW2tleV0pIH07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzKGRhdGFba2V5XSwgaXNOdWxsT3JVbmRlZmluZWQoZm9ybVZhbHVlcykgPyB7fSA6IGZvcm1WYWx1ZXNba2V5XSwgZGlydHlGaWVsZHNGcm9tVmFsdWVzW2tleV0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldID0gIWRlZXBFcXVhbChkYXRhW2tleV0sIGZvcm1WYWx1ZXNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGRpcnR5RmllbGRzRnJvbVZhbHVlcztcbn1cbnZhciBnZXREaXJ0eUZpZWxkcyA9IChkZWZhdWx0VmFsdWVzLCBmb3JtVmFsdWVzKSA9PiBnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzKGRlZmF1bHRWYWx1ZXMsIGZvcm1WYWx1ZXMsIG1hcmtGaWVsZHNEaXJ0eShmb3JtVmFsdWVzKSk7XG5cbmNvbnN0IGRlZmF1bHRSZXN1bHQgPSB7XG4gICAgdmFsdWU6IGZhbHNlLFxuICAgIGlzVmFsaWQ6IGZhbHNlLFxufTtcbmNvbnN0IHZhbGlkUmVzdWx0ID0geyB2YWx1ZTogdHJ1ZSwgaXNWYWxpZDogdHJ1ZSB9O1xudmFyIGdldENoZWNrYm94VmFsdWUgPSAob3B0aW9ucykgPT4ge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG9wdGlvbnMpKSB7XG4gICAgICAgIGlmIChvcHRpb25zLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IG9wdGlvbnNcbiAgICAgICAgICAgICAgICAuZmlsdGVyKChvcHRpb24pID0+IG9wdGlvbiAmJiBvcHRpb24uY2hlY2tlZCAmJiAhb3B0aW9uLmRpc2FibGVkKVxuICAgICAgICAgICAgICAgIC5tYXAoKG9wdGlvbikgPT4gb3B0aW9uLnZhbHVlKTtcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiB2YWx1ZXMsIGlzVmFsaWQ6ICEhdmFsdWVzLmxlbmd0aCB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvcHRpb25zWzBdLmNoZWNrZWQgJiYgIW9wdGlvbnNbMF0uZGlzYWJsZWRcbiAgICAgICAgICAgID8gLy8gQHRzLWV4cGVjdC1lcnJvciBleHBlY3RlZCB0byB3b3JrIGluIHRoZSBicm93c2VyXG4gICAgICAgICAgICAgICAgb3B0aW9uc1swXS5hdHRyaWJ1dGVzICYmICFpc1VuZGVmaW5lZChvcHRpb25zWzBdLmF0dHJpYnV0ZXMudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgID8gaXNVbmRlZmluZWQob3B0aW9uc1swXS52YWx1ZSkgfHwgb3B0aW9uc1swXS52YWx1ZSA9PT0gJydcbiAgICAgICAgICAgICAgICAgICAgICAgID8gdmFsaWRSZXN1bHRcbiAgICAgICAgICAgICAgICAgICAgICAgIDogeyB2YWx1ZTogb3B0aW9uc1swXS52YWx1ZSwgaXNWYWxpZDogdHJ1ZSB9XG4gICAgICAgICAgICAgICAgICAgIDogdmFsaWRSZXN1bHRcbiAgICAgICAgICAgIDogZGVmYXVsdFJlc3VsdDtcbiAgICB9XG4gICAgcmV0dXJuIGRlZmF1bHRSZXN1bHQ7XG59O1xuXG52YXIgZ2V0RmllbGRWYWx1ZUFzID0gKHZhbHVlLCB7IHZhbHVlQXNOdW1iZXIsIHZhbHVlQXNEYXRlLCBzZXRWYWx1ZUFzIH0pID0+IGlzVW5kZWZpbmVkKHZhbHVlKVxuICAgID8gdmFsdWVcbiAgICA6IHZhbHVlQXNOdW1iZXJcbiAgICAgICAgPyB2YWx1ZSA9PT0gJydcbiAgICAgICAgICAgID8gTmFOXG4gICAgICAgICAgICA6IHZhbHVlXG4gICAgICAgICAgICAgICAgPyArdmFsdWVcbiAgICAgICAgICAgICAgICA6IHZhbHVlXG4gICAgICAgIDogdmFsdWVBc0RhdGUgJiYgaXNTdHJpbmcodmFsdWUpXG4gICAgICAgICAgICA/IG5ldyBEYXRlKHZhbHVlKVxuICAgICAgICAgICAgOiBzZXRWYWx1ZUFzXG4gICAgICAgICAgICAgICAgPyBzZXRWYWx1ZUFzKHZhbHVlKVxuICAgICAgICAgICAgICAgIDogdmFsdWU7XG5cbmNvbnN0IGRlZmF1bHRSZXR1cm4gPSB7XG4gICAgaXNWYWxpZDogZmFsc2UsXG4gICAgdmFsdWU6IG51bGwsXG59O1xudmFyIGdldFJhZGlvVmFsdWUgPSAob3B0aW9ucykgPT4gQXJyYXkuaXNBcnJheShvcHRpb25zKVxuICAgID8gb3B0aW9ucy5yZWR1Y2UoKHByZXZpb3VzLCBvcHRpb24pID0+IG9wdGlvbiAmJiBvcHRpb24uY2hlY2tlZCAmJiAhb3B0aW9uLmRpc2FibGVkXG4gICAgICAgID8ge1xuICAgICAgICAgICAgaXNWYWxpZDogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiBvcHRpb24udmFsdWUsXG4gICAgICAgIH1cbiAgICAgICAgOiBwcmV2aW91cywgZGVmYXVsdFJldHVybilcbiAgICA6IGRlZmF1bHRSZXR1cm47XG5cbmZ1bmN0aW9uIGdldEZpZWxkVmFsdWUoX2YpIHtcbiAgICBjb25zdCByZWYgPSBfZi5yZWY7XG4gICAgaWYgKGlzRmlsZUlucHV0KHJlZikpIHtcbiAgICAgICAgcmV0dXJuIHJlZi5maWxlcztcbiAgICB9XG4gICAgaWYgKGlzUmFkaW9JbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiBnZXRSYWRpb1ZhbHVlKF9mLnJlZnMpLnZhbHVlO1xuICAgIH1cbiAgICBpZiAoaXNNdWx0aXBsZVNlbGVjdChyZWYpKSB7XG4gICAgICAgIHJldHVybiBbLi4ucmVmLnNlbGVjdGVkT3B0aW9uc10ubWFwKCh7IHZhbHVlIH0pID0+IHZhbHVlKTtcbiAgICB9XG4gICAgaWYgKGlzQ2hlY2tCb3hJbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiBnZXRDaGVja2JveFZhbHVlKF9mLnJlZnMpLnZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gZ2V0RmllbGRWYWx1ZUFzKGlzVW5kZWZpbmVkKHJlZi52YWx1ZSkgPyBfZi5yZWYudmFsdWUgOiByZWYudmFsdWUsIF9mKTtcbn1cblxudmFyIGdldFJlc29sdmVyT3B0aW9ucyA9IChmaWVsZHNOYW1lcywgX2ZpZWxkcywgY3JpdGVyaWFNb2RlLCBzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSA9PiB7XG4gICAgY29uc3QgZmllbGRzID0ge307XG4gICAgZm9yIChjb25zdCBuYW1lIG9mIGZpZWxkc05hbWVzKSB7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBmaWVsZCAmJiBzZXQoZmllbGRzLCBuYW1lLCBmaWVsZC5fZik7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIGNyaXRlcmlhTW9kZSxcbiAgICAgICAgbmFtZXM6IFsuLi5maWVsZHNOYW1lc10sXG4gICAgICAgIGZpZWxkcyxcbiAgICAgICAgc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbixcbiAgICB9O1xufTtcblxudmFyIGlzUmVnZXggPSAodmFsdWUpID0+IHZhbHVlIGluc3RhbmNlb2YgUmVnRXhwO1xuXG52YXIgZ2V0UnVsZVZhbHVlID0gKHJ1bGUpID0+IGlzVW5kZWZpbmVkKHJ1bGUpXG4gICAgPyBydWxlXG4gICAgOiBpc1JlZ2V4KHJ1bGUpXG4gICAgICAgID8gcnVsZS5zb3VyY2VcbiAgICAgICAgOiBpc09iamVjdChydWxlKVxuICAgICAgICAgICAgPyBpc1JlZ2V4KHJ1bGUudmFsdWUpXG4gICAgICAgICAgICAgICAgPyBydWxlLnZhbHVlLnNvdXJjZVxuICAgICAgICAgICAgICAgIDogcnVsZS52YWx1ZVxuICAgICAgICAgICAgOiBydWxlO1xuXG52YXIgZ2V0VmFsaWRhdGlvbk1vZGVzID0gKG1vZGUpID0+ICh7XG4gICAgaXNPblN1Ym1pdDogIW1vZGUgfHwgbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uU3VibWl0LFxuICAgIGlzT25CbHVyOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUub25CbHVyLFxuICAgIGlzT25DaGFuZ2U6IG1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5vbkNoYW5nZSxcbiAgICBpc09uQWxsOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUuYWxsLFxuICAgIGlzT25Ub3VjaDogbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uVG91Y2hlZCxcbn0pO1xuXG5jb25zdCBBU1lOQ19GVU5DVElPTiA9ICdBc3luY0Z1bmN0aW9uJztcbnZhciBoYXNQcm9taXNlVmFsaWRhdGlvbiA9IChmaWVsZFJlZmVyZW5jZSkgPT4gISFmaWVsZFJlZmVyZW5jZSAmJlxuICAgICEhZmllbGRSZWZlcmVuY2UudmFsaWRhdGUgJiZcbiAgICAhISgoaXNGdW5jdGlvbihmaWVsZFJlZmVyZW5jZS52YWxpZGF0ZSkgJiZcbiAgICAgICAgZmllbGRSZWZlcmVuY2UudmFsaWRhdGUuY29uc3RydWN0b3IubmFtZSA9PT0gQVNZTkNfRlVOQ1RJT04pIHx8XG4gICAgICAgIChpc09iamVjdChmaWVsZFJlZmVyZW5jZS52YWxpZGF0ZSkgJiZcbiAgICAgICAgICAgIE9iamVjdC52YWx1ZXMoZmllbGRSZWZlcmVuY2UudmFsaWRhdGUpLmZpbmQoKHZhbGlkYXRlRnVuY3Rpb24pID0+IHZhbGlkYXRlRnVuY3Rpb24uY29uc3RydWN0b3IubmFtZSA9PT0gQVNZTkNfRlVOQ1RJT04pKSk7XG5cbnZhciBoYXNWYWxpZGF0aW9uID0gKG9wdGlvbnMpID0+IG9wdGlvbnMubW91bnQgJiZcbiAgICAob3B0aW9ucy5yZXF1aXJlZCB8fFxuICAgICAgICBvcHRpb25zLm1pbiB8fFxuICAgICAgICBvcHRpb25zLm1heCB8fFxuICAgICAgICBvcHRpb25zLm1heExlbmd0aCB8fFxuICAgICAgICBvcHRpb25zLm1pbkxlbmd0aCB8fFxuICAgICAgICBvcHRpb25zLnBhdHRlcm4gfHxcbiAgICAgICAgb3B0aW9ucy52YWxpZGF0ZSk7XG5cbnZhciBpc1dhdGNoZWQgPSAobmFtZSwgX25hbWVzLCBpc0JsdXJFdmVudCkgPT4gIWlzQmx1ckV2ZW50ICYmXG4gICAgKF9uYW1lcy53YXRjaEFsbCB8fFxuICAgICAgICBfbmFtZXMud2F0Y2guaGFzKG5hbWUpIHx8XG4gICAgICAgIFsuLi5fbmFtZXMud2F0Y2hdLnNvbWUoKHdhdGNoTmFtZSkgPT4gbmFtZS5zdGFydHNXaXRoKHdhdGNoTmFtZSkgJiZcbiAgICAgICAgICAgIC9eXFwuXFx3Ky8udGVzdChuYW1lLnNsaWNlKHdhdGNoTmFtZS5sZW5ndGgpKSkpO1xuXG5jb25zdCBpdGVyYXRlRmllbGRzQnlBY3Rpb24gPSAoZmllbGRzLCBhY3Rpb24sIGZpZWxkc05hbWVzLCBhYm9ydEVhcmx5KSA9PiB7XG4gICAgZm9yIChjb25zdCBrZXkgb2YgZmllbGRzTmFtZXMgfHwgT2JqZWN0LmtleXMoZmllbGRzKSkge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChmaWVsZHMsIGtleSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgeyBfZiwgLi4uY3VycmVudEZpZWxkIH0gPSBmaWVsZDtcbiAgICAgICAgICAgIGlmIChfZikge1xuICAgICAgICAgICAgICAgIGlmIChfZi5yZWZzICYmIF9mLnJlZnNbMF0gJiYgYWN0aW9uKF9mLnJlZnNbMF0sIGtleSkgJiYgIWFib3J0RWFybHkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKF9mLnJlZiAmJiBhY3Rpb24oX2YucmVmLCBfZi5uYW1lKSAmJiAhYWJvcnRFYXJseSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVyYXRlRmllbGRzQnlBY3Rpb24oY3VycmVudEZpZWxkLCBhY3Rpb24pKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGlzT2JqZWN0KGN1cnJlbnRGaWVsZCkpIHtcbiAgICAgICAgICAgICAgICBpZiAoaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKGN1cnJlbnRGaWVsZCwgYWN0aW9uKSkge1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuO1xufTtcblxuZnVuY3Rpb24gc2NoZW1hRXJyb3JMb29rdXAoZXJyb3JzLCBfZmllbGRzLCBuYW1lKSB7XG4gICAgY29uc3QgZXJyb3IgPSBnZXQoZXJyb3JzLCBuYW1lKTtcbiAgICBpZiAoZXJyb3IgfHwgaXNLZXkobmFtZSkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgbmFtZXMgPSBuYW1lLnNwbGl0KCcuJyk7XG4gICAgd2hpbGUgKG5hbWVzLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBmaWVsZE5hbWUgPSBuYW1lcy5qb2luKCcuJyk7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgIGNvbnN0IGZvdW5kRXJyb3IgPSBnZXQoZXJyb3JzLCBmaWVsZE5hbWUpO1xuICAgICAgICBpZiAoZmllbGQgJiYgIUFycmF5LmlzQXJyYXkoZmllbGQpICYmIG5hbWUgIT09IGZpZWxkTmFtZSkge1xuICAgICAgICAgICAgcmV0dXJuIHsgbmFtZSB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3VuZEVycm9yICYmIGZvdW5kRXJyb3IudHlwZSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBuYW1lOiBmaWVsZE5hbWUsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGZvdW5kRXJyb3IsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3VuZEVycm9yICYmIGZvdW5kRXJyb3Iucm9vdCAmJiBmb3VuZEVycm9yLnJvb3QudHlwZSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBuYW1lOiBgJHtmaWVsZE5hbWV9LnJvb3RgLFxuICAgICAgICAgICAgICAgIGVycm9yOiBmb3VuZEVycm9yLnJvb3QsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIG5hbWVzLnBvcCgpO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBuYW1lLFxuICAgIH07XG59XG5cbnZhciBzaG91bGRSZW5kZXJGb3JtU3RhdGUgPSAoZm9ybVN0YXRlRGF0YSwgX3Byb3h5Rm9ybVN0YXRlLCB1cGRhdGVGb3JtU3RhdGUsIGlzUm9vdCkgPT4ge1xuICAgIHVwZGF0ZUZvcm1TdGF0ZShmb3JtU3RhdGVEYXRhKTtcbiAgICBjb25zdCB7IG5hbWUsIC4uLmZvcm1TdGF0ZSB9ID0gZm9ybVN0YXRlRGF0YTtcbiAgICByZXR1cm4gKGlzRW1wdHlPYmplY3QoZm9ybVN0YXRlKSB8fFxuICAgICAgICBPYmplY3Qua2V5cyhmb3JtU3RhdGUpLmxlbmd0aCA+PSBPYmplY3Qua2V5cyhfcHJveHlGb3JtU3RhdGUpLmxlbmd0aCB8fFxuICAgICAgICBPYmplY3Qua2V5cyhmb3JtU3RhdGUpLmZpbmQoKGtleSkgPT4gX3Byb3h5Rm9ybVN0YXRlW2tleV0gPT09XG4gICAgICAgICAgICAoIWlzUm9vdCB8fCBWQUxJREFUSU9OX01PREUuYWxsKSkpO1xufTtcblxudmFyIHNob3VsZFN1YnNjcmliZUJ5TmFtZSA9IChuYW1lLCBzaWduYWxOYW1lLCBleGFjdCkgPT4gIW5hbWUgfHxcbiAgICAhc2lnbmFsTmFtZSB8fFxuICAgIG5hbWUgPT09IHNpZ25hbE5hbWUgfHxcbiAgICBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSkuc29tZSgoY3VycmVudE5hbWUpID0+IGN1cnJlbnROYW1lICYmXG4gICAgICAgIChleGFjdFxuICAgICAgICAgICAgPyBjdXJyZW50TmFtZSA9PT0gc2lnbmFsTmFtZVxuICAgICAgICAgICAgOiBjdXJyZW50TmFtZS5zdGFydHNXaXRoKHNpZ25hbE5hbWUpIHx8XG4gICAgICAgICAgICAgICAgc2lnbmFsTmFtZS5zdGFydHNXaXRoKGN1cnJlbnROYW1lKSkpO1xuXG52YXIgc2tpcFZhbGlkYXRpb24gPSAoaXNCbHVyRXZlbnQsIGlzVG91Y2hlZCwgaXNTdWJtaXR0ZWQsIHJlVmFsaWRhdGVNb2RlLCBtb2RlKSA9PiB7XG4gICAgaWYgKG1vZGUuaXNPbkFsbCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGVsc2UgaWYgKCFpc1N1Ym1pdHRlZCAmJiBtb2RlLmlzT25Ub3VjaCkge1xuICAgICAgICByZXR1cm4gIShpc1RvdWNoZWQgfHwgaXNCbHVyRXZlbnQpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1N1Ym1pdHRlZCA/IHJlVmFsaWRhdGVNb2RlLmlzT25CbHVyIDogbW9kZS5pc09uQmx1cikge1xuICAgICAgICByZXR1cm4gIWlzQmx1ckV2ZW50O1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1N1Ym1pdHRlZCA/IHJlVmFsaWRhdGVNb2RlLmlzT25DaGFuZ2UgOiBtb2RlLmlzT25DaGFuZ2UpIHtcbiAgICAgICAgcmV0dXJuIGlzQmx1ckV2ZW50O1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn07XG5cbnZhciB1bnNldEVtcHR5QXJyYXkgPSAocmVmLCBuYW1lKSA9PiAhY29tcGFjdChnZXQocmVmLCBuYW1lKSkubGVuZ3RoICYmIHVuc2V0KHJlZiwgbmFtZSk7XG5cbnZhciB1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yID0gKGVycm9ycywgZXJyb3IsIG5hbWUpID0+IHtcbiAgICBjb25zdCBmaWVsZEFycmF5RXJyb3JzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGdldChlcnJvcnMsIG5hbWUpKTtcbiAgICBzZXQoZmllbGRBcnJheUVycm9ycywgJ3Jvb3QnLCBlcnJvcltuYW1lXSk7XG4gICAgc2V0KGVycm9ycywgbmFtZSwgZmllbGRBcnJheUVycm9ycyk7XG4gICAgcmV0dXJuIGVycm9ycztcbn07XG5cbnZhciBpc01lc3NhZ2UgPSAodmFsdWUpID0+IGlzU3RyaW5nKHZhbHVlKTtcblxuZnVuY3Rpb24gZ2V0VmFsaWRhdGVFcnJvcihyZXN1bHQsIHJlZiwgdHlwZSA9ICd2YWxpZGF0ZScpIHtcbiAgICBpZiAoaXNNZXNzYWdlKHJlc3VsdCkgfHxcbiAgICAgICAgKEFycmF5LmlzQXJyYXkocmVzdWx0KSAmJiByZXN1bHQuZXZlcnkoaXNNZXNzYWdlKSkgfHxcbiAgICAgICAgKGlzQm9vbGVhbihyZXN1bHQpICYmICFyZXN1bHQpKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlLFxuICAgICAgICAgICAgbWVzc2FnZTogaXNNZXNzYWdlKHJlc3VsdCkgPyByZXN1bHQgOiAnJyxcbiAgICAgICAgICAgIHJlZixcbiAgICAgICAgfTtcbiAgICB9XG59XG5cbnZhciBnZXRWYWx1ZUFuZE1lc3NhZ2UgPSAodmFsaWRhdGlvbkRhdGEpID0+IGlzT2JqZWN0KHZhbGlkYXRpb25EYXRhKSAmJiAhaXNSZWdleCh2YWxpZGF0aW9uRGF0YSlcbiAgICA/IHZhbGlkYXRpb25EYXRhXG4gICAgOiB7XG4gICAgICAgIHZhbHVlOiB2YWxpZGF0aW9uRGF0YSxcbiAgICAgICAgbWVzc2FnZTogJycsXG4gICAgfTtcblxudmFyIHZhbGlkYXRlRmllbGQgPSBhc3luYyAoZmllbGQsIGRpc2FibGVkRmllbGROYW1lcywgZm9ybVZhbHVlcywgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uLCBpc0ZpZWxkQXJyYXkpID0+IHtcbiAgICBjb25zdCB7IHJlZiwgcmVmcywgcmVxdWlyZWQsIG1heExlbmd0aCwgbWluTGVuZ3RoLCBtaW4sIG1heCwgcGF0dGVybiwgdmFsaWRhdGUsIG5hbWUsIHZhbHVlQXNOdW1iZXIsIG1vdW50LCB9ID0gZmllbGQuX2Y7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGdldChmb3JtVmFsdWVzLCBuYW1lKTtcbiAgICBpZiAoIW1vdW50IHx8IGRpc2FibGVkRmllbGROYW1lcy5oYXMobmFtZSkpIHtcbiAgICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgICBjb25zdCBpbnB1dFJlZiA9IHJlZnMgPyByZWZzWzBdIDogcmVmO1xuICAgIGNvbnN0IHNldEN1c3RvbVZhbGlkaXR5ID0gKG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKHNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24gJiYgaW5wdXRSZWYucmVwb3J0VmFsaWRpdHkpIHtcbiAgICAgICAgICAgIGlucHV0UmVmLnNldEN1c3RvbVZhbGlkaXR5KGlzQm9vbGVhbihtZXNzYWdlKSA/ICcnIDogbWVzc2FnZSB8fCAnJyk7XG4gICAgICAgICAgICBpbnB1dFJlZi5yZXBvcnRWYWxpZGl0eSgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBlcnJvciA9IHt9O1xuICAgIGNvbnN0IGlzUmFkaW8gPSBpc1JhZGlvSW5wdXQocmVmKTtcbiAgICBjb25zdCBpc0NoZWNrQm94ID0gaXNDaGVja0JveElucHV0KHJlZik7XG4gICAgY29uc3QgaXNSYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvIHx8IGlzQ2hlY2tCb3g7XG4gICAgY29uc3QgaXNFbXB0eSA9ICgodmFsdWVBc051bWJlciB8fCBpc0ZpbGVJbnB1dChyZWYpKSAmJlxuICAgICAgICBpc1VuZGVmaW5lZChyZWYudmFsdWUpICYmXG4gICAgICAgIGlzVW5kZWZpbmVkKGlucHV0VmFsdWUpKSB8fFxuICAgICAgICAoaXNIVE1MRWxlbWVudChyZWYpICYmIHJlZi52YWx1ZSA9PT0gJycpIHx8XG4gICAgICAgIGlucHV0VmFsdWUgPT09ICcnIHx8XG4gICAgICAgIChBcnJheS5pc0FycmF5KGlucHV0VmFsdWUpICYmICFpbnB1dFZhbHVlLmxlbmd0aCk7XG4gICAgY29uc3QgYXBwZW5kRXJyb3JzQ3VycnkgPSBhcHBlbmRFcnJvcnMuYmluZChudWxsLCBuYW1lLCB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEsIGVycm9yKTtcbiAgICBjb25zdCBnZXRNaW5NYXhNZXNzYWdlID0gKGV4Y2VlZE1heCwgbWF4TGVuZ3RoTWVzc2FnZSwgbWluTGVuZ3RoTWVzc2FnZSwgbWF4VHlwZSA9IElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4TGVuZ3RoLCBtaW5UeXBlID0gSU5QVVRfVkFMSURBVElPTl9SVUxFUy5taW5MZW5ndGgpID0+IHtcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IGV4Y2VlZE1heCA/IG1heExlbmd0aE1lc3NhZ2UgOiBtaW5MZW5ndGhNZXNzYWdlO1xuICAgICAgICBlcnJvcltuYW1lXSA9IHtcbiAgICAgICAgICAgIHR5cGU6IGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLFxuICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgIHJlZixcbiAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLCBtZXNzYWdlKSxcbiAgICAgICAgfTtcbiAgICB9O1xuICAgIGlmIChpc0ZpZWxkQXJyYXlcbiAgICAgICAgPyAhQXJyYXkuaXNBcnJheShpbnB1dFZhbHVlKSB8fCAhaW5wdXRWYWx1ZS5sZW5ndGhcbiAgICAgICAgOiByZXF1aXJlZCAmJlxuICAgICAgICAgICAgKCghaXNSYWRpb09yQ2hlY2tib3ggJiYgKGlzRW1wdHkgfHwgaXNOdWxsT3JVbmRlZmluZWQoaW5wdXRWYWx1ZSkpKSB8fFxuICAgICAgICAgICAgICAgIChpc0Jvb2xlYW4oaW5wdXRWYWx1ZSkgJiYgIWlucHV0VmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKGlzQ2hlY2tCb3ggJiYgIWdldENoZWNrYm94VmFsdWUocmVmcykuaXNWYWxpZCkgfHxcbiAgICAgICAgICAgICAgICAoaXNSYWRpbyAmJiAhZ2V0UmFkaW9WYWx1ZShyZWZzKS5pc1ZhbGlkKSkpIHtcbiAgICAgICAgY29uc3QgeyB2YWx1ZSwgbWVzc2FnZSB9ID0gaXNNZXNzYWdlKHJlcXVpcmVkKVxuICAgICAgICAgICAgPyB7IHZhbHVlOiAhIXJlcXVpcmVkLCBtZXNzYWdlOiByZXF1aXJlZCB9XG4gICAgICAgICAgICA6IGdldFZhbHVlQW5kTWVzc2FnZShyZXF1aXJlZCk7XG4gICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgdHlwZTogSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCxcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIHJlZjogaW5wdXRSZWYsXG4gICAgICAgICAgICAgICAgLi4uYXBwZW5kRXJyb3JzQ3VycnkoSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCwgbWVzc2FnZSksXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICBzZXRDdXN0b21WYWxpZGl0eShtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKCFpc0VtcHR5ICYmICghaXNOdWxsT3JVbmRlZmluZWQobWluKSB8fCAhaXNOdWxsT3JVbmRlZmluZWQobWF4KSkpIHtcbiAgICAgICAgbGV0IGV4Y2VlZE1heDtcbiAgICAgICAgbGV0IGV4Y2VlZE1pbjtcbiAgICAgICAgY29uc3QgbWF4T3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1heCk7XG4gICAgICAgIGNvbnN0IG1pbk91dHB1dCA9IGdldFZhbHVlQW5kTWVzc2FnZShtaW4pO1xuICAgICAgICBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKGlucHV0VmFsdWUpICYmICFpc05hTihpbnB1dFZhbHVlKSkge1xuICAgICAgICAgICAgY29uc3QgdmFsdWVOdW1iZXIgPSByZWYudmFsdWVBc051bWJlciB8fFxuICAgICAgICAgICAgICAgIChpbnB1dFZhbHVlID8gK2lucHV0VmFsdWUgOiBpbnB1dFZhbHVlKTtcbiAgICAgICAgICAgIGlmICghaXNOdWxsT3JVbmRlZmluZWQobWF4T3V0cHV0LnZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGV4Y2VlZE1heCA9IHZhbHVlTnVtYmVyID4gbWF4T3V0cHV0LnZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc051bGxPclVuZGVmaW5lZChtaW5PdXRwdXQudmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWluID0gdmFsdWVOdW1iZXIgPCBtaW5PdXRwdXQudmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZURhdGUgPSByZWYudmFsdWVBc0RhdGUgfHwgbmV3IERhdGUoaW5wdXRWYWx1ZSk7XG4gICAgICAgICAgICBjb25zdCBjb252ZXJ0VGltZVRvRGF0ZSA9ICh0aW1lKSA9PiBuZXcgRGF0ZShuZXcgRGF0ZSgpLnRvRGF0ZVN0cmluZygpICsgJyAnICsgdGltZSk7XG4gICAgICAgICAgICBjb25zdCBpc1RpbWUgPSByZWYudHlwZSA9PSAndGltZSc7XG4gICAgICAgICAgICBjb25zdCBpc1dlZWsgPSByZWYudHlwZSA9PSAnd2Vlayc7XG4gICAgICAgICAgICBpZiAoaXNTdHJpbmcobWF4T3V0cHV0LnZhbHVlKSAmJiBpbnB1dFZhbHVlKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWF4ID0gaXNUaW1lXG4gICAgICAgICAgICAgICAgICAgID8gY29udmVydFRpbWVUb0RhdGUoaW5wdXRWYWx1ZSkgPiBjb252ZXJ0VGltZVRvRGF0ZShtYXhPdXRwdXQudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgIDogaXNXZWVrXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGlucHV0VmFsdWUgPiBtYXhPdXRwdXQudmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWVEYXRlID4gbmV3IERhdGUobWF4T3V0cHV0LnZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc1N0cmluZyhtaW5PdXRwdXQudmFsdWUpICYmIGlucHV0VmFsdWUpIHtcbiAgICAgICAgICAgICAgICBleGNlZWRNaW4gPSBpc1RpbWVcbiAgICAgICAgICAgICAgICAgICAgPyBjb252ZXJ0VGltZVRvRGF0ZShpbnB1dFZhbHVlKSA8IGNvbnZlcnRUaW1lVG9EYXRlKG1pbk91dHB1dC52YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgOiBpc1dlZWtcbiAgICAgICAgICAgICAgICAgICAgICAgID8gaW5wdXRWYWx1ZSA8IG1pbk91dHB1dC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB2YWx1ZURhdGUgPCBuZXcgRGF0ZShtaW5PdXRwdXQudmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XG4gICAgICAgICAgICBnZXRNaW5NYXhNZXNzYWdlKCEhZXhjZWVkTWF4LCBtYXhPdXRwdXQubWVzc2FnZSwgbWluT3V0cHV0Lm1lc3NhZ2UsIElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4LCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1pbik7XG4gICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KGVycm9yW25hbWVdLm1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoKG1heExlbmd0aCB8fCBtaW5MZW5ndGgpICYmXG4gICAgICAgICFpc0VtcHR5ICYmXG4gICAgICAgIChpc1N0cmluZyhpbnB1dFZhbHVlKSB8fCAoaXNGaWVsZEFycmF5ICYmIEFycmF5LmlzQXJyYXkoaW5wdXRWYWx1ZSkpKSkge1xuICAgICAgICBjb25zdCBtYXhMZW5ndGhPdXRwdXQgPSBnZXRWYWx1ZUFuZE1lc3NhZ2UobWF4TGVuZ3RoKTtcbiAgICAgICAgY29uc3QgbWluTGVuZ3RoT3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1pbkxlbmd0aCk7XG4gICAgICAgIGNvbnN0IGV4Y2VlZE1heCA9ICFpc051bGxPclVuZGVmaW5lZChtYXhMZW5ndGhPdXRwdXQudmFsdWUpICYmXG4gICAgICAgICAgICBpbnB1dFZhbHVlLmxlbmd0aCA+ICttYXhMZW5ndGhPdXRwdXQudmFsdWU7XG4gICAgICAgIGNvbnN0IGV4Y2VlZE1pbiA9ICFpc051bGxPclVuZGVmaW5lZChtaW5MZW5ndGhPdXRwdXQudmFsdWUpICYmXG4gICAgICAgICAgICBpbnB1dFZhbHVlLmxlbmd0aCA8ICttaW5MZW5ndGhPdXRwdXQudmFsdWU7XG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XG4gICAgICAgICAgICBnZXRNaW5NYXhNZXNzYWdlKGV4Y2VlZE1heCwgbWF4TGVuZ3RoT3V0cHV0Lm1lc3NhZ2UsIG1pbkxlbmd0aE91dHB1dC5tZXNzYWdlKTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkoZXJyb3JbbmFtZV0ubWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChwYXR0ZXJuICYmICFpc0VtcHR5ICYmIGlzU3RyaW5nKGlucHV0VmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IHsgdmFsdWU6IHBhdHRlcm5WYWx1ZSwgbWVzc2FnZSB9ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKHBhdHRlcm4pO1xuICAgICAgICBpZiAoaXNSZWdleChwYXR0ZXJuVmFsdWUpICYmICFpbnB1dFZhbHVlLm1hdGNoKHBhdHRlcm5WYWx1ZSkpIHtcbiAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgIHR5cGU6IElOUFVUX1ZBTElEQVRJT05fUlVMRVMucGF0dGVybixcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIHJlZixcbiAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnBhdHRlcm4sIG1lc3NhZ2UpLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkobWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh2YWxpZGF0ZSkge1xuICAgICAgICBpZiAoaXNGdW5jdGlvbih2YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHZhbGlkYXRlKGlucHV0VmFsdWUsIGZvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgY29uc3QgdmFsaWRhdGVFcnJvciA9IGdldFZhbGlkYXRlRXJyb3IocmVzdWx0LCBpbnB1dFJlZik7XG4gICAgICAgICAgICBpZiAodmFsaWRhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgICAgICAuLi52YWxpZGF0ZUVycm9yLFxuICAgICAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnZhbGlkYXRlLCB2YWxpZGF0ZUVycm9yLm1lc3NhZ2UpLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkodmFsaWRhdGVFcnJvci5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc09iamVjdCh2YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgIGxldCB2YWxpZGF0aW9uUmVzdWx0ID0ge307XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiB2YWxpZGF0ZSkge1xuICAgICAgICAgICAgICAgIGlmICghaXNFbXB0eU9iamVjdCh2YWxpZGF0aW9uUmVzdWx0KSAmJiAhdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCB2YWxpZGF0ZUVycm9yID0gZ2V0VmFsaWRhdGVFcnJvcihhd2FpdCB2YWxpZGF0ZVtrZXldKGlucHV0VmFsdWUsIGZvcm1WYWx1ZXMpLCBpbnB1dFJlZiwga2V5KTtcbiAgICAgICAgICAgICAgICBpZiAodmFsaWRhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICB2YWxpZGF0aW9uUmVzdWx0ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4udmFsaWRhdGVFcnJvcixcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KGtleSwgdmFsaWRhdGVFcnJvci5tZXNzYWdlKSxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkodmFsaWRhdGVFcnJvci5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB2YWxpZGF0aW9uUmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc0VtcHR5T2JqZWN0KHZhbGlkYXRpb25SZXN1bHQpKSB7XG4gICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZjogaW5wdXRSZWYsXG4gICAgICAgICAgICAgICAgICAgIC4uLnZhbGlkYXRpb25SZXN1bHQsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNldEN1c3RvbVZhbGlkaXR5KHRydWUpO1xuICAgIHJldHVybiBlcnJvcjtcbn07XG5cbmNvbnN0IGRlZmF1bHRPcHRpb25zID0ge1xuICAgIG1vZGU6IFZBTElEQVRJT05fTU9ERS5vblN1Ym1pdCxcbiAgICByZVZhbGlkYXRlTW9kZTogVkFMSURBVElPTl9NT0RFLm9uQ2hhbmdlLFxuICAgIHNob3VsZEZvY3VzRXJyb3I6IHRydWUsXG59O1xuZnVuY3Rpb24gY3JlYXRlRm9ybUNvbnRyb2wocHJvcHMgPSB7fSkge1xuICAgIGxldCBfb3B0aW9ucyA9IHtcbiAgICAgICAgLi4uZGVmYXVsdE9wdGlvbnMsXG4gICAgICAgIC4uLnByb3BzLFxuICAgIH07XG4gICAgbGV0IF9mb3JtU3RhdGUgPSB7XG4gICAgICAgIHN1Ym1pdENvdW50OiAwLFxuICAgICAgICBpc0RpcnR5OiBmYWxzZSxcbiAgICAgICAgaXNSZWFkeTogZmFsc2UsXG4gICAgICAgIGlzTG9hZGluZzogaXNGdW5jdGlvbihfb3B0aW9ucy5kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXR0ZWQ6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczoge30sXG4gICAgICAgIGRpcnR5RmllbGRzOiB7fSxcbiAgICAgICAgdmFsaWRhdGluZ0ZpZWxkczoge30sXG4gICAgICAgIGVycm9yczogX29wdGlvbnMuZXJyb3JzIHx8IHt9LFxuICAgICAgICBkaXNhYmxlZDogX29wdGlvbnMuZGlzYWJsZWQgfHwgZmFsc2UsXG4gICAgfTtcbiAgICBsZXQgX2ZpZWxkcyA9IHt9O1xuICAgIGxldCBfZGVmYXVsdFZhbHVlcyA9IGlzT2JqZWN0KF9vcHRpb25zLmRlZmF1bHRWYWx1ZXMpIHx8IGlzT2JqZWN0KF9vcHRpb25zLnZhbHVlcylcbiAgICAgICAgPyBjbG9uZU9iamVjdChfb3B0aW9ucy5kZWZhdWx0VmFsdWVzIHx8IF9vcHRpb25zLnZhbHVlcykgfHwge31cbiAgICAgICAgOiB7fTtcbiAgICBsZXQgX2Zvcm1WYWx1ZXMgPSBfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyXG4gICAgICAgID8ge31cbiAgICAgICAgOiBjbG9uZU9iamVjdChfZGVmYXVsdFZhbHVlcyk7XG4gICAgbGV0IF9zdGF0ZSA9IHtcbiAgICAgICAgYWN0aW9uOiBmYWxzZSxcbiAgICAgICAgbW91bnQ6IGZhbHNlLFxuICAgICAgICB3YXRjaDogZmFsc2UsXG4gICAgfTtcbiAgICBsZXQgX25hbWVzID0ge1xuICAgICAgICBtb3VudDogbmV3IFNldCgpLFxuICAgICAgICBkaXNhYmxlZDogbmV3IFNldCgpLFxuICAgICAgICB1bk1vdW50OiBuZXcgU2V0KCksXG4gICAgICAgIGFycmF5OiBuZXcgU2V0KCksXG4gICAgICAgIHdhdGNoOiBuZXcgU2V0KCksXG4gICAgfTtcbiAgICBsZXQgZGVsYXlFcnJvckNhbGxiYWNrO1xuICAgIGxldCB0aW1lciA9IDA7XG4gICAgY29uc3QgX3Byb3h5Rm9ybVN0YXRlID0ge1xuICAgICAgICBpc0RpcnR5OiBmYWxzZSxcbiAgICAgICAgZGlydHlGaWVsZHM6IGZhbHNlLFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiBmYWxzZSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczogZmFsc2UsXG4gICAgICAgIGlzVmFsaWRhdGluZzogZmFsc2UsXG4gICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICBlcnJvcnM6IGZhbHNlLFxuICAgIH07XG4gICAgbGV0IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZSA9IHtcbiAgICAgICAgLi4uX3Byb3h5Rm9ybVN0YXRlLFxuICAgIH07XG4gICAgY29uc3QgX3N1YmplY3RzID0ge1xuICAgICAgICBhcnJheTogY3JlYXRlU3ViamVjdCgpLFxuICAgICAgICBzdGF0ZTogY3JlYXRlU3ViamVjdCgpLFxuICAgIH07XG4gICAgY29uc3Qgc2hvdWxkRGlzcGxheUFsbEFzc29jaWF0ZWRFcnJvcnMgPSBfb3B0aW9ucy5jcml0ZXJpYU1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5hbGw7XG4gICAgY29uc3QgZGVib3VuY2UgPSAoY2FsbGJhY2spID0+ICh3YWl0KSA9PiB7XG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgICAgIHRpbWVyID0gc2V0VGltZW91dChjYWxsYmFjaywgd2FpdCk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0VmFsaWQgPSBhc3luYyAoc2hvdWxkVXBkYXRlVmFsaWQpID0+IHtcbiAgICAgICAgaWYgKCFfb3B0aW9ucy5kaXNhYmxlZCAmJlxuICAgICAgICAgICAgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVWYWxpZCkpIHtcbiAgICAgICAgICAgIGNvbnN0IGlzVmFsaWQgPSBfb3B0aW9ucy5yZXNvbHZlclxuICAgICAgICAgICAgICAgID8gaXNFbXB0eU9iamVjdCgoYXdhaXQgX3J1blNjaGVtYSgpKS5lcnJvcnMpXG4gICAgICAgICAgICAgICAgOiBhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oX2ZpZWxkcywgdHJ1ZSk7XG4gICAgICAgICAgICBpZiAoaXNWYWxpZCAhPT0gX2Zvcm1TdGF0ZS5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfdXBkYXRlSXNWYWxpZGF0aW5nID0gKG5hbWVzLCBpc1ZhbGlkYXRpbmcpID0+IHtcbiAgICAgICAgaWYgKCFfb3B0aW9ucy5kaXNhYmxlZCAmJlxuICAgICAgICAgICAgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkYXRpbmcgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkYXRpbmcgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcykpIHtcbiAgICAgICAgICAgIChuYW1lcyB8fCBBcnJheS5mcm9tKF9uYW1lcy5tb3VudCkpLmZvckVhY2goKG5hbWUpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAobmFtZSkge1xuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkYXRpbmdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcywgbmFtZSwgaXNWYWxpZGF0aW5nKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB1bnNldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcyxcbiAgICAgICAgICAgICAgICBpc1ZhbGlkYXRpbmc6ICFpc0VtcHR5T2JqZWN0KF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcyksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgX3NldEZpZWxkQXJyYXkgPSAobmFtZSwgdmFsdWVzID0gW10sIG1ldGhvZCwgYXJncywgc2hvdWxkU2V0VmFsdWVzID0gdHJ1ZSwgc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgPSB0cnVlKSA9PiB7XG4gICAgICAgIGlmIChhcmdzICYmIG1ldGhvZCAmJiAhX29wdGlvbnMuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgIF9zdGF0ZS5hY3Rpb24gPSB0cnVlO1xuICAgICAgICAgICAgaWYgKHNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlICYmIEFycmF5LmlzQXJyYXkoZ2V0KF9maWVsZHMsIG5hbWUpKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWVzID0gbWV0aG9kKGdldChfZmllbGRzLCBuYW1lKSwgYXJncy5hcmdBLCBhcmdzLmFyZ0IpO1xuICAgICAgICAgICAgICAgIHNob3VsZFNldFZhbHVlcyAmJiBzZXQoX2ZpZWxkcywgbmFtZSwgZmllbGRWYWx1ZXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlICYmXG4gICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9ycyA9IG1ldGhvZChnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpLCBhcmdzLmFyZ0EsIGFyZ3MuYXJnQik7XG4gICAgICAgICAgICAgICAgc2hvdWxkU2V0VmFsdWVzICYmIHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3JzKTtcbiAgICAgICAgICAgICAgICB1bnNldEVtcHR5QXJyYXkoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKChfcHJveHlGb3JtU3RhdGUudG91Y2hlZEZpZWxkcyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzKSAmJlxuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlICYmXG4gICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShnZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKSkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB0b3VjaGVkRmllbGRzID0gbWV0aG9kKGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpLCBhcmdzLmFyZ0EsIGFyZ3MuYXJnQik7XG4gICAgICAgICAgICAgICAgc2hvdWxkU2V0VmFsdWVzICYmIHNldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUsIHRvdWNoZWRGaWVsZHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKF9wcm94eUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcyB8fCBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuZGlydHlGaWVsZHMpIHtcbiAgICAgICAgICAgICAgICBfZm9ybVN0YXRlLmRpcnR5RmllbGRzID0gZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgIGlzRGlydHk6IF9nZXREaXJ0eShuYW1lLCB2YWx1ZXMpLFxuICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzOiBfZm9ybVN0YXRlLmRpcnR5RmllbGRzLFxuICAgICAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgICAgICAgICAgaXNWYWxpZDogX2Zvcm1TdGF0ZS5pc1ZhbGlkLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIHZhbHVlcyk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZUVycm9ycyA9IChuYW1lLCBlcnJvcikgPT4ge1xuICAgICAgICBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIGVycm9yKTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RXJyb3JzID0gKGVycm9ycykgPT4ge1xuICAgICAgICBfZm9ybVN0YXRlLmVycm9ycyA9IGVycm9ycztcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZVZhbGlkQW5kVmFsdWUgPSAobmFtZSwgc2hvdWxkU2tpcFNldFZhbHVlQXMsIHZhbHVlLCByZWYpID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgZGVmYXVsdFZhbHVlID0gZ2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBpc1VuZGVmaW5lZCh2YWx1ZSkgPyBnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpIDogdmFsdWUpO1xuICAgICAgICAgICAgaXNVbmRlZmluZWQoZGVmYXVsdFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgIChyZWYgJiYgcmVmLmRlZmF1bHRDaGVja2VkKSB8fFxuICAgICAgICAgICAgICAgIHNob3VsZFNraXBTZXRWYWx1ZUFzXG4gICAgICAgICAgICAgICAgPyBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIHNob3VsZFNraXBTZXRWYWx1ZUFzID8gZGVmYXVsdFZhbHVlIDogZ2V0RmllbGRWYWx1ZShmaWVsZC5fZikpXG4gICAgICAgICAgICAgICAgOiBzZXRGaWVsZFZhbHVlKG5hbWUsIGRlZmF1bHRWYWx1ZSk7XG4gICAgICAgICAgICBfc3RhdGUubW91bnQgJiYgX3NldFZhbGlkKCk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZVRvdWNoQW5kRGlydHkgPSAobmFtZSwgZmllbGRWYWx1ZSwgaXNCbHVyRXZlbnQsIHNob3VsZERpcnR5LCBzaG91bGRSZW5kZXIpID0+IHtcbiAgICAgICAgbGV0IHNob3VsZFVwZGF0ZUZpZWxkID0gZmFsc2U7XG4gICAgICAgIGxldCBpc1ByZXZpb3VzRGlydHkgPSBmYWxzZTtcbiAgICAgICAgY29uc3Qgb3V0cHV0ID0ge1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKCFfb3B0aW9ucy5kaXNhYmxlZCkge1xuICAgICAgICAgICAgaWYgKCFpc0JsdXJFdmVudCB8fCBzaG91bGREaXJ0eSkge1xuICAgICAgICAgICAgICAgIGlmIChfcHJveHlGb3JtU3RhdGUuaXNEaXJ0eSB8fCBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNEaXJ0eSkge1xuICAgICAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRGlydHkgPSBfZm9ybVN0YXRlLmlzRGlydHk7XG4gICAgICAgICAgICAgICAgICAgIF9mb3JtU3RhdGUuaXNEaXJ0eSA9IG91dHB1dC5pc0RpcnR5ID0gX2dldERpcnR5KCk7XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkID0gaXNQcmV2aW91c0RpcnR5ICE9PSBvdXRwdXQuaXNEaXJ0eTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50RmllbGRQcmlzdGluZSA9IGRlZXBFcXVhbChnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpLCBmaWVsZFZhbHVlKTtcbiAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRGlydHkgPSAhIWdldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpc0N1cnJlbnRGaWVsZFByaXN0aW5lXG4gICAgICAgICAgICAgICAgICAgID8gdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSlcbiAgICAgICAgICAgICAgICAgICAgOiBzZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgb3V0cHV0LmRpcnR5RmllbGRzID0gX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcztcbiAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCA9XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAoKF9wcm94eUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcyB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcykgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRGlydHkgIT09ICFpc0N1cnJlbnRGaWVsZFByaXN0aW5lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc0JsdXJFdmVudCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzUHJldmlvdXNGaWVsZFRvdWNoZWQgPSBnZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoIWlzUHJldmlvdXNGaWVsZFRvdWNoZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSwgaXNCbHVyRXZlbnQpO1xuICAgICAgICAgICAgICAgICAgICBvdXRwdXQudG91Y2hlZEZpZWxkcyA9IF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcztcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgPVxuICAgICAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoKF9wcm94eUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzKSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRmllbGRUb3VjaGVkICE9PSBpc0JsdXJFdmVudCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgJiYgc2hvdWxkUmVuZGVyICYmIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KG91dHB1dCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNob3VsZFVwZGF0ZUZpZWxkID8gb3V0cHV0IDoge307XG4gICAgfTtcbiAgICBjb25zdCBzaG91bGRSZW5kZXJCeUVycm9yID0gKG5hbWUsIGlzVmFsaWQsIGVycm9yLCBmaWVsZFN0YXRlKSA9PiB7XG4gICAgICAgIGNvbnN0IHByZXZpb3VzRmllbGRFcnJvciA9IGdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgIGNvbnN0IHNob3VsZFVwZGF0ZVZhbGlkID0gKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkKSAmJlxuICAgICAgICAgICAgaXNCb29sZWFuKGlzVmFsaWQpICYmXG4gICAgICAgICAgICBfZm9ybVN0YXRlLmlzVmFsaWQgIT09IGlzVmFsaWQ7XG4gICAgICAgIGlmIChfb3B0aW9ucy5kZWxheUVycm9yICYmIGVycm9yKSB7XG4gICAgICAgICAgICBkZWxheUVycm9yQ2FsbGJhY2sgPSBkZWJvdW5jZSgoKSA9PiB1cGRhdGVFcnJvcnMobmFtZSwgZXJyb3IpKTtcbiAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayhfb3B0aW9ucy5kZWxheUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgICAgICAgICBkZWxheUVycm9yQ2FsbGJhY2sgPSBudWxsO1xuICAgICAgICAgICAgZXJyb3JcbiAgICAgICAgICAgICAgICA/IHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3IpXG4gICAgICAgICAgICAgICAgOiB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKChlcnJvciA/ICFkZWVwRXF1YWwocHJldmlvdXNGaWVsZEVycm9yLCBlcnJvcikgOiBwcmV2aW91c0ZpZWxkRXJyb3IpIHx8XG4gICAgICAgICAgICAhaXNFbXB0eU9iamVjdChmaWVsZFN0YXRlKSB8fFxuICAgICAgICAgICAgc2hvdWxkVXBkYXRlVmFsaWQpIHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRGb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAgICAgLi4uZmllbGRTdGF0ZSxcbiAgICAgICAgICAgICAgICAuLi4oc2hvdWxkVXBkYXRlVmFsaWQgJiYgaXNCb29sZWFuKGlzVmFsaWQpID8geyBpc1ZhbGlkIH0gOiB7fSksXG4gICAgICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIF9mb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAgICAgLi4uX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgICAgICAuLi51cGRhdGVkRm9ybVN0YXRlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHVwZGF0ZWRGb3JtU3RhdGUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfcnVuU2NoZW1hID0gYXN5bmMgKG5hbWUpID0+IHtcbiAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhuYW1lLCB0cnVlKTtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgX29wdGlvbnMucmVzb2x2ZXIoX2Zvcm1WYWx1ZXMsIF9vcHRpb25zLmNvbnRleHQsIGdldFJlc29sdmVyT3B0aW9ucyhuYW1lIHx8IF9uYW1lcy5tb3VudCwgX2ZpZWxkcywgX29wdGlvbnMuY3JpdGVyaWFNb2RlLCBfb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSk7XG4gICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcobmFtZSk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbiAgICBjb25zdCBleGVjdXRlU2NoZW1hQW5kVXBkYXRlU3RhdGUgPSBhc3luYyAobmFtZXMpID0+IHtcbiAgICAgICAgY29uc3QgeyBlcnJvcnMgfSA9IGF3YWl0IF9ydW5TY2hlbWEobmFtZXMpO1xuICAgICAgICBpZiAobmFtZXMpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBuYW1lcykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZ2V0KGVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgZXJyb3JcbiAgICAgICAgICAgICAgICAgICAgPyBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIGVycm9yKVxuICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIF9mb3JtU3RhdGUuZXJyb3JzID0gZXJyb3JzO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBlcnJvcnM7XG4gICAgfTtcbiAgICBjb25zdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24gPSBhc3luYyAoZmllbGRzLCBzaG91bGRPbmx5Q2hlY2tWYWxpZCwgY29udGV4dCA9IHtcbiAgICAgICAgdmFsaWQ6IHRydWUsXG4gICAgfSkgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IG5hbWUgaW4gZmllbGRzKSB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZCA9IGZpZWxkc1tuYW1lXTtcbiAgICAgICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgX2YsIC4uLmZpZWxkVmFsdWUgfSA9IGZpZWxkO1xuICAgICAgICAgICAgICAgIGlmIChfZikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0ZpZWxkQXJyYXlSb290ID0gX25hbWVzLmFycmF5LmhhcyhfZi5uYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNQcm9taXNlRnVuY3Rpb24gPSBmaWVsZC5fZiAmJiBoYXNQcm9taXNlVmFsaWRhdGlvbihmaWVsZC5fZik7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc1Byb21pc2VGdW5jdGlvbiAmJiBfcHJveHlGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0sIHRydWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkRXJyb3IgPSBhd2FpdCB2YWxpZGF0ZUZpZWxkKGZpZWxkLCBfbmFtZXMuZGlzYWJsZWQsIF9mb3JtVmFsdWVzLCBzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycywgX29wdGlvbnMuc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbiAmJiAhc2hvdWxkT25seUNoZWNrVmFsaWQsIGlzRmllbGRBcnJheVJvb3QpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaXNQcm9taXNlRnVuY3Rpb24gJiYgX3Byb3h5Rm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcoW25hbWVdKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoZmllbGRFcnJvcltfZi5uYW1lXSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC52YWxpZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHNob3VsZE9ubHlDaGVja1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgIXNob3VsZE9ubHlDaGVja1ZhbGlkICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAoZ2V0KGZpZWxkRXJyb3IsIF9mLm5hbWUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBpc0ZpZWxkQXJyYXlSb290XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdXBkYXRlRmllbGRBcnJheVJvb3RFcnJvcihfZm9ybVN0YXRlLmVycm9ycywgZmllbGRFcnJvciwgX2YubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIF9mLm5hbWUsIGZpZWxkRXJyb3JbX2YubmFtZV0pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgX2YubmFtZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAhaXNFbXB0eU9iamVjdChmaWVsZFZhbHVlKSAmJlxuICAgICAgICAgICAgICAgICAgICAoYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKGZpZWxkVmFsdWUsIHNob3VsZE9ubHlDaGVja1ZhbGlkLCBjb250ZXh0KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbnRleHQudmFsaWQ7XG4gICAgfTtcbiAgICBjb25zdCBfcmVtb3ZlVW5tb3VudGVkID0gKCkgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IG5hbWUgb2YgX25hbWVzLnVuTW91bnQpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgZmllbGQgJiZcbiAgICAgICAgICAgICAgICAoZmllbGQuX2YucmVmc1xuICAgICAgICAgICAgICAgICAgICA/IGZpZWxkLl9mLnJlZnMuZXZlcnkoKHJlZikgPT4gIWxpdmUocmVmKSlcbiAgICAgICAgICAgICAgICAgICAgOiAhbGl2ZShmaWVsZC5fZi5yZWYpKSAmJlxuICAgICAgICAgICAgICAgIHVucmVnaXN0ZXIobmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgX25hbWVzLnVuTW91bnQgPSBuZXcgU2V0KCk7XG4gICAgfTtcbiAgICBjb25zdCBfZ2V0RGlydHkgPSAobmFtZSwgZGF0YSkgPT4gIV9vcHRpb25zLmRpc2FibGVkICYmXG4gICAgICAgIChuYW1lICYmIGRhdGEgJiYgc2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBkYXRhKSxcbiAgICAgICAgICAgICFkZWVwRXF1YWwoZ2V0VmFsdWVzKCksIF9kZWZhdWx0VmFsdWVzKSk7XG4gICAgY29uc3QgX2dldFdhdGNoID0gKG5hbWVzLCBkZWZhdWx0VmFsdWUsIGlzR2xvYmFsKSA9PiBnZW5lcmF0ZVdhdGNoT3V0cHV0KG5hbWVzLCBfbmFtZXMsIHtcbiAgICAgICAgLi4uKF9zdGF0ZS5tb3VudFxuICAgICAgICAgICAgPyBfZm9ybVZhbHVlc1xuICAgICAgICAgICAgOiBpc1VuZGVmaW5lZChkZWZhdWx0VmFsdWUpXG4gICAgICAgICAgICAgICAgPyBfZGVmYXVsdFZhbHVlc1xuICAgICAgICAgICAgICAgIDogaXNTdHJpbmcobmFtZXMpXG4gICAgICAgICAgICAgICAgICAgID8geyBbbmFtZXNdOiBkZWZhdWx0VmFsdWUgfVxuICAgICAgICAgICAgICAgICAgICA6IGRlZmF1bHRWYWx1ZSksXG4gICAgfSwgaXNHbG9iYWwsIGRlZmF1bHRWYWx1ZSk7XG4gICAgY29uc3QgX2dldEZpZWxkQXJyYXkgPSAobmFtZSkgPT4gY29tcGFjdChnZXQoX3N0YXRlLm1vdW50ID8gX2Zvcm1WYWx1ZXMgOiBfZGVmYXVsdFZhbHVlcywgbmFtZSwgX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciA/IGdldChfZGVmYXVsdFZhbHVlcywgbmFtZSwgW10pIDogW10pKTtcbiAgICBjb25zdCBzZXRGaWVsZFZhbHVlID0gKG5hbWUsIHZhbHVlLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGxldCBmaWVsZFZhbHVlID0gdmFsdWU7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBmaWVsZC5fZjtcbiAgICAgICAgICAgIGlmIChmaWVsZFJlZmVyZW5jZSkge1xuICAgICAgICAgICAgICAgICFmaWVsZFJlZmVyZW5jZS5kaXNhYmxlZCAmJlxuICAgICAgICAgICAgICAgICAgICBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGdldEZpZWxkVmFsdWVBcyh2YWx1ZSwgZmllbGRSZWZlcmVuY2UpKTtcbiAgICAgICAgICAgICAgICBmaWVsZFZhbHVlID1cbiAgICAgICAgICAgICAgICAgICAgaXNIVE1MRWxlbWVudChmaWVsZFJlZmVyZW5jZS5yZWYpICYmIGlzTnVsbE9yVW5kZWZpbmVkKHZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiB2YWx1ZTtcbiAgICAgICAgICAgICAgICBpZiAoaXNNdWx0aXBsZVNlbGVjdChmaWVsZFJlZmVyZW5jZS5yZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgIFsuLi5maWVsZFJlZmVyZW5jZS5yZWYub3B0aW9uc10uZm9yRWFjaCgob3B0aW9uUmVmKSA9PiAob3B0aW9uUmVmLnNlbGVjdGVkID0gZmllbGRWYWx1ZS5pbmNsdWRlcyhvcHRpb25SZWYudmFsdWUpKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGZpZWxkUmVmZXJlbmNlLnJlZnMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzQ2hlY2tCb3hJbnB1dChmaWVsZFJlZmVyZW5jZS5yZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZmVyZW5jZS5yZWZzLmZvckVhY2goKGNoZWNrYm94UmVmKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFjaGVja2JveFJlZi5kZWZhdWx0Q2hlY2tlZCB8fCAhY2hlY2tib3hSZWYuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmllbGRWYWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrYm94UmVmLmNoZWNrZWQgPSAhIWZpZWxkVmFsdWUuZmluZCgoZGF0YSkgPT4gZGF0YSA9PT0gY2hlY2tib3hSZWYudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tib3hSZWYuY2hlY2tlZCA9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRWYWx1ZSA9PT0gY2hlY2tib3hSZWYudmFsdWUgfHwgISFmaWVsZFZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZmVyZW5jZS5yZWZzLmZvckVhY2goKHJhZGlvUmVmKSA9PiAocmFkaW9SZWYuY2hlY2tlZCA9IHJhZGlvUmVmLnZhbHVlID09PSBmaWVsZFZhbHVlKSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoaXNGaWxlSW5wdXQoZmllbGRSZWZlcmVuY2UucmVmKSkge1xuICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZmVyZW5jZS5yZWYudmFsdWUgPSAnJztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZi52YWx1ZSA9IGZpZWxkVmFsdWU7XG4gICAgICAgICAgICAgICAgICAgIGlmICghZmllbGRSZWZlcmVuY2UucmVmLnR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgKG9wdGlvbnMuc2hvdWxkRGlydHkgfHwgb3B0aW9ucy5zaG91bGRUb3VjaCkgJiZcbiAgICAgICAgICAgIHVwZGF0ZVRvdWNoQW5kRGlydHkobmFtZSwgZmllbGRWYWx1ZSwgb3B0aW9ucy5zaG91bGRUb3VjaCwgb3B0aW9ucy5zaG91bGREaXJ0eSwgdHJ1ZSk7XG4gICAgICAgIG9wdGlvbnMuc2hvdWxkVmFsaWRhdGUgJiYgdHJpZ2dlcihuYW1lKTtcbiAgICB9O1xuICAgIGNvbnN0IHNldFZhbHVlcyA9IChuYW1lLCB2YWx1ZSwgb3B0aW9ucykgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IGZpZWxkS2V5IGluIHZhbHVlKSB7XG4gICAgICAgICAgICBpZiAoIXZhbHVlLmhhc093blByb3BlcnR5KGZpZWxkS2V5KSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWUgPSB2YWx1ZVtmaWVsZEtleV07XG4gICAgICAgICAgICBjb25zdCBmaWVsZE5hbWUgPSBuYW1lICsgJy4nICsgZmllbGRLZXk7XG4gICAgICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBmaWVsZE5hbWUpO1xuICAgICAgICAgICAgKF9uYW1lcy5hcnJheS5oYXMobmFtZSkgfHxcbiAgICAgICAgICAgICAgICBpc09iamVjdChmaWVsZFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgIChmaWVsZCAmJiAhZmllbGQuX2YpKSAmJlxuICAgICAgICAgICAgICAgICFpc0RhdGVPYmplY3QoZmllbGRWYWx1ZSlcbiAgICAgICAgICAgICAgICA/IHNldFZhbHVlcyhmaWVsZE5hbWUsIGZpZWxkVmFsdWUsIG9wdGlvbnMpXG4gICAgICAgICAgICAgICAgOiBzZXRGaWVsZFZhbHVlKGZpZWxkTmFtZSwgZmllbGRWYWx1ZSwgb3B0aW9ucyk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHNldFZhbHVlID0gKG5hbWUsIHZhbHVlLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGNvbnN0IGlzRmllbGRBcnJheSA9IF9uYW1lcy5hcnJheS5oYXMobmFtZSk7XG4gICAgICAgIGNvbnN0IGNsb25lVmFsdWUgPSBjbG9uZU9iamVjdCh2YWx1ZSk7XG4gICAgICAgIHNldChfZm9ybVZhbHVlcywgbmFtZSwgY2xvbmVWYWx1ZSk7XG4gICAgICAgIGlmIChpc0ZpZWxkQXJyYXkpIHtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5hcnJheS5uZXh0KHtcbiAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoKF9wcm94eUZvcm1TdGF0ZS5pc0RpcnR5IHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5Rm9ybVN0YXRlLmRpcnR5RmllbGRzIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzRGlydHkgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuZGlydHlGaWVsZHMpICYmXG4gICAgICAgICAgICAgICAgb3B0aW9ucy5zaG91bGREaXJ0eSkge1xuICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgZGlydHlGaWVsZHM6IGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICAgICAgICAgIGlzRGlydHk6IF9nZXREaXJ0eShuYW1lLCBjbG9uZVZhbHVlKSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGZpZWxkICYmICFmaWVsZC5fZiAmJiAhaXNOdWxsT3JVbmRlZmluZWQoY2xvbmVWYWx1ZSlcbiAgICAgICAgICAgICAgICA/IHNldFZhbHVlcyhuYW1lLCBjbG9uZVZhbHVlLCBvcHRpb25zKVxuICAgICAgICAgICAgICAgIDogc2V0RmllbGRWYWx1ZShuYW1lLCBjbG9uZVZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICBpc1dhdGNoZWQobmFtZSwgX25hbWVzKSAmJiBfc3ViamVjdHMuc3RhdGUubmV4dCh7IC4uLl9mb3JtU3RhdGUsIG5hbWUgfSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIG5hbWU6IF9zdGF0ZS5tb3VudCA/IG5hbWUgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBvbkNoYW5nZSA9IGFzeW5jIChldmVudCkgPT4ge1xuICAgICAgICBfc3RhdGUubW91bnQgPSB0cnVlO1xuICAgICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQ7XG4gICAgICAgIGxldCBuYW1lID0gdGFyZ2V0Lm5hbWU7XG4gICAgICAgIGxldCBpc0ZpZWxkVmFsdWVVcGRhdGVkID0gdHJ1ZTtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGNvbnN0IF91cGRhdGVJc0ZpZWxkVmFsdWVVcGRhdGVkID0gKGZpZWxkVmFsdWUpID0+IHtcbiAgICAgICAgICAgIGlzRmllbGRWYWx1ZVVwZGF0ZWQgPVxuICAgICAgICAgICAgICAgIE51bWJlci5pc05hTihmaWVsZFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgICAgICAoaXNEYXRlT2JqZWN0KGZpZWxkVmFsdWUpICYmIGlzTmFOKGZpZWxkVmFsdWUuZ2V0VGltZSgpKSkgfHxcbiAgICAgICAgICAgICAgICAgICAgZGVlcEVxdWFsKGZpZWxkVmFsdWUsIGdldChfZm9ybVZhbHVlcywgbmFtZSwgZmllbGRWYWx1ZSkpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCB2YWxpZGF0aW9uTW9kZUJlZm9yZVN1Ym1pdCA9IGdldFZhbGlkYXRpb25Nb2Rlcyhfb3B0aW9ucy5tb2RlKTtcbiAgICAgICAgY29uc3QgdmFsaWRhdGlvbk1vZGVBZnRlclN1Ym1pdCA9IGdldFZhbGlkYXRpb25Nb2Rlcyhfb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgbGV0IGVycm9yO1xuICAgICAgICAgICAgbGV0IGlzVmFsaWQ7XG4gICAgICAgICAgICBjb25zdCBmaWVsZFZhbHVlID0gdGFyZ2V0LnR5cGVcbiAgICAgICAgICAgICAgICA/IGdldEZpZWxkVmFsdWUoZmllbGQuX2YpXG4gICAgICAgICAgICAgICAgOiBnZXRFdmVudFZhbHVlKGV2ZW50KTtcbiAgICAgICAgICAgIGNvbnN0IGlzQmx1ckV2ZW50ID0gZXZlbnQudHlwZSA9PT0gRVZFTlRTLkJMVVIgfHwgZXZlbnQudHlwZSA9PT0gRVZFTlRTLkZPQ1VTX09VVDtcbiAgICAgICAgICAgIGNvbnN0IHNob3VsZFNraXBWYWxpZGF0aW9uID0gKCFoYXNWYWxpZGF0aW9uKGZpZWxkLl9mKSAmJlxuICAgICAgICAgICAgICAgICFfb3B0aW9ucy5yZXNvbHZlciAmJlxuICAgICAgICAgICAgICAgICFnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpICYmXG4gICAgICAgICAgICAgICAgIWZpZWxkLl9mLmRlcHMpIHx8XG4gICAgICAgICAgICAgICAgc2tpcFZhbGlkYXRpb24oaXNCbHVyRXZlbnQsIGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpLCBfZm9ybVN0YXRlLmlzU3VibWl0dGVkLCB2YWxpZGF0aW9uTW9kZUFmdGVyU3VibWl0LCB2YWxpZGF0aW9uTW9kZUJlZm9yZVN1Ym1pdCk7XG4gICAgICAgICAgICBjb25zdCB3YXRjaGVkID0gaXNXYXRjaGVkKG5hbWUsIF9uYW1lcywgaXNCbHVyRXZlbnQpO1xuICAgICAgICAgICAgc2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBmaWVsZFZhbHVlKTtcbiAgICAgICAgICAgIGlmIChpc0JsdXJFdmVudCkge1xuICAgICAgICAgICAgICAgIGZpZWxkLl9mLm9uQmx1ciAmJiBmaWVsZC5fZi5vbkJsdXIoZXZlbnQpO1xuICAgICAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayAmJiBkZWxheUVycm9yQ2FsbGJhY2soMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChmaWVsZC5fZi5vbkNoYW5nZSkge1xuICAgICAgICAgICAgICAgIGZpZWxkLl9mLm9uQ2hhbmdlKGV2ZW50KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGZpZWxkU3RhdGUgPSB1cGRhdGVUb3VjaEFuZERpcnR5KG5hbWUsIGZpZWxkVmFsdWUsIGlzQmx1ckV2ZW50KTtcbiAgICAgICAgICAgIGNvbnN0IHNob3VsZFJlbmRlciA9ICFpc0VtcHR5T2JqZWN0KGZpZWxkU3RhdGUpIHx8IHdhdGNoZWQ7XG4gICAgICAgICAgICAhaXNCbHVyRXZlbnQgJiZcbiAgICAgICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IGV2ZW50LnR5cGUsXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHNob3VsZFNraXBWYWxpZGF0aW9uKSB7XG4gICAgICAgICAgICAgICAgaWYgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChfb3B0aW9ucy5tb2RlID09PSAnb25CbHVyJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgX3NldFZhbGlkKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoIWlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfc2V0VmFsaWQoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gKHNob3VsZFJlbmRlciAmJlxuICAgICAgICAgICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7IG5hbWUsIC4uLih3YXRjaGVkID8ge30gOiBmaWVsZFN0YXRlKSB9KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAhaXNCbHVyRXZlbnQgJiYgd2F0Y2hlZCAmJiBfc3ViamVjdHMuc3RhdGUubmV4dCh7IC4uLl9mb3JtU3RhdGUgfSk7XG4gICAgICAgICAgICBpZiAoX29wdGlvbnMucmVzb2x2ZXIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGVycm9ycyB9ID0gYXdhaXQgX3J1blNjaGVtYShbbmFtZV0pO1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc0ZpZWxkVmFsdWVVcGRhdGVkKGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgICAgIGlmIChpc0ZpZWxkVmFsdWVVcGRhdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByZXZpb3VzRXJyb3JMb29rdXBSZXN1bHQgPSBzY2hlbWFFcnJvckxvb2t1cChfZm9ybVN0YXRlLmVycm9ycywgX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yTG9va3VwUmVzdWx0ID0gc2NoZW1hRXJyb3JMb29rdXAoZXJyb3JzLCBfZmllbGRzLCBwcmV2aW91c0Vycm9yTG9va3VwUmVzdWx0Lm5hbWUgfHwgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yID0gZXJyb3JMb29rdXBSZXN1bHQuZXJyb3I7XG4gICAgICAgICAgICAgICAgICAgIG5hbWUgPSBlcnJvckxvb2t1cFJlc3VsdC5uYW1lO1xuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkID0gaXNFbXB0eU9iamVjdChlcnJvcnMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcoW25hbWVdLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBlcnJvciA9IChhd2FpdCB2YWxpZGF0ZUZpZWxkKGZpZWxkLCBfbmFtZXMuZGlzYWJsZWQsIF9mb3JtVmFsdWVzLCBzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycywgX29wdGlvbnMuc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbikpW25hbWVdO1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcoW25hbWVdKTtcbiAgICAgICAgICAgICAgICBfdXBkYXRlSXNGaWVsZFZhbHVlVXBkYXRlZChmaWVsZFZhbHVlKTtcbiAgICAgICAgICAgICAgICBpZiAoaXNGaWVsZFZhbHVlVXBkYXRlZCkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChfcHJveHlGb3JtU3RhdGUuaXNWYWxpZCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgPSBhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oX2ZpZWxkcywgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNGaWVsZFZhbHVlVXBkYXRlZCkge1xuICAgICAgICAgICAgICAgIGZpZWxkLl9mLmRlcHMgJiZcbiAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcihmaWVsZC5fZi5kZXBzKTtcbiAgICAgICAgICAgICAgICBzaG91bGRSZW5kZXJCeUVycm9yKG5hbWUsIGlzVmFsaWQsIGVycm9yLCBmaWVsZFN0YXRlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgX2ZvY3VzSW5wdXQgPSAocmVmLCBrZXkpID0+IHtcbiAgICAgICAgaWYgKGdldChfZm9ybVN0YXRlLmVycm9ycywga2V5KSAmJiByZWYuZm9jdXMpIHtcbiAgICAgICAgICAgIHJlZi5mb2N1cygpO1xuICAgICAgICAgICAgcmV0dXJuIDE7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgIH07XG4gICAgY29uc3QgdHJpZ2dlciA9IGFzeW5jIChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgbGV0IGlzVmFsaWQ7XG4gICAgICAgIGxldCB2YWxpZGF0aW9uUmVzdWx0O1xuICAgICAgICBjb25zdCBmaWVsZE5hbWVzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKG5hbWUpO1xuICAgICAgICBpZiAoX29wdGlvbnMucmVzb2x2ZXIpIHtcbiAgICAgICAgICAgIGNvbnN0IGVycm9ycyA9IGF3YWl0IGV4ZWN1dGVTY2hlbWFBbmRVcGRhdGVTdGF0ZShpc1VuZGVmaW5lZChuYW1lKSA/IG5hbWUgOiBmaWVsZE5hbWVzKTtcbiAgICAgICAgICAgIGlzVmFsaWQgPSBpc0VtcHR5T2JqZWN0KGVycm9ycyk7XG4gICAgICAgICAgICB2YWxpZGF0aW9uUmVzdWx0ID0gbmFtZVxuICAgICAgICAgICAgICAgID8gIWZpZWxkTmFtZXMuc29tZSgobmFtZSkgPT4gZ2V0KGVycm9ycywgbmFtZSkpXG4gICAgICAgICAgICAgICAgOiBpc1ZhbGlkO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKG5hbWUpIHtcbiAgICAgICAgICAgIHZhbGlkYXRpb25SZXN1bHQgPSAoYXdhaXQgUHJvbWlzZS5hbGwoZmllbGROYW1lcy5tYXAoYXN5bmMgKGZpZWxkTmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihmaWVsZCAmJiBmaWVsZC5fZiA/IHsgW2ZpZWxkTmFtZV06IGZpZWxkIH0gOiBmaWVsZCk7XG4gICAgICAgICAgICB9KSkpLmV2ZXJ5KEJvb2xlYW4pO1xuICAgICAgICAgICAgISghdmFsaWRhdGlvblJlc3VsdCAmJiAhX2Zvcm1TdGF0ZS5pc1ZhbGlkKSAmJiBfc2V0VmFsaWQoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHZhbGlkYXRpb25SZXN1bHQgPSBpc1ZhbGlkID0gYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKF9maWVsZHMpO1xuICAgICAgICB9XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIC4uLighaXNTdHJpbmcobmFtZSkgfHxcbiAgICAgICAgICAgICAgICAoKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkKSAmJlxuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkICE9PSBfZm9ybVN0YXRlLmlzVmFsaWQpXG4gICAgICAgICAgICAgICAgPyB7fVxuICAgICAgICAgICAgICAgIDogeyBuYW1lIH0pLFxuICAgICAgICAgICAgLi4uKF9vcHRpb25zLnJlc29sdmVyIHx8ICFuYW1lID8geyBpc1ZhbGlkIH0gOiB7fSksXG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICAgICAgb3B0aW9ucy5zaG91bGRGb2N1cyAmJlxuICAgICAgICAgICAgIXZhbGlkYXRpb25SZXN1bHQgJiZcbiAgICAgICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihfZmllbGRzLCBfZm9jdXNJbnB1dCwgbmFtZSA/IGZpZWxkTmFtZXMgOiBfbmFtZXMubW91bnQpO1xuICAgICAgICByZXR1cm4gdmFsaWRhdGlvblJlc3VsdDtcbiAgICB9O1xuICAgIGNvbnN0IGdldFZhbHVlcyA9IChmaWVsZE5hbWVzKSA9PiB7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IHtcbiAgICAgICAgICAgIC4uLihfc3RhdGUubW91bnQgPyBfZm9ybVZhbHVlcyA6IF9kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIGlzVW5kZWZpbmVkKGZpZWxkTmFtZXMpXG4gICAgICAgICAgICA/IHZhbHVlc1xuICAgICAgICAgICAgOiBpc1N0cmluZyhmaWVsZE5hbWVzKVxuICAgICAgICAgICAgICAgID8gZ2V0KHZhbHVlcywgZmllbGROYW1lcylcbiAgICAgICAgICAgICAgICA6IGZpZWxkTmFtZXMubWFwKChuYW1lKSA9PiBnZXQodmFsdWVzLCBuYW1lKSk7XG4gICAgfTtcbiAgICBjb25zdCBnZXRGaWVsZFN0YXRlID0gKG5hbWUsIGZvcm1TdGF0ZSkgPT4gKHtcbiAgICAgICAgaW52YWxpZDogISFnZXQoKGZvcm1TdGF0ZSB8fCBfZm9ybVN0YXRlKS5lcnJvcnMsIG5hbWUpLFxuICAgICAgICBpc0RpcnR5OiAhIWdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLmRpcnR5RmllbGRzLCBuYW1lKSxcbiAgICAgICAgZXJyb3I6IGdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLmVycm9ycywgbmFtZSksXG4gICAgICAgIGlzVmFsaWRhdGluZzogISFnZXQoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBuYW1lKSxcbiAgICAgICAgaXNUb3VjaGVkOiAhIWdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLnRvdWNoZWRGaWVsZHMsIG5hbWUpLFxuICAgIH0pO1xuICAgIGNvbnN0IGNsZWFyRXJyb3JzID0gKG5hbWUpID0+IHtcbiAgICAgICAgbmFtZSAmJlxuICAgICAgICAgICAgY29udmVydFRvQXJyYXlQYXlsb2FkKG5hbWUpLmZvckVhY2goKGlucHV0TmFtZSkgPT4gdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIGlucHV0TmFtZSkpO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBlcnJvcnM6IG5hbWUgPyBfZm9ybVN0YXRlLmVycm9ycyA6IHt9LFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHNldEVycm9yID0gKG5hbWUsIGVycm9yLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IHJlZiA9IChnZXQoX2ZpZWxkcywgbmFtZSwgeyBfZjoge30gfSkuX2YgfHwge30pLnJlZjtcbiAgICAgICAgY29uc3QgY3VycmVudEVycm9yID0gZ2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKSB8fCB7fTtcbiAgICAgICAgLy8gRG9uJ3Qgb3ZlcnJpZGUgZXhpc3RpbmcgZXJyb3IgbWVzc2FnZXMgZWxzZXdoZXJlIGluIHRoZSBvYmplY3QgdHJlZS5cbiAgICAgICAgY29uc3QgeyByZWY6IGN1cnJlbnRSZWYsIG1lc3NhZ2UsIHR5cGUsIC4uLnJlc3RPZkVycm9yVHJlZSB9ID0gY3VycmVudEVycm9yO1xuICAgICAgICBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIHtcbiAgICAgICAgICAgIC4uLnJlc3RPZkVycm9yVHJlZSxcbiAgICAgICAgICAgIC4uLmVycm9yLFxuICAgICAgICAgICAgcmVmLFxuICAgICAgICB9KTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgICAgIG9wdGlvbnMgJiYgb3B0aW9ucy5zaG91bGRGb2N1cyAmJiByZWYgJiYgcmVmLmZvY3VzICYmIHJlZi5mb2N1cygpO1xuICAgIH07XG4gICAgY29uc3Qgd2F0Y2ggPSAobmFtZSwgZGVmYXVsdFZhbHVlKSA9PiBpc0Z1bmN0aW9uKG5hbWUpXG4gICAgICAgID8gX3N1YmplY3RzLnN0YXRlLnN1YnNjcmliZSh7XG4gICAgICAgICAgICBuZXh0OiAocGF5bG9hZCkgPT4gJ3ZhbHVlcycgaW4gcGF5bG9hZCAmJlxuICAgICAgICAgICAgICAgIG5hbWUoX2dldFdhdGNoKHVuZGVmaW5lZCwgZGVmYXVsdFZhbHVlKSwgcGF5bG9hZCksXG4gICAgICAgIH0pXG4gICAgICAgIDogX2dldFdhdGNoKG5hbWUsIGRlZmF1bHRWYWx1ZSwgdHJ1ZSk7XG4gICAgY29uc3QgX3N1YnNjcmliZSA9IChwcm9wcykgPT4gX3N1YmplY3RzLnN0YXRlLnN1YnNjcmliZSh7XG4gICAgICAgIG5leHQ6IChmb3JtU3RhdGUpID0+IHtcbiAgICAgICAgICAgIGlmIChzaG91bGRTdWJzY3JpYmVCeU5hbWUocHJvcHMubmFtZSwgZm9ybVN0YXRlLm5hbWUsIHByb3BzLmV4YWN0KSAmJlxuICAgICAgICAgICAgICAgIHNob3VsZFJlbmRlckZvcm1TdGF0ZShmb3JtU3RhdGUsIHByb3BzLmZvcm1TdGF0ZSB8fCBfcHJveHlGb3JtU3RhdGUsIF9zZXRGb3JtU3RhdGUsIHByb3BzLnJlUmVuZGVyUm9vdCkpIHtcbiAgICAgICAgICAgICAgICBwcm9wcy5jYWxsYmFjayh7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlczogeyAuLi5fZm9ybVZhbHVlcyB9LFxuICAgICAgICAgICAgICAgICAgICAuLi5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgICAgICAuLi5mb3JtU3RhdGUsXG4gICAgICAgICAgICAgICAgICAgIGRlZmF1bHRWYWx1ZXM6IF9kZWZhdWx0VmFsdWVzLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH0pLnVuc3Vic2NyaWJlO1xuICAgIGNvbnN0IHN1YnNjcmliZSA9IChwcm9wcykgPT4ge1xuICAgICAgICBfc3RhdGUubW91bnQgPSB0cnVlO1xuICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAuLi5fcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi5wcm9wcy5mb3JtU3RhdGUsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBfc3Vic2NyaWJlKHtcbiAgICAgICAgICAgIC4uLnByb3BzLFxuICAgICAgICAgICAgZm9ybVN0YXRlOiBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgdW5yZWdpc3RlciA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgZm9yIChjb25zdCBmaWVsZE5hbWUgb2YgbmFtZSA/IGNvbnZlcnRUb0FycmF5UGF5bG9hZChuYW1lKSA6IF9uYW1lcy5tb3VudCkge1xuICAgICAgICAgICAgX25hbWVzLm1vdW50LmRlbGV0ZShmaWVsZE5hbWUpO1xuICAgICAgICAgICAgX25hbWVzLmFycmF5LmRlbGV0ZShmaWVsZE5hbWUpO1xuICAgICAgICAgICAgaWYgKCFvcHRpb25zLmtlZXBWYWx1ZSkge1xuICAgICAgICAgICAgICAgIHVuc2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1WYWx1ZXMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwRXJyb3IgJiYgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwRGlydHkgJiYgdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICFvcHRpb25zLmtlZXBUb3VjaGVkICYmIHVuc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICFvcHRpb25zLmtlZXBJc1ZhbGlkYXRpbmcgJiZcbiAgICAgICAgICAgICAgICB1bnNldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciAmJlxuICAgICAgICAgICAgICAgICFvcHRpb25zLmtlZXBEZWZhdWx0VmFsdWUgJiZcbiAgICAgICAgICAgICAgICB1bnNldChfZGVmYXVsdFZhbHVlcywgZmllbGROYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKSxcbiAgICAgICAgfSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIC4uLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi4oIW9wdGlvbnMua2VlcERpcnR5ID8ge30gOiB7IGlzRGlydHk6IF9nZXREaXJ0eSgpIH0pLFxuICAgICAgICB9KTtcbiAgICAgICAgIW9wdGlvbnMua2VlcElzVmFsaWQgJiYgX3NldFZhbGlkKCk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RGlzYWJsZWRGaWVsZCA9ICh7IGRpc2FibGVkLCBuYW1lLCB9KSA9PiB7XG4gICAgICAgIGlmICgoaXNCb29sZWFuKGRpc2FibGVkKSAmJiBfc3RhdGUubW91bnQpIHx8XG4gICAgICAgICAgICAhIWRpc2FibGVkIHx8XG4gICAgICAgICAgICBfbmFtZXMuZGlzYWJsZWQuaGFzKG5hbWUpKSB7XG4gICAgICAgICAgICBkaXNhYmxlZCA/IF9uYW1lcy5kaXNhYmxlZC5hZGQobmFtZSkgOiBfbmFtZXMuZGlzYWJsZWQuZGVsZXRlKG5hbWUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCByZWdpc3RlciA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgbGV0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBjb25zdCBkaXNhYmxlZElzRGVmaW5lZCA9IGlzQm9vbGVhbihvcHRpb25zLmRpc2FibGVkKSB8fCBpc0Jvb2xlYW4oX29wdGlvbnMuZGlzYWJsZWQpO1xuICAgICAgICBzZXQoX2ZpZWxkcywgbmFtZSwge1xuICAgICAgICAgICAgLi4uKGZpZWxkIHx8IHt9KSxcbiAgICAgICAgICAgIF9mOiB7XG4gICAgICAgICAgICAgICAgLi4uKGZpZWxkICYmIGZpZWxkLl9mID8gZmllbGQuX2YgOiB7IHJlZjogeyBuYW1lIH0gfSksXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICBtb3VudDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICAgIF9uYW1lcy5tb3VudC5hZGQobmFtZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgX3NldERpc2FibGVkRmllbGQoe1xuICAgICAgICAgICAgICAgIGRpc2FibGVkOiBpc0Jvb2xlYW4ob3B0aW9ucy5kaXNhYmxlZClcbiAgICAgICAgICAgICAgICAgICAgPyBvcHRpb25zLmRpc2FibGVkXG4gICAgICAgICAgICAgICAgICAgIDogX29wdGlvbnMuZGlzYWJsZWQsXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdXBkYXRlVmFsaWRBbmRWYWx1ZShuYW1lLCB0cnVlLCBvcHRpb25zLnZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uKGRpc2FibGVkSXNEZWZpbmVkXG4gICAgICAgICAgICAgICAgPyB7IGRpc2FibGVkOiBvcHRpb25zLmRpc2FibGVkIHx8IF9vcHRpb25zLmRpc2FibGVkIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgIC4uLihfb3B0aW9ucy5wcm9ncmVzc2l2ZVxuICAgICAgICAgICAgICAgID8ge1xuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogISFvcHRpb25zLnJlcXVpcmVkLFxuICAgICAgICAgICAgICAgICAgICBtaW46IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1pbiksXG4gICAgICAgICAgICAgICAgICAgIG1heDogZ2V0UnVsZVZhbHVlKG9wdGlvbnMubWF4KSxcbiAgICAgICAgICAgICAgICAgICAgbWluTGVuZ3RoOiBnZXRSdWxlVmFsdWUob3B0aW9ucy5taW5MZW5ndGgpLFxuICAgICAgICAgICAgICAgICAgICBtYXhMZW5ndGg6IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1heExlbmd0aCksXG4gICAgICAgICAgICAgICAgICAgIHBhdHRlcm46IGdldFJ1bGVWYWx1ZShvcHRpb25zLnBhdHRlcm4pLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICBvbkNoYW5nZSxcbiAgICAgICAgICAgIG9uQmx1cjogb25DaGFuZ2UsXG4gICAgICAgICAgICByZWY6IChyZWYpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAocmVmKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZ2lzdGVyKG5hbWUsIG9wdGlvbnMpO1xuICAgICAgICAgICAgICAgICAgICBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRSZWYgPSBpc1VuZGVmaW5lZChyZWYudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IHJlZi5xdWVyeVNlbGVjdG9yQWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyByZWYucXVlcnlTZWxlY3RvckFsbCgnaW5wdXQsc2VsZWN0LHRleHRhcmVhJylbMF0gfHwgcmVmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByZWZcbiAgICAgICAgICAgICAgICAgICAgICAgIDogcmVmO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvT3JDaGVja2JveChmaWVsZFJlZik7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlZnMgPSBmaWVsZC5fZi5yZWZzIHx8IFtdO1xuICAgICAgICAgICAgICAgICAgICBpZiAocmFkaW9PckNoZWNrYm94XG4gICAgICAgICAgICAgICAgICAgICAgICA/IHJlZnMuZmluZCgob3B0aW9uKSA9PiBvcHRpb24gPT09IGZpZWxkUmVmKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBmaWVsZFJlZiA9PT0gZmllbGQuX2YucmVmKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgc2V0KF9maWVsZHMsIG5hbWUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9mOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZmllbGQuX2YsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uKHJhZGlvT3JDaGVja2JveFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZnM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZWZzLmZpbHRlcihsaXZlKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4oQXJyYXkuaXNBcnJheShnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpKSA/IFt7fV0gOiBbXSksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmOiB7IHR5cGU6IGZpZWxkUmVmLnR5cGUsIG5hbWUgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHsgcmVmOiBmaWVsZFJlZiB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVWYWxpZEFuZFZhbHVlKG5hbWUsIGZhbHNlLCB1bmRlZmluZWQsIGZpZWxkUmVmKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUsIHt9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIChfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyIHx8IG9wdGlvbnMuc2hvdWxkVW5yZWdpc3RlcikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICEoaXNOYW1lSW5GaWVsZEFycmF5KF9uYW1lcy5hcnJheSwgbmFtZSkgJiYgX3N0YXRlLmFjdGlvbikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIF9uYW1lcy51bk1vdW50LmFkZChuYW1lKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgX2ZvY3VzRXJyb3IgPSAoKSA9PiBfb3B0aW9ucy5zaG91bGRGb2N1c0Vycm9yICYmXG4gICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihfZmllbGRzLCBfZm9jdXNJbnB1dCwgX25hbWVzLm1vdW50KTtcbiAgICBjb25zdCBfZGlzYWJsZUZvcm0gPSAoZGlzYWJsZWQpID0+IHtcbiAgICAgICAgaWYgKGlzQm9vbGVhbihkaXNhYmxlZCkpIHtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgZGlzYWJsZWQgfSk7XG4gICAgICAgICAgICBpdGVyYXRlRmllbGRzQnlBY3Rpb24oX2ZpZWxkcywgKHJlZiwgbmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoY3VycmVudEZpZWxkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZi5kaXNhYmxlZCA9IGN1cnJlbnRGaWVsZC5fZi5kaXNhYmxlZCB8fCBkaXNhYmxlZDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY3VycmVudEZpZWxkLl9mLnJlZnMpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50RmllbGQuX2YucmVmcy5mb3JFYWNoKChpbnB1dFJlZikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0UmVmLmRpc2FibGVkID0gY3VycmVudEZpZWxkLl9mLmRpc2FibGVkIHx8IGRpc2FibGVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LCAwLCBmYWxzZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChvblZhbGlkLCBvbkludmFsaWQpID0+IGFzeW5jIChlKSA9PiB7XG4gICAgICAgIGxldCBvblZhbGlkRXJyb3IgPSB1bmRlZmluZWQ7XG4gICAgICAgIGlmIChlKSB7XG4gICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0ICYmIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIGUucGVyc2lzdCAmJlxuICAgICAgICAgICAgICAgIGUucGVyc2lzdCgpO1xuICAgICAgICB9XG4gICAgICAgIGxldCBmaWVsZFZhbHVlcyA9IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgaXNTdWJtaXR0aW5nOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKF9vcHRpb25zLnJlc29sdmVyKSB7XG4gICAgICAgICAgICBjb25zdCB7IGVycm9ycywgdmFsdWVzIH0gPSBhd2FpdCBfcnVuU2NoZW1hKCk7XG4gICAgICAgICAgICBfZm9ybVN0YXRlLmVycm9ycyA9IGVycm9ycztcbiAgICAgICAgICAgIGZpZWxkVmFsdWVzID0gY2xvbmVPYmplY3QodmFsdWVzKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoX25hbWVzLmRpc2FibGVkLnNpemUpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgICAgICB1bnNldChmaWVsZFZhbHVlcywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsICdyb290Jyk7XG4gICAgICAgIGlmIChpc0VtcHR5T2JqZWN0KF9mb3JtU3RhdGUuZXJyb3JzKSkge1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIGVycm9yczoge30sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgb25WYWxpZChmaWVsZFZhbHVlcywgZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBvblZhbGlkRXJyb3IgPSBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmIChvbkludmFsaWQpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBvbkludmFsaWQoeyAuLi5fZm9ybVN0YXRlLmVycm9ycyB9LCBlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9mb2N1c0Vycm9yKCk7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KF9mb2N1c0Vycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBpc1N1Ym1pdHRlZDogdHJ1ZSxcbiAgICAgICAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGlzRW1wdHlPYmplY3QoX2Zvcm1TdGF0ZS5lcnJvcnMpICYmICFvblZhbGlkRXJyb3IsXG4gICAgICAgICAgICBzdWJtaXRDb3VudDogX2Zvcm1TdGF0ZS5zdWJtaXRDb3VudCArIDEsXG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKG9uVmFsaWRFcnJvcikge1xuICAgICAgICAgICAgdGhyb3cgb25WYWxpZEVycm9yO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCByZXNldEZpZWxkID0gKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBpZiAoZ2V0KF9maWVsZHMsIG5hbWUpKSB7XG4gICAgICAgICAgICBpZiAoaXNVbmRlZmluZWQob3B0aW9ucy5kZWZhdWx0VmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsdWUobmFtZSwgY2xvbmVPYmplY3QoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsdWUobmFtZSwgb3B0aW9ucy5kZWZhdWx0VmFsdWUpO1xuICAgICAgICAgICAgICAgIHNldChfZGVmYXVsdFZhbHVlcywgbmFtZSwgY2xvbmVPYmplY3Qob3B0aW9ucy5kZWZhdWx0VmFsdWUpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghb3B0aW9ucy5rZWVwVG91Y2hlZCkge1xuICAgICAgICAgICAgICAgIHVuc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMua2VlcERpcnR5KSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgX2Zvcm1TdGF0ZS5pc0RpcnR5ID0gb3B0aW9ucy5kZWZhdWx0VmFsdWVcbiAgICAgICAgICAgICAgICAgICAgPyBfZ2V0RGlydHkobmFtZSwgY2xvbmVPYmplY3QoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkpXG4gICAgICAgICAgICAgICAgICAgIDogX2dldERpcnR5KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMua2VlcEVycm9yKSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkICYmIF9zZXRWYWxpZCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoeyAuLi5fZm9ybVN0YXRlIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfcmVzZXQgPSAoZm9ybVZhbHVlcywga2VlcFN0YXRlT3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRWYWx1ZXMgPSBmb3JtVmFsdWVzID8gY2xvbmVPYmplY3QoZm9ybVZhbHVlcykgOiBfZGVmYXVsdFZhbHVlcztcbiAgICAgICAgY29uc3QgY2xvbmVVcGRhdGVkVmFsdWVzID0gY2xvbmVPYmplY3QodXBkYXRlZFZhbHVlcyk7XG4gICAgICAgIGNvbnN0IGlzRW1wdHlSZXNldFZhbHVlcyA9IGlzRW1wdHlPYmplY3QoZm9ybVZhbHVlcyk7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IGlzRW1wdHlSZXNldFZhbHVlcyA/IF9kZWZhdWx0VmFsdWVzIDogY2xvbmVVcGRhdGVkVmFsdWVzO1xuICAgICAgICBpZiAoIWtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMpIHtcbiAgICAgICAgICAgIF9kZWZhdWx0VmFsdWVzID0gdXBkYXRlZFZhbHVlcztcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWtlZXBTdGF0ZU9wdGlvbnMua2VlcFZhbHVlcykge1xuICAgICAgICAgICAgaWYgKGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRzVG9DaGVjayA9IG5ldyBTZXQoW1xuICAgICAgICAgICAgICAgICAgICAuLi5fbmFtZXMubW91bnQsXG4gICAgICAgICAgICAgICAgICAgIC4uLk9iamVjdC5rZXlzKGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBfZm9ybVZhbHVlcykpLFxuICAgICAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZmllbGROYW1lIG9mIEFycmF5LmZyb20oZmllbGRzVG9DaGVjaykpIHtcbiAgICAgICAgICAgICAgICAgICAgZ2V0KF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsIGZpZWxkTmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KHZhbHVlcywgZmllbGROYW1lLCBnZXQoX2Zvcm1WYWx1ZXMsIGZpZWxkTmFtZSkpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHNldFZhbHVlKGZpZWxkTmFtZSwgZ2V0KHZhbHVlcywgZmllbGROYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzV2ViICYmIGlzVW5kZWZpbmVkKGZvcm1WYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMubW91bnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBBcnJheS5pc0FycmF5KGZpZWxkLl9mLnJlZnMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZmllbGQuX2YucmVmc1swXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGZpZWxkLl9mLnJlZjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXNIVE1MRWxlbWVudChmaWVsZFJlZmVyZW5jZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZm9ybSA9IGZpZWxkUmVmZXJlbmNlLmNsb3Nlc3QoJ2Zvcm0nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZvcm0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm0ucmVzZXQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChrZWVwU3RhdGVPcHRpb25zLmtlZXBGaWVsZHNSZWYpIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBmaWVsZE5hbWUgb2YgX25hbWVzLm1vdW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRWYWx1ZShmaWVsZE5hbWUsIGdldCh2YWx1ZXMsIGZpZWxkTmFtZSkpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBfZmllbGRzID0ge307XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX2Zvcm1WYWx1ZXMgPSBfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyXG4gICAgICAgICAgICAgICAgPyBrZWVwU3RhdGVPcHRpb25zLmtlZXBEZWZhdWx0VmFsdWVzXG4gICAgICAgICAgICAgICAgICAgID8gY2xvbmVPYmplY3QoX2RlZmF1bHRWYWx1ZXMpXG4gICAgICAgICAgICAgICAgICAgIDoge31cbiAgICAgICAgICAgICAgICA6IGNsb25lT2JqZWN0KHZhbHVlcyk7XG4gICAgICAgICAgICBfc3ViamVjdHMuYXJyYXkubmV4dCh7XG4gICAgICAgICAgICAgICAgdmFsdWVzOiB7IC4uLnZhbHVlcyB9LFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgdmFsdWVzOiB7IC4uLnZhbHVlcyB9LFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgX25hbWVzID0ge1xuICAgICAgICAgICAgbW91bnQ6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzID8gX25hbWVzLm1vdW50IDogbmV3IFNldCgpLFxuICAgICAgICAgICAgdW5Nb3VudDogbmV3IFNldCgpLFxuICAgICAgICAgICAgYXJyYXk6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIGRpc2FibGVkOiBuZXcgU2V0KCksXG4gICAgICAgICAgICB3YXRjaDogbmV3IFNldCgpLFxuICAgICAgICAgICAgd2F0Y2hBbGw6IGZhbHNlLFxuICAgICAgICAgICAgZm9jdXM6ICcnLFxuICAgICAgICB9O1xuICAgICAgICBfc3RhdGUubW91bnQgPVxuICAgICAgICAgICAgIV9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8XG4gICAgICAgICAgICAgICAgISFrZWVwU3RhdGVPcHRpb25zLmtlZXBJc1ZhbGlkIHx8XG4gICAgICAgICAgICAgICAgISFrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVZhbHVlcztcbiAgICAgICAgX3N0YXRlLndhdGNoID0gISFfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBzdWJtaXRDb3VudDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwU3VibWl0Q291bnRcbiAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUuc3VibWl0Q291bnRcbiAgICAgICAgICAgICAgICA6IDAsXG4gICAgICAgICAgICBpc0RpcnR5OiBpc0VtcHR5UmVzZXRWYWx1ZXNcbiAgICAgICAgICAgICAgICA/IGZhbHNlXG4gICAgICAgICAgICAgICAgOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVxuICAgICAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUuaXNEaXJ0eVxuICAgICAgICAgICAgICAgICAgICA6ICEhKGtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICFkZWVwRXF1YWwoZm9ybVZhbHVlcywgX2RlZmF1bHRWYWx1ZXMpKSxcbiAgICAgICAgICAgIGlzU3VibWl0dGVkOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBJc1N1Ym1pdHRlZFxuICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZFxuICAgICAgICAgICAgICAgIDogZmFsc2UsXG4gICAgICAgICAgICBkaXJ0eUZpZWxkczogaXNFbXB0eVJlc2V0VmFsdWVzXG4gICAgICAgICAgICAgICAgPyB7fVxuICAgICAgICAgICAgICAgIDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlWYWx1ZXNcbiAgICAgICAgICAgICAgICAgICAgPyBrZWVwU3RhdGVPcHRpb25zLmtlZXBEZWZhdWx0VmFsdWVzICYmIF9mb3JtVmFsdWVzXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBfZm9ybVZhbHVlcylcbiAgICAgICAgICAgICAgICAgICAgICAgIDogX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkc1xuICAgICAgICAgICAgICAgICAgICA6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMgJiYgZm9ybVZhbHVlc1xuICAgICAgICAgICAgICAgICAgICAgICAgPyBnZXREaXJ0eUZpZWxkcyhfZGVmYXVsdFZhbHVlcywgZm9ybVZhbHVlcylcbiAgICAgICAgICAgICAgICAgICAgICAgIDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUuZGlydHlGaWVsZHNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHt9LFxuICAgICAgICAgICAgdG91Y2hlZEZpZWxkczoga2VlcFN0YXRlT3B0aW9ucy5rZWVwVG91Y2hlZFxuICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzXG4gICAgICAgICAgICAgICAgOiB7fSxcbiAgICAgICAgICAgIGVycm9yczoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRXJyb3JzID8gX2Zvcm1TdGF0ZS5lcnJvcnMgOiB7fSxcbiAgICAgICAgICAgIGlzU3VibWl0U3VjY2Vzc2Z1bDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwSXNTdWJtaXRTdWNjZXNzZnVsXG4gICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLmlzU3VibWl0U3VjY2Vzc2Z1bFxuICAgICAgICAgICAgICAgIDogZmFsc2UsXG4gICAgICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHJlc2V0ID0gKGZvcm1WYWx1ZXMsIGtlZXBTdGF0ZU9wdGlvbnMpID0+IF9yZXNldChpc0Z1bmN0aW9uKGZvcm1WYWx1ZXMpXG4gICAgICAgID8gZm9ybVZhbHVlcyhfZm9ybVZhbHVlcylcbiAgICAgICAgOiBmb3JtVmFsdWVzLCBrZWVwU3RhdGVPcHRpb25zKTtcbiAgICBjb25zdCBzZXRGb2N1cyA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGNvbnN0IGZpZWxkUmVmZXJlbmNlID0gZmllbGQgJiYgZmllbGQuX2Y7XG4gICAgICAgIGlmIChmaWVsZFJlZmVyZW5jZSkge1xuICAgICAgICAgICAgY29uc3QgZmllbGRSZWYgPSBmaWVsZFJlZmVyZW5jZS5yZWZzXG4gICAgICAgICAgICAgICAgPyBmaWVsZFJlZmVyZW5jZS5yZWZzWzBdXG4gICAgICAgICAgICAgICAgOiBmaWVsZFJlZmVyZW5jZS5yZWY7XG4gICAgICAgICAgICBpZiAoZmllbGRSZWYuZm9jdXMpIHtcbiAgICAgICAgICAgICAgICBmaWVsZFJlZi5mb2N1cygpO1xuICAgICAgICAgICAgICAgIG9wdGlvbnMuc2hvdWxkU2VsZWN0ICYmXG4gICAgICAgICAgICAgICAgICAgIGlzRnVuY3Rpb24oZmllbGRSZWYuc2VsZWN0KSAmJlxuICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZi5zZWxlY3QoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgX3NldEZvcm1TdGF0ZSA9ICh1cGRhdGVkRm9ybVN0YXRlKSA9PiB7XG4gICAgICAgIF9mb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAuLi5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgLi4udXBkYXRlZEZvcm1TdGF0ZSxcbiAgICAgICAgfTtcbiAgICB9O1xuICAgIGNvbnN0IF9yZXNldERlZmF1bHRWYWx1ZXMgPSAoKSA9PiBpc0Z1bmN0aW9uKF9vcHRpb25zLmRlZmF1bHRWYWx1ZXMpICYmXG4gICAgICAgIF9vcHRpb25zLmRlZmF1bHRWYWx1ZXMoKS50aGVuKCh2YWx1ZXMpID0+IHtcbiAgICAgICAgICAgIHJlc2V0KHZhbHVlcywgX29wdGlvbnMucmVzZXRPcHRpb25zKTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIGNvbnN0IG1ldGhvZHMgPSB7XG4gICAgICAgIGNvbnRyb2w6IHtcbiAgICAgICAgICAgIHJlZ2lzdGVyLFxuICAgICAgICAgICAgdW5yZWdpc3RlcixcbiAgICAgICAgICAgIGdldEZpZWxkU3RhdGUsXG4gICAgICAgICAgICBoYW5kbGVTdWJtaXQsXG4gICAgICAgICAgICBzZXRFcnJvcixcbiAgICAgICAgICAgIF9zdWJzY3JpYmUsXG4gICAgICAgICAgICBfcnVuU2NoZW1hLFxuICAgICAgICAgICAgX2ZvY3VzRXJyb3IsXG4gICAgICAgICAgICBfZ2V0V2F0Y2gsXG4gICAgICAgICAgICBfZ2V0RGlydHksXG4gICAgICAgICAgICBfc2V0VmFsaWQsXG4gICAgICAgICAgICBfc2V0RmllbGRBcnJheSxcbiAgICAgICAgICAgIF9zZXREaXNhYmxlZEZpZWxkLFxuICAgICAgICAgICAgX3NldEVycm9ycyxcbiAgICAgICAgICAgIF9nZXRGaWVsZEFycmF5LFxuICAgICAgICAgICAgX3Jlc2V0LFxuICAgICAgICAgICAgX3Jlc2V0RGVmYXVsdFZhbHVlcyxcbiAgICAgICAgICAgIF9yZW1vdmVVbm1vdW50ZWQsXG4gICAgICAgICAgICBfZGlzYWJsZUZvcm0sXG4gICAgICAgICAgICBfc3ViamVjdHMsXG4gICAgICAgICAgICBfcHJveHlGb3JtU3RhdGUsXG4gICAgICAgICAgICBnZXQgX2ZpZWxkcygpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX2ZpZWxkcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgX2Zvcm1WYWx1ZXMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9mb3JtVmFsdWVzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfc3RhdGUoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9zdGF0ZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXQgX3N0YXRlKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgX3N0YXRlID0gdmFsdWU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9kZWZhdWx0VmFsdWVzKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfZGVmYXVsdFZhbHVlcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgX25hbWVzKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfbmFtZXM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc2V0IF9uYW1lcyh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIF9uYW1lcyA9IHZhbHVlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfZm9ybVN0YXRlKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfZm9ybVN0YXRlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfb3B0aW9ucygpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX29wdGlvbnM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc2V0IF9vcHRpb25zKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgX29wdGlvbnMgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLl9vcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICAuLi52YWx1ZSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgc3Vic2NyaWJlLFxuICAgICAgICB0cmlnZ2VyLFxuICAgICAgICByZWdpc3RlcixcbiAgICAgICAgaGFuZGxlU3VibWl0LFxuICAgICAgICB3YXRjaCxcbiAgICAgICAgc2V0VmFsdWUsXG4gICAgICAgIGdldFZhbHVlcyxcbiAgICAgICAgcmVzZXQsXG4gICAgICAgIHJlc2V0RmllbGQsXG4gICAgICAgIGNsZWFyRXJyb3JzLFxuICAgICAgICB1bnJlZ2lzdGVyLFxuICAgICAgICBzZXRFcnJvcixcbiAgICAgICAgc2V0Rm9jdXMsXG4gICAgICAgIGdldEZpZWxkU3RhdGUsXG4gICAgfTtcbiAgICByZXR1cm4ge1xuICAgICAgICAuLi5tZXRob2RzLFxuICAgICAgICBmb3JtQ29udHJvbDogbWV0aG9kcyxcbiAgICB9O1xufVxuXG52YXIgZ2VuZXJhdGVJZCA9ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLnJhbmRvbVVVSUQpIHtcbiAgICAgICAgcmV0dXJuIGNyeXB0by5yYW5kb21VVUlEKCk7XG4gICAgfVxuICAgIGNvbnN0IGQgPSB0eXBlb2YgcGVyZm9ybWFuY2UgPT09ICd1bmRlZmluZWQnID8gRGF0ZS5ub3coKSA6IHBlcmZvcm1hbmNlLm5vdygpICogMTAwMDtcbiAgICByZXR1cm4gJ3h4eHh4eHh4LXh4eHgtNHh4eC15eHh4LXh4eHh4eHh4eHh4eCcucmVwbGFjZSgvW3h5XS9nLCAoYykgPT4ge1xuICAgICAgICBjb25zdCByID0gKE1hdGgucmFuZG9tKCkgKiAxNiArIGQpICUgMTYgfCAwO1xuICAgICAgICByZXR1cm4gKGMgPT0gJ3gnID8gciA6IChyICYgMHgzKSB8IDB4OCkudG9TdHJpbmcoMTYpO1xuICAgIH0pO1xufTtcblxudmFyIGdldEZvY3VzRmllbGROYW1lID0gKG5hbWUsIGluZGV4LCBvcHRpb25zID0ge30pID0+IG9wdGlvbnMuc2hvdWxkRm9jdXMgfHwgaXNVbmRlZmluZWQob3B0aW9ucy5zaG91bGRGb2N1cylcbiAgICA/IG9wdGlvbnMuZm9jdXNOYW1lIHx8XG4gICAgICAgIGAke25hbWV9LiR7aXNVbmRlZmluZWQob3B0aW9ucy5mb2N1c0luZGV4KSA/IGluZGV4IDogb3B0aW9ucy5mb2N1c0luZGV4fS5gXG4gICAgOiAnJztcblxudmFyIGFwcGVuZEF0ID0gKGRhdGEsIHZhbHVlKSA9PiBbXG4gICAgLi4uZGF0YSxcbiAgICAuLi5jb252ZXJ0VG9BcnJheVBheWxvYWQodmFsdWUpLFxuXTtcblxudmFyIGZpbGxFbXB0eUFycmF5ID0gKHZhbHVlKSA9PiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlLm1hcCgoKSA9PiB1bmRlZmluZWQpIDogdW5kZWZpbmVkO1xuXG5mdW5jdGlvbiBpbnNlcnQoZGF0YSwgaW5kZXgsIHZhbHVlKSB7XG4gICAgcmV0dXJuIFtcbiAgICAgICAgLi4uZGF0YS5zbGljZSgwLCBpbmRleCksXG4gICAgICAgIC4uLmNvbnZlcnRUb0FycmF5UGF5bG9hZCh2YWx1ZSksXG4gICAgICAgIC4uLmRhdGEuc2xpY2UoaW5kZXgpLFxuICAgIF07XG59XG5cbnZhciBtb3ZlQXJyYXlBdCA9IChkYXRhLCBmcm9tLCB0bykgPT4ge1xuICAgIGlmICghQXJyYXkuaXNBcnJheShkYXRhKSkge1xuICAgICAgICByZXR1cm4gW107XG4gICAgfVxuICAgIGlmIChpc1VuZGVmaW5lZChkYXRhW3RvXSkpIHtcbiAgICAgICAgZGF0YVt0b10gPSB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGRhdGEuc3BsaWNlKHRvLCAwLCBkYXRhLnNwbGljZShmcm9tLCAxKVswXSk7XG4gICAgcmV0dXJuIGRhdGE7XG59O1xuXG52YXIgcHJlcGVuZEF0ID0gKGRhdGEsIHZhbHVlKSA9PiBbXG4gICAgLi4uY29udmVydFRvQXJyYXlQYXlsb2FkKHZhbHVlKSxcbiAgICAuLi5jb252ZXJ0VG9BcnJheVBheWxvYWQoZGF0YSksXG5dO1xuXG5mdW5jdGlvbiByZW1vdmVBdEluZGV4ZXMoZGF0YSwgaW5kZXhlcykge1xuICAgIGxldCBpID0gMDtcbiAgICBjb25zdCB0ZW1wID0gWy4uLmRhdGFdO1xuICAgIGZvciAoY29uc3QgaW5kZXggb2YgaW5kZXhlcykge1xuICAgICAgICB0ZW1wLnNwbGljZShpbmRleCAtIGksIDEpO1xuICAgICAgICBpKys7XG4gICAgfVxuICAgIHJldHVybiBjb21wYWN0KHRlbXApLmxlbmd0aCA/IHRlbXAgOiBbXTtcbn1cbnZhciByZW1vdmVBcnJheUF0ID0gKGRhdGEsIGluZGV4KSA9PiBpc1VuZGVmaW5lZChpbmRleClcbiAgICA/IFtdXG4gICAgOiByZW1vdmVBdEluZGV4ZXMoZGF0YSwgY29udmVydFRvQXJyYXlQYXlsb2FkKGluZGV4KS5zb3J0KChhLCBiKSA9PiBhIC0gYikpO1xuXG52YXIgc3dhcEFycmF5QXQgPSAoZGF0YSwgaW5kZXhBLCBpbmRleEIpID0+IHtcbiAgICBbZGF0YVtpbmRleEFdLCBkYXRhW2luZGV4Ql1dID0gW2RhdGFbaW5kZXhCXSwgZGF0YVtpbmRleEFdXTtcbn07XG5cbnZhciB1cGRhdGVBdCA9IChmaWVsZFZhbHVlcywgaW5kZXgsIHZhbHVlKSA9PiB7XG4gICAgZmllbGRWYWx1ZXNbaW5kZXhdID0gdmFsdWU7XG4gICAgcmV0dXJuIGZpZWxkVmFsdWVzO1xufTtcblxuLyoqXG4gKiBBIGN1c3RvbSBob29rIHRoYXQgZXhwb3NlcyBjb252ZW5pZW50IG1ldGhvZHMgdG8gcGVyZm9ybSBvcGVyYXRpb25zIHdpdGggYSBsaXN0IG9mIGR5bmFtaWMgaW5wdXRzIHRoYXQgbmVlZCB0byBiZSBhcHBlbmRlZCwgdXBkYXRlZCwgcmVtb3ZlZCBldGMuIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS11c2VmaWVsZGFycmF5LXNzdWduKSDigKIgW1ZpZGVvXShodHRwczovL3lvdXR1LmJlLzRNcmJmR1NGWTJBKVxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2VmaWVsZGFycmF5KSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdXNlZmllbGRhcnJheS1zc3VnbilcbiAqXG4gKiBAcGFyYW0gcHJvcHMgLSB1c2VGaWVsZEFycmF5IHByb3BzXG4gKlxuICogQHJldHVybnMgbWV0aG9kcyAtIGZ1bmN0aW9ucyB0byBtYW5pcHVsYXRlIHdpdGggdGhlIEZpZWxkIEFycmF5cyAoZHluYW1pYyBpbnB1dHMpIHtAbGluayBVc2VGaWVsZEFycmF5UmV0dXJufVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciwgY29udHJvbCwgaGFuZGxlU3VibWl0LCByZXNldCwgdHJpZ2dlciwgc2V0RXJyb3IgfSA9IHVzZUZvcm0oe1xuICogICAgIGRlZmF1bHRWYWx1ZXM6IHtcbiAqICAgICAgIHRlc3Q6IFtdXG4gKiAgICAgfVxuICogICB9KTtcbiAqICAgY29uc3QgeyBmaWVsZHMsIGFwcGVuZCB9ID0gdXNlRmllbGRBcnJheSh7XG4gKiAgICAgY29udHJvbCxcbiAqICAgICBuYW1lOiBcInRlc3RcIlxuICogICB9KTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpKX0+XG4gKiAgICAgICB7ZmllbGRzLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAqICAgICAgICAgIDxpbnB1dCBrZXk9e2l0ZW0uaWR9IHsuLi5yZWdpc3RlcihgdGVzdC4ke2luZGV4fS5maXJzdE5hbWVgKX0gIC8+XG4gKiAgICAgICApKX1cbiAqICAgICAgIDxidXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9eygpID0+IGFwcGVuZCh7IGZpcnN0TmFtZTogXCJiaWxsXCIgfSl9PlxuICogICAgICAgICBhcHBlbmRcbiAqICAgICAgIDwvYnV0dG9uPlxuICogICAgICAgPGlucHV0IHR5cGU9XCJzdWJtaXRcIiAvPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VGaWVsZEFycmF5KHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBjb250cm9sID0gbWV0aG9kcy5jb250cm9sLCBuYW1lLCBrZXlOYW1lID0gJ2lkJywgc2hvdWxkVW5yZWdpc3RlciwgcnVsZXMsIH0gPSBwcm9wcztcbiAgICBjb25zdCBbZmllbGRzLCBzZXRGaWVsZHNdID0gUmVhY3QudXNlU3RhdGUoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSk7XG4gICAgY29uc3QgaWRzID0gUmVhY3QudXNlUmVmKGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSkubWFwKGdlbmVyYXRlSWQpKTtcbiAgICBjb25zdCBfZmllbGRJZHMgPSBSZWFjdC51c2VSZWYoZmllbGRzKTtcbiAgICBjb25zdCBfYWN0aW9uZWQgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICAgIF9maWVsZElkcy5jdXJyZW50ID0gZmllbGRzO1xuICAgIGNvbnRyb2wuX25hbWVzLmFycmF5LmFkZChuYW1lKTtcbiAgICBSZWFjdC51c2VNZW1vKCgpID0+IHJ1bGVzICYmXG4gICAgICAgIGNvbnRyb2wucmVnaXN0ZXIobmFtZSwgcnVsZXMpLCBbY29udHJvbCwgcnVsZXMsIG5hbWVdKTtcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IGNvbnRyb2wuX3N1YmplY3RzLmFycmF5LnN1YnNjcmliZSh7XG4gICAgICAgIG5leHQ6ICh7IHZhbHVlcywgbmFtZTogZmllbGRBcnJheU5hbWUsIH0pID0+IHtcbiAgICAgICAgICAgIGlmIChmaWVsZEFycmF5TmFtZSA9PT0gbmFtZSB8fCAhZmllbGRBcnJheU5hbWUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaWVsZFZhbHVlcyA9IGdldCh2YWx1ZXMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGZpZWxkVmFsdWVzKSkge1xuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZHMoZmllbGRWYWx1ZXMpO1xuICAgICAgICAgICAgICAgICAgICBpZHMuY3VycmVudCA9IGZpZWxkVmFsdWVzLm1hcChnZW5lcmF0ZUlkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgfSkudW5zdWJzY3JpYmUsIFtjb250cm9sLCBuYW1lXSk7XG4gICAgY29uc3QgdXBkYXRlVmFsdWVzID0gUmVhY3QudXNlQ2FsbGJhY2soKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKSA9PiB7XG4gICAgICAgIF9hY3Rpb25lZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgfSwgW2NvbnRyb2wsIG5hbWVdKTtcbiAgICBjb25zdCBhcHBlbmQgPSAodmFsdWUsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgY29uc3QgYXBwZW5kVmFsdWUgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBhcHBlbmRBdChjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpLCBhcHBlbmRWYWx1ZSk7XG4gICAgICAgIGNvbnRyb2wuX25hbWVzLmZvY3VzID0gZ2V0Rm9jdXNGaWVsZE5hbWUobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMubGVuZ3RoIC0gMSwgb3B0aW9ucyk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gYXBwZW5kQXQoaWRzLmN1cnJlbnQsIGFwcGVuZFZhbHVlLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIGFwcGVuZEF0LCB7XG4gICAgICAgICAgICBhcmdBOiBmaWxsRW1wdHlBcnJheSh2YWx1ZSksXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgcHJlcGVuZCA9ICh2YWx1ZSwgb3B0aW9ucykgPT4ge1xuICAgICAgICBjb25zdCBwcmVwZW5kVmFsdWUgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBwcmVwZW5kQXQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgcHJlcGVuZFZhbHVlKTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSBnZXRGb2N1c0ZpZWxkTmFtZShuYW1lLCAwLCBvcHRpb25zKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSBwcmVwZW5kQXQoaWRzLmN1cnJlbnQsIHByZXBlbmRWYWx1ZS5tYXAoZ2VuZXJhdGVJZCkpO1xuICAgICAgICB1cGRhdGVWYWx1ZXModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBzZXRGaWVsZHModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBwcmVwZW5kQXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGZpbGxFbXB0eUFycmF5KHZhbHVlKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCByZW1vdmUgPSAoaW5kZXgpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSByZW1vdmVBcnJheUF0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGluZGV4KTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSByZW1vdmVBcnJheUF0KGlkcy5jdXJyZW50LCBpbmRleCk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgICFBcnJheS5pc0FycmF5KGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpKSAmJlxuICAgICAgICAgICAgc2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSwgdW5kZWZpbmVkKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgcmVtb3ZlQXJyYXlBdCwge1xuICAgICAgICAgICAgYXJnQTogaW5kZXgsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgaW5zZXJ0JDEgPSAoaW5kZXgsIHZhbHVlLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IGluc2VydFZhbHVlID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGNsb25lT2JqZWN0KHZhbHVlKSk7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gaW5zZXJ0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGluZGV4LCBpbnNlcnRWYWx1ZSk7XG4gICAgICAgIGNvbnRyb2wuX25hbWVzLmZvY3VzID0gZ2V0Rm9jdXNGaWVsZE5hbWUobmFtZSwgaW5kZXgsIG9wdGlvbnMpO1xuICAgICAgICBpZHMuY3VycmVudCA9IGluc2VydChpZHMuY3VycmVudCwgaW5kZXgsIGluc2VydFZhbHVlLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIGluc2VydCwge1xuICAgICAgICAgICAgYXJnQTogaW5kZXgsXG4gICAgICAgICAgICBhcmdCOiBmaWxsRW1wdHlBcnJheSh2YWx1ZSksXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3Qgc3dhcCA9IChpbmRleEEsIGluZGV4QikgPT4ge1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSk7XG4gICAgICAgIHN3YXBBcnJheUF0KHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBpbmRleEEsIGluZGV4Qik7XG4gICAgICAgIHN3YXBBcnJheUF0KGlkcy5jdXJyZW50LCBpbmRleEEsIGluZGV4Qik7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIHN3YXBBcnJheUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleEEsXG4gICAgICAgICAgICBhcmdCOiBpbmRleEIsXG4gICAgICAgIH0sIGZhbHNlKTtcbiAgICB9O1xuICAgIGNvbnN0IG1vdmUgPSAoZnJvbSwgdG8pID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpO1xuICAgICAgICBtb3ZlQXJyYXlBdCh1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgZnJvbSwgdG8pO1xuICAgICAgICBtb3ZlQXJyYXlBdChpZHMuY3VycmVudCwgZnJvbSwgdG8pO1xuICAgICAgICB1cGRhdGVWYWx1ZXModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBzZXRGaWVsZHModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBtb3ZlQXJyYXlBdCwge1xuICAgICAgICAgICAgYXJnQTogZnJvbSxcbiAgICAgICAgICAgIGFyZ0I6IHRvLFxuICAgICAgICB9LCBmYWxzZSk7XG4gICAgfTtcbiAgICBjb25zdCB1cGRhdGUgPSAoaW5kZXgsIHZhbHVlKSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZVZhbHVlID0gY2xvbmVPYmplY3QodmFsdWUpO1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IHVwZGF0ZUF0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGluZGV4LCB1cGRhdGVWYWx1ZSk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gWy4uLnVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzXS5tYXAoKGl0ZW0sIGkpID0+ICFpdGVtIHx8IGkgPT09IGluZGV4ID8gZ2VuZXJhdGVJZCgpIDogaWRzLmN1cnJlbnRbaV0pO1xuICAgICAgICB1cGRhdGVWYWx1ZXModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBzZXRGaWVsZHMoWy4uLnVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzXSk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIHVwZGF0ZUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleCxcbiAgICAgICAgICAgIGFyZ0I6IHVwZGF0ZVZhbHVlLFxuICAgICAgICB9LCB0cnVlLCBmYWxzZSk7XG4gICAgfTtcbiAgICBjb25zdCByZXBsYWNlID0gKHZhbHVlKSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGNsb25lT2JqZWN0KHZhbHVlKSk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMubWFwKGdlbmVyYXRlSWQpO1xuICAgICAgICB1cGRhdGVWYWx1ZXMoWy4uLnVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzXSk7XG4gICAgICAgIHNldEZpZWxkcyhbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCBbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdLCAoZGF0YSkgPT4gZGF0YSwge30sIHRydWUsIGZhbHNlKTtcbiAgICB9O1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnRyb2wuX3N0YXRlLmFjdGlvbiA9IGZhbHNlO1xuICAgICAgICBpc1dhdGNoZWQobmFtZSwgY29udHJvbC5fbmFtZXMpICYmXG4gICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAuLi5jb250cm9sLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgaWYgKF9hY3Rpb25lZC5jdXJyZW50ICYmXG4gICAgICAgICAgICAoIWdldFZhbGlkYXRpb25Nb2Rlcyhjb250cm9sLl9vcHRpb25zLm1vZGUpLmlzT25TdWJtaXQgfHxcbiAgICAgICAgICAgICAgICBjb250cm9sLl9mb3JtU3RhdGUuaXNTdWJtaXR0ZWQpICYmXG4gICAgICAgICAgICAhZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMucmVWYWxpZGF0ZU1vZGUpLmlzT25TdWJtaXQpIHtcbiAgICAgICAgICAgIGlmIChjb250cm9sLl9vcHRpb25zLnJlc29sdmVyKSB7XG4gICAgICAgICAgICAgICAgY29udHJvbC5fcnVuU2NoZW1hKFtuYW1lXSkudGhlbigocmVzdWx0KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZ2V0KHJlc3VsdC5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ0Vycm9yID0gZ2V0KGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXhpc3RpbmdFcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgPyAoIWVycm9yICYmIGV4aXN0aW5nRXJyb3IudHlwZSkgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXJyb3IgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGV4aXN0aW5nRXJyb3IudHlwZSAhPT0gZXJyb3IudHlwZSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdFcnJvci5tZXNzYWdlICE9PSBlcnJvci5tZXNzYWdlKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDogZXJyb3IgJiYgZXJyb3IudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHNldChjb250cm9sLl9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lLCBlcnJvcilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JzOiBjb250cm9sLl9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkLl9mICYmXG4gICAgICAgICAgICAgICAgICAgICEoZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMucmVWYWxpZGF0ZU1vZGUpLmlzT25TdWJtaXQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIGdldFZhbGlkYXRpb25Nb2Rlcyhjb250cm9sLl9vcHRpb25zLm1vZGUpLmlzT25TdWJtaXQpKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbGlkYXRlRmllbGQoZmllbGQsIGNvbnRyb2wuX25hbWVzLmRpc2FibGVkLCBjb250cm9sLl9mb3JtVmFsdWVzLCBjb250cm9sLl9vcHRpb25zLmNyaXRlcmlhTW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLmFsbCwgY29udHJvbC5fb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uLCB0cnVlKS50aGVuKChlcnJvcikgPT4gIWlzRW1wdHlPYmplY3QoZXJyb3IpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnM6IHVwZGF0ZUZpZWxkQXJyYXlSb290RXJyb3IoY29udHJvbC5fZm9ybVN0YXRlLmVycm9ycywgZXJyb3IsIG5hbWUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMpLFxuICAgICAgICB9KTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgJiZcbiAgICAgICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihjb250cm9sLl9maWVsZHMsIChyZWYsIGtleSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChjb250cm9sLl9uYW1lcy5mb2N1cyAmJlxuICAgICAgICAgICAgICAgICAgICBrZXkuc3RhcnRzV2l0aChjb250cm9sLl9uYW1lcy5mb2N1cykgJiZcbiAgICAgICAgICAgICAgICAgICAgcmVmLmZvY3VzKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZi5mb2N1cygpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIGNvbnRyb2wuX25hbWVzLmZvY3VzID0gJyc7XG4gICAgICAgIGNvbnRyb2wuX3NldFZhbGlkKCk7XG4gICAgICAgIF9hY3Rpb25lZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgfSwgW2ZpZWxkcywgbmFtZSwgY29udHJvbF0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgICFnZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSkgJiYgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZU1vdW50ZWQgPSAobmFtZSwgdmFsdWUpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaWVsZCA9IGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlmIChmaWVsZCAmJiBmaWVsZC5fZikge1xuICAgICAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IHZhbHVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb250cm9sLl9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXIgfHwgc2hvdWxkVW5yZWdpc3RlclxuICAgICAgICAgICAgICAgID8gY29udHJvbC51bnJlZ2lzdGVyKG5hbWUpXG4gICAgICAgICAgICAgICAgOiB1cGRhdGVNb3VudGVkKG5hbWUsIGZhbHNlKTtcbiAgICAgICAgfTtcbiAgICB9LCBbbmFtZSwgY29udHJvbCwga2V5TmFtZSwgc2hvdWxkVW5yZWdpc3Rlcl0pO1xuICAgIHJldHVybiB7XG4gICAgICAgIHN3YXA6IFJlYWN0LnVzZUNhbGxiYWNrKHN3YXAsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgbW92ZTogUmVhY3QudXNlQ2FsbGJhY2sobW92ZSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICBwcmVwZW5kOiBSZWFjdC51c2VDYWxsYmFjayhwcmVwZW5kLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIGFwcGVuZDogUmVhY3QudXNlQ2FsbGJhY2soYXBwZW5kLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIHJlbW92ZTogUmVhY3QudXNlQ2FsbGJhY2socmVtb3ZlLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIGluc2VydDogUmVhY3QudXNlQ2FsbGJhY2soaW5zZXJ0JDEsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgdXBkYXRlOiBSZWFjdC51c2VDYWxsYmFjayh1cGRhdGUsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgcmVwbGFjZTogUmVhY3QudXNlQ2FsbGJhY2socmVwbGFjZSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICBmaWVsZHM6IFJlYWN0LnVzZU1lbW8oKCkgPT4gZmllbGRzLm1hcCgoZmllbGQsIGluZGV4KSA9PiAoe1xuICAgICAgICAgICAgLi4uZmllbGQsXG4gICAgICAgICAgICBba2V5TmFtZV06IGlkcy5jdXJyZW50W2luZGV4XSB8fCBnZW5lcmF0ZUlkKCksXG4gICAgICAgIH0pKSwgW2ZpZWxkcywga2V5TmFtZV0pLFxuICAgIH07XG59XG5cbi8qKlxuICogQ3VzdG9tIGhvb2sgdG8gbWFuYWdlIHRoZSBlbnRpcmUgZm9ybS5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybSkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLWdldC1zdGFydGVkLXRzLTVrc21tKSDigKIgW1ZpZGVvXShodHRwczovL3d3dy55b3V0dWJlLmNvbS93YXRjaD92PVJrWHY0QVhYQ180KVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIGZvcm0gY29uZmlndXJhdGlvbiBhbmQgdmFsaWRhdGlvbiBwYXJhbWV0ZXJzLlxuICpcbiAqIEByZXR1cm5zIG1ldGhvZHMgLSBpbmRpdmlkdWFsIGZ1bmN0aW9ucyB0byBtYW5hZ2UgdGhlIGZvcm0gc3RhdGUuIHtAbGluayBVc2VGb3JtUmV0dXJufVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciwgaGFuZGxlU3VibWl0LCB3YXRjaCwgZm9ybVN0YXRlOiB7IGVycm9ycyB9IH0gPSB1c2VGb3JtKCk7XG4gKiAgIGNvbnN0IG9uU3VibWl0ID0gZGF0YSA9PiBjb25zb2xlLmxvZyhkYXRhKTtcbiAqXG4gKiAgIGNvbnNvbGUubG9nKHdhdGNoKFwiZXhhbXBsZVwiKSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgIDxpbnB1dCBkZWZhdWx0VmFsdWU9XCJ0ZXN0XCIgey4uLnJlZ2lzdGVyKFwiZXhhbXBsZVwiKX0gLz5cbiAqICAgICAgIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJleGFtcGxlUmVxdWlyZWRcIiwgeyByZXF1aXJlZDogdHJ1ZSB9KX0gLz5cbiAqICAgICAgIHtlcnJvcnMuZXhhbXBsZVJlcXVpcmVkICYmIDxzcGFuPlRoaXMgZmllbGQgaXMgcmVxdWlyZWQ8L3NwYW4+fVxuICogICAgICAgPGJ1dHRvbj5TdWJtaXQ8L2J1dHRvbj5cbiAqICAgICA8L2Zvcm0+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gdXNlRm9ybShwcm9wcyA9IHt9KSB7XG4gICAgY29uc3QgX2Zvcm1Db250cm9sID0gUmVhY3QudXNlUmVmKHVuZGVmaW5lZCk7XG4gICAgY29uc3QgX3ZhbHVlcyA9IFJlYWN0LnVzZVJlZih1bmRlZmluZWQpO1xuICAgIGNvbnN0IFtmb3JtU3RhdGUsIHVwZGF0ZUZvcm1TdGF0ZV0gPSBSZWFjdC51c2VTdGF0ZSh7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkYXRpbmc6IGZhbHNlLFxuICAgICAgICBpc0xvYWRpbmc6IGlzRnVuY3Rpb24ocHJvcHMuZGVmYXVsdFZhbHVlcyksXG4gICAgICAgIGlzU3VibWl0dGVkOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIHN1Ym1pdENvdW50OiAwLFxuICAgICAgICBkaXJ0eUZpZWxkczoge30sXG4gICAgICAgIHRvdWNoZWRGaWVsZHM6IHt9LFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiB7fSxcbiAgICAgICAgZXJyb3JzOiBwcm9wcy5lcnJvcnMgfHwge30sXG4gICAgICAgIGRpc2FibGVkOiBwcm9wcy5kaXNhYmxlZCB8fCBmYWxzZSxcbiAgICAgICAgaXNSZWFkeTogZmFsc2UsXG4gICAgICAgIGRlZmF1bHRWYWx1ZXM6IGlzRnVuY3Rpb24ocHJvcHMuZGVmYXVsdFZhbHVlcylcbiAgICAgICAgICAgID8gdW5kZWZpbmVkXG4gICAgICAgICAgICA6IHByb3BzLmRlZmF1bHRWYWx1ZXMsXG4gICAgfSk7XG4gICAgaWYgKCFfZm9ybUNvbnRyb2wuY3VycmVudCkge1xuICAgICAgICBpZiAocHJvcHMuZm9ybUNvbnRyb2wpIHtcbiAgICAgICAgICAgIF9mb3JtQ29udHJvbC5jdXJyZW50ID0ge1xuICAgICAgICAgICAgICAgIC4uLnByb3BzLmZvcm1Db250cm9sLFxuICAgICAgICAgICAgICAgIGZvcm1TdGF0ZSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBpZiAocHJvcHMuZGVmYXVsdFZhbHVlcyAmJiAhaXNGdW5jdGlvbihwcm9wcy5kZWZhdWx0VmFsdWVzKSkge1xuICAgICAgICAgICAgICAgIHByb3BzLmZvcm1Db250cm9sLnJlc2V0KHByb3BzLmRlZmF1bHRWYWx1ZXMsIHByb3BzLnJlc2V0T3B0aW9ucyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCB7IGZvcm1Db250cm9sLCAuLi5yZXN0IH0gPSBjcmVhdGVGb3JtQ29udHJvbChwcm9wcyk7XG4gICAgICAgICAgICBfZm9ybUNvbnRyb2wuY3VycmVudCA9IHtcbiAgICAgICAgICAgICAgICAuLi5yZXN0LFxuICAgICAgICAgICAgICAgIGZvcm1TdGF0ZSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgY29udHJvbCA9IF9mb3JtQ29udHJvbC5jdXJyZW50LmNvbnRyb2w7XG4gICAgY29udHJvbC5fb3B0aW9ucyA9IHByb3BzO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgICBjb25zdCBzdWIgPSBjb250cm9sLl9zdWJzY3JpYmUoe1xuICAgICAgICAgICAgZm9ybVN0YXRlOiBjb250cm9sLl9wcm94eUZvcm1TdGF0ZSxcbiAgICAgICAgICAgIGNhbGxiYWNrOiAoKSA9PiB1cGRhdGVGb3JtU3RhdGUoeyAuLi5jb250cm9sLl9mb3JtU3RhdGUgfSksXG4gICAgICAgICAgICByZVJlbmRlclJvb3Q6IHRydWUsXG4gICAgICAgIH0pO1xuICAgICAgICB1cGRhdGVGb3JtU3RhdGUoKGRhdGEpID0+ICh7XG4gICAgICAgICAgICAuLi5kYXRhLFxuICAgICAgICAgICAgaXNSZWFkeTogdHJ1ZSxcbiAgICAgICAgfSkpO1xuICAgICAgICBjb250cm9sLl9mb3JtU3RhdGUuaXNSZWFkeSA9IHRydWU7XG4gICAgICAgIHJldHVybiBzdWI7XG4gICAgfSwgW2NvbnRyb2xdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4gY29udHJvbC5fZGlzYWJsZUZvcm0ocHJvcHMuZGlzYWJsZWQpLCBbY29udHJvbCwgcHJvcHMuZGlzYWJsZWRdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAocHJvcHMubW9kZSkge1xuICAgICAgICAgICAgY29udHJvbC5fb3B0aW9ucy5tb2RlID0gcHJvcHMubW9kZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocHJvcHMucmVWYWxpZGF0ZU1vZGUpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX29wdGlvbnMucmVWYWxpZGF0ZU1vZGUgPSBwcm9wcy5yZVZhbGlkYXRlTW9kZTtcbiAgICAgICAgfVxuICAgIH0sIFtjb250cm9sLCBwcm9wcy5tb2RlLCBwcm9wcy5yZVZhbGlkYXRlTW9kZV0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChwcm9wcy5lcnJvcnMpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3NldEVycm9ycyhwcm9wcy5lcnJvcnMpO1xuICAgICAgICAgICAgY29udHJvbC5fZm9jdXNFcnJvcigpO1xuICAgICAgICB9XG4gICAgfSwgW2NvbnRyb2wsIHByb3BzLmVycm9yc10pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHByb3BzLnNob3VsZFVucmVnaXN0ZXIgJiZcbiAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIHZhbHVlczogY29udHJvbC5fZ2V0V2F0Y2goKSxcbiAgICAgICAgICAgIH0pO1xuICAgIH0sIFtjb250cm9sLCBwcm9wcy5zaG91bGRVbnJlZ2lzdGVyXSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKGNvbnRyb2wuX3Byb3h5Rm9ybVN0YXRlLmlzRGlydHkpIHtcbiAgICAgICAgICAgIGNvbnN0IGlzRGlydHkgPSBjb250cm9sLl9nZXREaXJ0eSgpO1xuICAgICAgICAgICAgaWYgKGlzRGlydHkgIT09IGZvcm1TdGF0ZS5pc0RpcnR5KSB7XG4gICAgICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgIGlzRGlydHksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbCwgZm9ybVN0YXRlLmlzRGlydHldKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAocHJvcHMudmFsdWVzICYmICFkZWVwRXF1YWwocHJvcHMudmFsdWVzLCBfdmFsdWVzLmN1cnJlbnQpKSB7XG4gICAgICAgICAgICBjb250cm9sLl9yZXNldChwcm9wcy52YWx1ZXMsIHtcbiAgICAgICAgICAgICAgICBrZWVwRmllbGRzUmVmOiB0cnVlLFxuICAgICAgICAgICAgICAgIC4uLmNvbnRyb2wuX29wdGlvbnMucmVzZXRPcHRpb25zLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBfdmFsdWVzLmN1cnJlbnQgPSBwcm9wcy52YWx1ZXM7XG4gICAgICAgICAgICB1cGRhdGVGb3JtU3RhdGUoKHN0YXRlKSA9PiAoeyAuLi5zdGF0ZSB9KSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb250cm9sLl9yZXNldERlZmF1bHRWYWx1ZXMoKTtcbiAgICAgICAgfVxuICAgIH0sIFtjb250cm9sLCBwcm9wcy52YWx1ZXNdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIWNvbnRyb2wuX3N0YXRlLm1vdW50KSB7XG4gICAgICAgICAgICBjb250cm9sLl9zZXRWYWxpZCgpO1xuICAgICAgICAgICAgY29udHJvbC5fc3RhdGUubW91bnQgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChjb250cm9sLl9zdGF0ZS53YXRjaCkge1xuICAgICAgICAgICAgY29udHJvbC5fc3RhdGUud2F0Y2ggPSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoeyAuLi5jb250cm9sLl9mb3JtU3RhdGUgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY29udHJvbC5fcmVtb3ZlVW5tb3VudGVkKCk7XG4gICAgfSk7XG4gICAgX2Zvcm1Db250cm9sLmN1cnJlbnQuZm9ybVN0YXRlID0gZ2V0UHJveHlGb3JtU3RhdGUoZm9ybVN0YXRlLCBjb250cm9sKTtcbiAgICByZXR1cm4gX2Zvcm1Db250cm9sLmN1cnJlbnQ7XG59XG5cbmV4cG9ydCB7IENvbnRyb2xsZXIsIEZvcm0sIEZvcm1Qcm92aWRlciwgYXBwZW5kRXJyb3JzLCBjcmVhdGVGb3JtQ29udHJvbCwgZ2V0LCBzZXQsIHVzZUNvbnRyb2xsZXIsIHVzZUZpZWxkQXJyYXksIHVzZUZvcm0sIHVzZUZvcm1Db250ZXh0LCB1c2VGb3JtU3RhdGUsIHVzZVdhdGNoIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5lc20ubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiaXNDaGVja0JveElucHV0IiwiZWxlbWVudCIsInR5cGUiLCJpc0RhdGVPYmplY3QiLCJ2YWx1ZSIsIkRhdGUiLCJpc051bGxPclVuZGVmaW5lZCIsImlzT2JqZWN0VHlwZSIsImlzT2JqZWN0IiwiQXJyYXkiLCJpc0FycmF5IiwiZ2V0RXZlbnRWYWx1ZSIsImV2ZW50IiwidGFyZ2V0IiwiY2hlY2tlZCIsImdldE5vZGVQYXJlbnROYW1lIiwibmFtZSIsInN1YnN0cmluZyIsInNlYXJjaCIsImlzTmFtZUluRmllbGRBcnJheSIsIm5hbWVzIiwiaGFzIiwiaXNQbGFpbk9iamVjdCIsInRlbXBPYmplY3QiLCJwcm90b3R5cGVDb3B5IiwiY29uc3RydWN0b3IiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImlzV2ViIiwid2luZG93IiwiSFRNTEVsZW1lbnQiLCJkb2N1bWVudCIsImNsb25lT2JqZWN0IiwiZGF0YSIsImNvcHkiLCJpc0ZpbGVMaXN0SW5zdGFuY2UiLCJGaWxlTGlzdCIsIkJsb2IiLCJrZXkiLCJpc0tleSIsInRlc3QiLCJpc1VuZGVmaW5lZCIsInZhbCIsInVuZGVmaW5lZCIsImNvbXBhY3QiLCJmaWx0ZXIiLCJCb29sZWFuIiwic3RyaW5nVG9QYXRoIiwiaW5wdXQiLCJyZXBsYWNlIiwic3BsaXQiLCJnZXQiLCJvYmplY3QiLCJwYXRoIiwiZGVmYXVsdFZhbHVlIiwicmVzdWx0IiwicmVkdWNlIiwiaXNCb29sZWFuIiwic2V0IiwiaW5kZXgiLCJ0ZW1wUGF0aCIsImxlbmd0aCIsImxhc3RJbmRleCIsIm5ld1ZhbHVlIiwib2JqVmFsdWUiLCJpc05hTiIsIkVWRU5UUyIsIkJMVVIiLCJGT0NVU19PVVQiLCJDSEFOR0UiLCJWQUxJREFUSU9OX01PREUiLCJvbkJsdXIiLCJvbkNoYW5nZSIsIm9uU3VibWl0Iiwib25Ub3VjaGVkIiwiYWxsIiwiSU5QVVRfVkFMSURBVElPTl9SVUxFUyIsIm1heCIsIm1pbiIsIm1heExlbmd0aCIsIm1pbkxlbmd0aCIsInBhdHRlcm4iLCJyZXF1aXJlZCIsInZhbGlkYXRlIiwiSG9va0Zvcm1Db250ZXh0IiwiY3JlYXRlQ29udGV4dCIsImRpc3BsYXlOYW1lIiwidXNlRm9ybUNvbnRleHQiLCJ1c2VDb250ZXh0IiwiRm9ybVByb3ZpZGVyIiwicHJvcHMiLCJjaGlsZHJlbiIsImNyZWF0ZUVsZW1lbnQiLCJQcm92aWRlciIsImdldFByb3h5Rm9ybVN0YXRlIiwiZm9ybVN0YXRlIiwiY29udHJvbCIsImxvY2FsUHJveHlGb3JtU3RhdGUiLCJpc1Jvb3QiLCJkZWZhdWx0VmFsdWVzIiwiX2RlZmF1bHRWYWx1ZXMiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsIl9rZXkiLCJfcHJveHlGb3JtU3RhdGUiLCJ1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IiwidXNlTGF5b3V0RWZmZWN0IiwidXNlRWZmZWN0IiwidXNlRm9ybVN0YXRlIiwibWV0aG9kcyIsImRpc2FibGVkIiwiZXhhY3QiLCJ1cGRhdGVGb3JtU3RhdGUiLCJ1c2VTdGF0ZSIsIl9mb3JtU3RhdGUiLCJfbG9jYWxQcm94eUZvcm1TdGF0ZSIsInVzZVJlZiIsImlzRGlydHkiLCJpc0xvYWRpbmciLCJkaXJ0eUZpZWxkcyIsInRvdWNoZWRGaWVsZHMiLCJ2YWxpZGF0aW5nRmllbGRzIiwiaXNWYWxpZGF0aW5nIiwiaXNWYWxpZCIsImVycm9ycyIsIl9zdWJzY3JpYmUiLCJjdXJyZW50IiwiY2FsbGJhY2siLCJfc2V0VmFsaWQiLCJ1c2VNZW1vIiwiaXNTdHJpbmciLCJnZW5lcmF0ZVdhdGNoT3V0cHV0IiwiX25hbWVzIiwiZm9ybVZhbHVlcyIsImlzR2xvYmFsIiwid2F0Y2giLCJhZGQiLCJtYXAiLCJmaWVsZE5hbWUiLCJ3YXRjaEFsbCIsImlzUHJpbWl0aXZlIiwiZGVlcEVxdWFsIiwib2JqZWN0MSIsIm9iamVjdDIiLCJfaW50ZXJuYWxfdmlzaXRlZCIsIldlYWtTZXQiLCJnZXRUaW1lIiwia2V5czEiLCJrZXlzIiwia2V5czIiLCJ2YWwxIiwiaW5jbHVkZXMiLCJ2YWwyIiwidXNlV2F0Y2giLCJjb21wdXRlIiwiX2RlZmF1bHRWYWx1ZSIsIl9jb21wdXRlIiwiX2NvbXB1dGVGb3JtVmFsdWVzIiwiZGVmYXVsdFZhbHVlTWVtbyIsIl9nZXRXYXRjaCIsInVwZGF0ZVZhbHVlIiwidmFsdWVzIiwiX2Zvcm1WYWx1ZXMiLCJjb21wdXRlZEZvcm1WYWx1ZXMiLCJfcmVtb3ZlVW5tb3VudGVkIiwidXNlQ29udHJvbGxlciIsInNob3VsZFVucmVnaXN0ZXIiLCJpc0FycmF5RmllbGQiLCJhcnJheSIsIl9wcm9wcyIsIl9yZWdpc3RlclByb3BzIiwicmVnaXN0ZXIiLCJydWxlcyIsImZpZWxkU3RhdGUiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiaW52YWxpZCIsImVudW1lcmFibGUiLCJpc1RvdWNoZWQiLCJlcnJvciIsInVzZUNhbGxiYWNrIiwicmVmIiwiZWxtIiwiZmllbGQiLCJfZmllbGRzIiwiX2YiLCJmb2N1cyIsInNlbGVjdCIsInNldEN1c3RvbVZhbGlkaXR5IiwibWVzc2FnZSIsInJlcG9ydFZhbGlkaXR5IiwiX3Nob3VsZFVucmVnaXN0ZXJGaWVsZCIsIl9vcHRpb25zIiwidXBkYXRlTW91bnRlZCIsIm1vdW50IiwiX3N0YXRlIiwiYWN0aW9uIiwidW5yZWdpc3RlciIsIl9zZXREaXNhYmxlZEZpZWxkIiwiQ29udHJvbGxlciIsInJlbmRlciIsImZsYXR0ZW4iLCJvYmoiLCJvdXRwdXQiLCJuZXN0ZWQiLCJuZXN0ZWRLZXkiLCJQT1NUX1JFUVVFU1QiLCJGb3JtIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJtZXRob2QiLCJoZWFkZXJzIiwiZW5jVHlwZSIsIm9uRXJyb3IiLCJvblN1Y2Nlc3MiLCJ2YWxpZGF0ZVN0YXR1cyIsInJlc3QiLCJzdWJtaXQiLCJoYXNFcnJvciIsImhhbmRsZVN1Ym1pdCIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJmb3JtRGF0YUpzb24iLCJKU09OIiwic3RyaW5naWZ5IiwiX2EiLCJmbGF0dGVuRm9ybVZhbHVlcyIsImFwcGVuZCIsInNob3VsZFN0cmluZ2lmeVN1Ym1pc3Npb25EYXRhIiwic29tZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJTdHJpbmciLCJib2R5Iiwic3RhdHVzIiwiX3N1YmplY3RzIiwic3RhdGUiLCJuZXh0IiwiaXNTdWJtaXRTdWNjZXNzZnVsIiwic2V0RXJyb3IiLCJGcmFnbWVudCIsIm5vVmFsaWRhdGUiLCJhcHBlbmRFcnJvcnMiLCJ2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEiLCJ0eXBlcyIsImNvbnZlcnRUb0FycmF5UGF5bG9hZCIsImNyZWF0ZVN1YmplY3QiLCJfb2JzZXJ2ZXJzIiwib2JzZXJ2ZXIiLCJzdWJzY3JpYmUiLCJwdXNoIiwidW5zdWJzY3JpYmUiLCJvIiwib2JzZXJ2ZXJzIiwiaXNFbXB0eU9iamVjdCIsImlzRmlsZUlucHV0IiwiaXNGdW5jdGlvbiIsImlzSFRNTEVsZW1lbnQiLCJvd25lciIsIm93bmVyRG9jdW1lbnQiLCJkZWZhdWx0VmlldyIsImlzTXVsdGlwbGVTZWxlY3QiLCJpc1JhZGlvSW5wdXQiLCJpc1JhZGlvT3JDaGVja2JveCIsImxpdmUiLCJpc0Nvbm5lY3RlZCIsImJhc2VHZXQiLCJ1cGRhdGVQYXRoIiwic2xpY2UiLCJpc0VtcHR5QXJyYXkiLCJ1bnNldCIsInBhdGhzIiwiY2hpbGRPYmplY3QiLCJvYmplY3RIYXNGdW5jdGlvbiIsIm1hcmtGaWVsZHNEaXJ0eSIsImZpZWxkcyIsImlzUGFyZW50Tm9kZUFycmF5IiwiZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyIsImRpcnR5RmllbGRzRnJvbVZhbHVlcyIsImdldERpcnR5RmllbGRzIiwiZGVmYXVsdFJlc3VsdCIsInZhbGlkUmVzdWx0IiwiZ2V0Q2hlY2tib3hWYWx1ZSIsIm9wdGlvbnMiLCJvcHRpb24iLCJhdHRyaWJ1dGVzIiwiZ2V0RmllbGRWYWx1ZUFzIiwidmFsdWVBc051bWJlciIsInZhbHVlQXNEYXRlIiwic2V0VmFsdWVBcyIsIk5hTiIsImRlZmF1bHRSZXR1cm4iLCJnZXRSYWRpb1ZhbHVlIiwicHJldmlvdXMiLCJnZXRGaWVsZFZhbHVlIiwiZmlsZXMiLCJyZWZzIiwic2VsZWN0ZWRPcHRpb25zIiwiZ2V0UmVzb2x2ZXJPcHRpb25zIiwiZmllbGRzTmFtZXMiLCJjcml0ZXJpYU1vZGUiLCJzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uIiwiaXNSZWdleCIsIlJlZ0V4cCIsImdldFJ1bGVWYWx1ZSIsInJ1bGUiLCJzb3VyY2UiLCJnZXRWYWxpZGF0aW9uTW9kZXMiLCJtb2RlIiwiaXNPblN1Ym1pdCIsImlzT25CbHVyIiwiaXNPbkNoYW5nZSIsImlzT25BbGwiLCJpc09uVG91Y2giLCJBU1lOQ19GVU5DVElPTiIsImhhc1Byb21pc2VWYWxpZGF0aW9uIiwiZmllbGRSZWZlcmVuY2UiLCJmaW5kIiwidmFsaWRhdGVGdW5jdGlvbiIsImhhc1ZhbGlkYXRpb24iLCJpc1dhdGNoZWQiLCJpc0JsdXJFdmVudCIsIndhdGNoTmFtZSIsInN0YXJ0c1dpdGgiLCJpdGVyYXRlRmllbGRzQnlBY3Rpb24iLCJhYm9ydEVhcmx5IiwiY3VycmVudEZpZWxkIiwic2NoZW1hRXJyb3JMb29rdXAiLCJqb2luIiwiZm91bmRFcnJvciIsInJvb3QiLCJwb3AiLCJzaG91bGRSZW5kZXJGb3JtU3RhdGUiLCJmb3JtU3RhdGVEYXRhIiwic2hvdWxkU3Vic2NyaWJlQnlOYW1lIiwic2lnbmFsTmFtZSIsImN1cnJlbnROYW1lIiwic2tpcFZhbGlkYXRpb24iLCJpc1N1Ym1pdHRlZCIsInJlVmFsaWRhdGVNb2RlIiwidW5zZXRFbXB0eUFycmF5IiwidXBkYXRlRmllbGRBcnJheVJvb3RFcnJvciIsImZpZWxkQXJyYXlFcnJvcnMiLCJpc01lc3NhZ2UiLCJnZXRWYWxpZGF0ZUVycm9yIiwiZXZlcnkiLCJnZXRWYWx1ZUFuZE1lc3NhZ2UiLCJ2YWxpZGF0aW9uRGF0YSIsInZhbGlkYXRlRmllbGQiLCJkaXNhYmxlZEZpZWxkTmFtZXMiLCJpc0ZpZWxkQXJyYXkiLCJpbnB1dFZhbHVlIiwiaW5wdXRSZWYiLCJpc1JhZGlvIiwiaXNDaGVja0JveCIsImlzRW1wdHkiLCJhcHBlbmRFcnJvcnNDdXJyeSIsImJpbmQiLCJnZXRNaW5NYXhNZXNzYWdlIiwiZXhjZWVkTWF4IiwibWF4TGVuZ3RoTWVzc2FnZSIsIm1pbkxlbmd0aE1lc3NhZ2UiLCJtYXhUeXBlIiwibWluVHlwZSIsImV4Y2VlZE1pbiIsIm1heE91dHB1dCIsIm1pbk91dHB1dCIsInZhbHVlTnVtYmVyIiwidmFsdWVEYXRlIiwiY29udmVydFRpbWVUb0RhdGUiLCJ0aW1lIiwidG9EYXRlU3RyaW5nIiwiaXNUaW1lIiwiaXNXZWVrIiwibWF4TGVuZ3RoT3V0cHV0IiwibWluTGVuZ3RoT3V0cHV0IiwicGF0dGVyblZhbHVlIiwibWF0Y2giLCJ2YWxpZGF0ZUVycm9yIiwidmFsaWRhdGlvblJlc3VsdCIsImRlZmF1bHRPcHRpb25zIiwic2hvdWxkRm9jdXNFcnJvciIsImNyZWF0ZUZvcm1Db250cm9sIiwic3VibWl0Q291bnQiLCJpc1JlYWR5IiwiaXNTdWJtaXR0aW5nIiwiU2V0IiwidW5Nb3VudCIsImRlbGF5RXJyb3JDYWxsYmFjayIsInRpbWVyIiwiX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlIiwic2hvdWxkRGlzcGxheUFsbEFzc29jaWF0ZWRFcnJvcnMiLCJkZWJvdW5jZSIsIndhaXQiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0Iiwic2hvdWxkVXBkYXRlVmFsaWQiLCJyZXNvbHZlciIsIl9ydW5TY2hlbWEiLCJleGVjdXRlQnVpbHRJblZhbGlkYXRpb24iLCJfdXBkYXRlSXNWYWxpZGF0aW5nIiwiZnJvbSIsImZvckVhY2giLCJfc2V0RmllbGRBcnJheSIsImFyZ3MiLCJzaG91bGRTZXRWYWx1ZXMiLCJzaG91bGRVcGRhdGVGaWVsZHNBbmRTdGF0ZSIsImZpZWxkVmFsdWVzIiwiYXJnQSIsImFyZ0IiLCJfZ2V0RGlydHkiLCJ1cGRhdGVFcnJvcnMiLCJfc2V0RXJyb3JzIiwidXBkYXRlVmFsaWRBbmRWYWx1ZSIsInNob3VsZFNraXBTZXRWYWx1ZUFzIiwiZGVmYXVsdENoZWNrZWQiLCJzZXRGaWVsZFZhbHVlIiwidXBkYXRlVG91Y2hBbmREaXJ0eSIsImZpZWxkVmFsdWUiLCJzaG91bGREaXJ0eSIsInNob3VsZFJlbmRlciIsInNob3VsZFVwZGF0ZUZpZWxkIiwiaXNQcmV2aW91c0RpcnR5IiwiaXNDdXJyZW50RmllbGRQcmlzdGluZSIsImlzUHJldmlvdXNGaWVsZFRvdWNoZWQiLCJzaG91bGRSZW5kZXJCeUVycm9yIiwicHJldmlvdXNGaWVsZEVycm9yIiwiZGVsYXlFcnJvciIsInVwZGF0ZWRGb3JtU3RhdGUiLCJjb250ZXh0IiwiZXhlY3V0ZVNjaGVtYUFuZFVwZGF0ZVN0YXRlIiwic2hvdWxkT25seUNoZWNrVmFsaWQiLCJ2YWxpZCIsImlzRmllbGRBcnJheVJvb3QiLCJpc1Byb21pc2VGdW5jdGlvbiIsImZpZWxkRXJyb3IiLCJnZXRWYWx1ZXMiLCJfZ2V0RmllbGRBcnJheSIsIm9wdGlvblJlZiIsInNlbGVjdGVkIiwiY2hlY2tib3hSZWYiLCJyYWRpb1JlZiIsInNob3VsZFRvdWNoIiwic2hvdWxkVmFsaWRhdGUiLCJ0cmlnZ2VyIiwic2V0VmFsdWVzIiwiZmllbGRLZXkiLCJzZXRWYWx1ZSIsImNsb25lVmFsdWUiLCJpc0ZpZWxkVmFsdWVVcGRhdGVkIiwiX3VwZGF0ZUlzRmllbGRWYWx1ZVVwZGF0ZWQiLCJOdW1iZXIiLCJ2YWxpZGF0aW9uTW9kZUJlZm9yZVN1Ym1pdCIsInZhbGlkYXRpb25Nb2RlQWZ0ZXJTdWJtaXQiLCJzaG91bGRTa2lwVmFsaWRhdGlvbiIsImRlcHMiLCJ3YXRjaGVkIiwicHJldmlvdXNFcnJvckxvb2t1cFJlc3VsdCIsImVycm9yTG9va3VwUmVzdWx0IiwiX2ZvY3VzSW5wdXQiLCJmaWVsZE5hbWVzIiwiUHJvbWlzZSIsInNob3VsZEZvY3VzIiwiZ2V0RmllbGRTdGF0ZSIsImNsZWFyRXJyb3JzIiwiaW5wdXROYW1lIiwiY3VycmVudEVycm9yIiwiY3VycmVudFJlZiIsInJlc3RPZkVycm9yVHJlZSIsInBheWxvYWQiLCJfc2V0Rm9ybVN0YXRlIiwicmVSZW5kZXJSb290IiwiZGVsZXRlIiwia2VlcFZhbHVlIiwia2VlcEVycm9yIiwia2VlcERpcnR5Iiwia2VlcFRvdWNoZWQiLCJrZWVwSXNWYWxpZGF0aW5nIiwia2VlcERlZmF1bHRWYWx1ZSIsImtlZXBJc1ZhbGlkIiwiZGlzYWJsZWRJc0RlZmluZWQiLCJwcm9ncmVzc2l2ZSIsImZpZWxkUmVmIiwicXVlcnlTZWxlY3RvckFsbCIsInJhZGlvT3JDaGVja2JveCIsIl9mb2N1c0Vycm9yIiwiX2Rpc2FibGVGb3JtIiwib25WYWxpZCIsIm9uSW52YWxpZCIsImUiLCJvblZhbGlkRXJyb3IiLCJwcmV2ZW50RGVmYXVsdCIsInBlcnNpc3QiLCJzaXplIiwicmVzZXRGaWVsZCIsIl9yZXNldCIsImtlZXBTdGF0ZU9wdGlvbnMiLCJ1cGRhdGVkVmFsdWVzIiwiY2xvbmVVcGRhdGVkVmFsdWVzIiwiaXNFbXB0eVJlc2V0VmFsdWVzIiwia2VlcERlZmF1bHRWYWx1ZXMiLCJrZWVwVmFsdWVzIiwia2VlcERpcnR5VmFsdWVzIiwiZmllbGRzVG9DaGVjayIsImZvcm0iLCJjbG9zZXN0IiwicmVzZXQiLCJrZWVwRmllbGRzUmVmIiwia2VlcFN1Ym1pdENvdW50Iiwia2VlcElzU3VibWl0dGVkIiwia2VlcEVycm9ycyIsImtlZXBJc1N1Ym1pdFN1Y2Nlc3NmdWwiLCJzZXRGb2N1cyIsInNob3VsZFNlbGVjdCIsIl9yZXNldERlZmF1bHRWYWx1ZXMiLCJ0aGVuIiwicmVzZXRPcHRpb25zIiwiZm9ybUNvbnRyb2wiLCJnZW5lcmF0ZUlkIiwiY3J5cHRvIiwicmFuZG9tVVVJRCIsImQiLCJwZXJmb3JtYW5jZSIsIm5vdyIsImMiLCJyIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwiZ2V0Rm9jdXNGaWVsZE5hbWUiLCJmb2N1c05hbWUiLCJmb2N1c0luZGV4IiwiYXBwZW5kQXQiLCJmaWxsRW1wdHlBcnJheSIsImluc2VydCIsIm1vdmVBcnJheUF0IiwidG8iLCJzcGxpY2UiLCJwcmVwZW5kQXQiLCJyZW1vdmVBdEluZGV4ZXMiLCJpbmRleGVzIiwiaSIsInRlbXAiLCJyZW1vdmVBcnJheUF0Iiwic29ydCIsImEiLCJiIiwic3dhcEFycmF5QXQiLCJpbmRleEEiLCJpbmRleEIiLCJ1cGRhdGVBdCIsInVzZUZpZWxkQXJyYXkiLCJrZXlOYW1lIiwic2V0RmllbGRzIiwiaWRzIiwiX2ZpZWxkSWRzIiwiX2FjdGlvbmVkIiwiZmllbGRBcnJheU5hbWUiLCJ1cGRhdGVWYWx1ZXMiLCJ1cGRhdGVkRmllbGRBcnJheVZhbHVlcyIsImFwcGVuZFZhbHVlIiwicHJlcGVuZCIsInByZXBlbmRWYWx1ZSIsInJlbW92ZSIsImluc2VydCQxIiwiaW5zZXJ0VmFsdWUiLCJzd2FwIiwibW92ZSIsInVwZGF0ZSIsIml0ZW0iLCJleGlzdGluZ0Vycm9yIiwidXNlRm9ybSIsIl9mb3JtQ29udHJvbCIsIl92YWx1ZXMiLCJzdWIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;