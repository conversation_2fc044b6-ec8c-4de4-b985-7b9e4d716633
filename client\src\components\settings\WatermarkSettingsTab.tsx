'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { settingsService } from '@/lib/services';
import { StudioSettings } from '@/types';
import { Droplets, Eye, Move, Sliders } from 'lucide-react';

interface WatermarkSettingsData {
  enableWatermark: boolean;
  watermarkPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  watermarkOpacity: number;
  watermarkScale: number;
  watermarkMargin: number;
}

interface WatermarkSettingsTabProps {
  settings: StudioSettings | null;
  onSettingsUpdate: (settings: StudioSettings) => void;
}

const POSITION_OPTIONS = [
  { value: 'top-left', label: 'Top Left', icon: '↖️' },
  { value: 'top-right', label: 'Top Right', icon: '↗️' },
  { value: 'bottom-left', label: 'Bottom Left', icon: '↙️' },
  { value: 'bottom-right', label: 'Bottom Right', icon: '↘️' },
  { value: 'center', label: 'Center', icon: '🎯' },
];

export default function WatermarkSettingsTab({ 
  settings, 
  onSettingsUpdate 
}: WatermarkSettingsTabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<WatermarkSettingsData>({
    defaultValues: {
      enableWatermark: settings?.enableWatermark ?? true,
      watermarkPosition: (settings?.watermarkPosition as any) ?? 'bottom-right',
      watermarkOpacity: settings?.watermarkOpacity ?? 70,
      watermarkScale: settings?.watermarkScale ?? 0.15,
      watermarkMargin: settings?.watermarkMargin ?? 20,
    },
  });

  const watchEnableWatermark = watch('enableWatermark');
  const watchOpacity = watch('watermarkOpacity');
  const watchScale = watch('watermarkScale');
  const watchMargin = watch('watermarkMargin');

  const onSubmit = async (data: WatermarkSettingsData) => {
    setIsLoading(true);
    try {
      const updatedSettings = await settingsService.updateWatermarkSettings(data);
      onSettingsUpdate(updatedSettings);
      toast.success('Watermark settings updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update watermark settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Droplets className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Watermark Settings</h3>
            <p className="text-sm text-gray-600">
              Configure how your logo appears on uploaded images
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Enable Watermark */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Eye className="h-5 w-5 text-gray-600" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Enable Watermark</h4>
                <p className="text-sm text-gray-600">
                  Automatically add your logo to all uploaded images
                </p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                {...register('enableWatermark')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {watchEnableWatermark && (
            <>
              {/* Watermark Position */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Move className="h-5 w-5 text-gray-600" />
                  <label className="text-sm font-medium text-gray-700">
                    Watermark Position
                  </label>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  {POSITION_OPTIONS.map((option) => (
                    <label
                      key={option.value}
                      className="relative flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors"
                    >
                      <input
                        type="radio"
                        value={option.value}
                        {...register('watermarkPosition')}
                        className="sr-only peer"
                      />
                      <div className="text-center peer-checked:text-blue-600">
                        <div className="text-2xl mb-1">{option.icon}</div>
                        <div className="text-xs font-medium">{option.label}</div>
                      </div>
                      <div className="absolute inset-0 border-2 border-transparent peer-checked:border-blue-500 rounded-lg pointer-events-none"></div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Opacity Slider */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Sliders className="h-5 w-5 text-gray-600" />
                    <label className="text-sm font-medium text-gray-700">
                      Opacity
                    </label>
                  </div>
                  <span className="text-sm text-gray-600">{watchOpacity}%</span>
                </div>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="5"
                  {...register('watermarkOpacity', { valueAsNumber: true })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* Scale Slider */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Sliders className="h-5 w-5 text-gray-600" />
                    <label className="text-sm font-medium text-gray-700">
                      Size (% of image width)
                    </label>
                  </div>
                  <span className="text-sm text-gray-600">{Math.round(watchScale * 100)}%</span>
                </div>
                <input
                  type="range"
                  min="0.05"
                  max="0.5"
                  step="0.01"
                  {...register('watermarkScale', { valueAsNumber: true })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* Margin Slider */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Sliders className="h-5 w-5 text-gray-600" />
                    <label className="text-sm font-medium text-gray-700">
                      Margin from edge
                    </label>
                  </div>
                  <span className="text-sm text-gray-600">{watchMargin}px</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="5"
                  {...register('watermarkMargin', { valueAsNumber: true })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* Preview Note */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="text-blue-600">💡</div>
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">Preview Note</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Watermark will be applied automatically to all new image uploads. 
                      Make sure you have uploaded your studio logo in the Studio tab first.
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Submit Button */}
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Saving...' : 'Save Watermark Settings'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
