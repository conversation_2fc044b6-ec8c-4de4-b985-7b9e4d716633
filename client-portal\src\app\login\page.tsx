'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Camera, LogIn } from 'lucide-react';
import { authService as authApi } from '@/lib/services';
import { authService } from '@/lib/auth';
import { LoginRequest } from '@/types';
import toast from 'react-hot-toast';

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<LoginRequest>();

  useEffect(() => {
    // Check if already authenticated
    if (authService.isAuthenticated()) {
      router.push('/gallery');
      return;
    }

    // Pre-fill client ID from URL if available
    const clientId = searchParams.get('id');
    if (clientId) {
      setValue('uniqueId', clientId);
    }
  }, [router, searchParams, setValue]);

  const onSubmit = async (data: LoginRequest) => {
    setIsLoading(true);
    console.log('Login attempt:', data);
    try {
      const response = await authApi.login(data);
      console.log('Login response:', response);
      authService.setAuth(response.token, response.client);
      toast.success(`Welcome, ${response.client.name}!`);
      router.push('/gallery');
    } catch (error: any) {
      console.error('Login error:', error);
      console.error('Error response:', error.response);
      toast.error(error.response?.data?.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
            <Camera className="h-8 w-8 text-primary-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Access Your Gallery
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter your credentials to view your photos
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <label htmlFor="uniqueId" className="block text-sm font-medium text-gray-700 mb-2">
                Client ID
              </label>
              <input
                {...register('uniqueId', {
                  required: 'Client ID is required',
                })}
                type="text"
                className="input-field"
                placeholder="Enter your client ID"
                autoComplete="username"
              />
              {errors.uniqueId && (
                <p className="mt-1 text-sm text-red-600">{errors.uniqueId.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  {...register('password', {
                    required: 'Password is required',
                  })}
                  type={showPassword ? 'text' : 'password'}
                  className="input-field pr-10"
                  placeholder="Enter your password"
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                <LogIn className="h-5 w-5 text-primary-500 group-hover:text-primary-400" />
              </span>
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Don't have your credentials? Contact your photographer.
            </p>
          </div>
        </form>

        {/* QR Code Scanner Info */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800 text-center">
            <strong>Tip:</strong> If you have a QR code from your photographer,
            scan it with your phone camera to automatically fill in your details.
          </p>
        </div>
      </div>
    </div>
  );
}
