'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Users, Calendar, Key, Mail, MessageSquare } from 'lucide-react';
import { settingsService } from '@/lib/services';
import { StudioSettings } from '@/types';
import toast from 'react-hot-toast';

interface ClientSettingsTabProps {
  settings: StudioSettings;
  onUpdate: (settings: StudioSettings) => void;
}

interface ClientSettingsData {
  defaultAccessDuration: number;
  autoGeneratePassword: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

export default function ClientSettingsTab({ settings, onUpdate }: ClientSettingsTabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<ClientSettingsData>({
    defaultValues: {
      defaultAccessDuration: settings?.defaultAccessDuration || 30,
      autoGeneratePassword: settings?.autoGeneratePassword || true,
      emailNotifications: settings?.emailNotifications || true,
      smsNotifications: settings?.smsNotifications || false,
    },
  });

  const watchAutoGenerate = watch('autoGeneratePassword');

  const onSubmit = async (data: ClientSettingsData) => {
    setIsLoading(true);
    try {
      const updatedSettings = await settingsService.updateClientSettings(data);
      onUpdate(updatedSettings);
      toast.success('Client settings updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update client settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Users className="h-6 w-6 text-primary-600 mr-2" />
        <h2 className="text-xl font-semibold text-gray-900">Client Settings</h2>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Access Settings */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Calendar className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Access Settings</h3>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Default Access Duration (days)
              </label>
              <select
                {...register('defaultAccessDuration', {
                  required: 'Access duration is required',
                  min: { value: 1, message: 'Minimum duration is 1 day' },
                  max: { value: 365, message: 'Maximum duration is 365 days' },
                  valueAsNumber: true,
                })}
                className="input-field"
              >
                <option value={7}>7 days</option>
                <option value={14}>14 days</option>
                <option value={30}>30 days</option>
                <option value={60}>60 days</option>
                <option value={90}>90 days</option>
                <option value={180}>180 days</option>
                <option value={365}>365 days</option>
              </select>
              {errors.defaultAccessDuration && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.defaultAccessDuration.message}
                </p>
              )}
              <p className="mt-1 text-sm text-gray-600">
                How long clients can access their galleries by default
              </p>
            </div>
          </div>
        </div>

        {/* Password Settings */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Key className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Password Settings</h3>
          </div>

          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Auto-generate Passwords</h4>
                <p className="text-sm text-gray-600">
                  Automatically generate secure passwords for new clients
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  {...register('autoGeneratePassword')}
                  type="checkbox"
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {watchAutoGenerate && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Auto-generated Password Rules</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 8 characters long</li>
                  <li>• Mix of letters and numbers</li>
                  <li>• Easy to share with clients</li>
                  <li>• Can be reset anytime</li>
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Mail className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Notification Settings</h3>
          </div>

          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                <p className="text-sm text-gray-600">
                  Send email notifications for client activities
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  {...register('emailNotifications')}
                  type="checkbox"
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">SMS Notifications</h4>
                <p className="text-sm text-gray-600">
                  Send SMS notifications for important updates
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  {...register('smsNotifications')}
                  type="checkbox"
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <MessageSquare className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-900">Notification Types</h4>
                  <ul className="text-sm text-yellow-800 mt-1 space-y-1">
                    <li>• New client registration</li>
                    <li>• Client login activity</li>
                    <li>• Image upload confirmations</li>
                    <li>• Gallery access notifications</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Current Settings Summary */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Current Settings Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Default Access Duration:</span>
                <span className="font-medium text-gray-900">{settings?.defaultAccessDuration || 30} days</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Auto-generate Passwords:</span>
                <span className={`font-medium ${settings?.autoGeneratePassword ? 'text-green-600' : 'text-red-600'}`}>
                  {settings?.autoGeneratePassword ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Email Notifications:</span>
                <span className={`font-medium ${settings?.emailNotifications ? 'text-green-600' : 'text-red-600'}`}>
                  {settings?.emailNotifications ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">SMS Notifications:</span>
                <span className={`font-medium ${settings?.smsNotifications ? 'text-green-600' : 'text-red-600'}`}>
                  {settings?.smsNotifications ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t border-gray-200">
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary disabled:opacity-50"
          >
            {isLoading ? 'Updating...' : 'Update Client Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
