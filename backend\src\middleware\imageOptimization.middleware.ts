import sharp from 'sharp';
import path from 'path';
import fs from 'fs/promises';
import { Request, Response, NextFunction } from 'express';
import { applyWatermark, isWatermarkAvailable } from '../services/watermark.service';
import { prisma } from '../utils/prisma';

interface OptimizedFile extends Express.Multer.File {
  originalSize?: number;
  compressedSize?: number;
  compressionRatio?: number;
}

/**
 * Image optimization configuration
 */
const OPTIMIZATION_CONFIG = {
  // Maximum dimensions
  maxWidth: 1920,
  maxHeight: 1080,
  
  // Quality settings
  jpeg: { quality: 85, progressive: true },
  png: { quality: 85, compressionLevel: 8 },
  webp: { quality: 85 },
  
  // Size thresholds
  maxFileSize: 2 * 1024 * 1024, // 2MB after compression
  enableWebP: true, // Convert to WebP for better compression
};

/**
 * Optimize single image with dual storage
 */
async function optimizeImage(file: OptimizedFile): Promise<OptimizedFile> {
  try {
    const originalSize = file.size;
    const inputPath = file.path;
    const ext = path.extname(file.originalname).toLowerCase();

    // Create paths for both original and optimized versions
    const baseName = path.basename(file.filename, path.extname(file.filename));
    const originalDir = path.join(path.dirname(inputPath), 'originals');
    const optimizedDir = path.dirname(inputPath);

    // Ensure directories exist
    await fs.mkdir(originalDir, { recursive: true });

    // Move original to 'originals' folder
    const originalPath = path.join(originalDir, file.filename);
    await fs.rename(inputPath, originalPath);

    // Create optimized version
    const outputExt = OPTIMIZATION_CONFIG.enableWebP ? '.webp' : ext;
    const outputFilename = `${baseName}${outputExt}`;
    const outputPath = path.join(optimizedDir, outputFilename);
    
    // Sharp processing pipeline
    let pipeline = sharp(inputPath)
      .resize(OPTIMIZATION_CONFIG.maxWidth, OPTIMIZATION_CONFIG.maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      });
    
    // Apply format-specific optimizations
    if (OPTIMIZATION_CONFIG.enableWebP) {
      pipeline = pipeline.webp(OPTIMIZATION_CONFIG.webp);
    } else {
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          pipeline = pipeline.jpeg(OPTIMIZATION_CONFIG.jpeg);
          break;
        case '.png':
          pipeline = pipeline.png(OPTIMIZATION_CONFIG.png);
          break;
        default:
          pipeline = pipeline.jpeg(OPTIMIZATION_CONFIG.jpeg);
      }
    }
    
    // Process and save optimized image from original
    await pipeline.toFile(outputPath);

    // Get optimized file stats
    const stats = await fs.stat(outputPath);
    const compressedSize = stats.size;

    // Always keep both versions
    // Update file object to point to optimized version for display
    file.path = outputPath;
    file.filename = outputFilename;
    file.size = compressedSize;
    file.originalSize = originalSize;
    file.compressedSize = compressedSize;
    file.compressionRatio = Math.round(((originalSize - compressedSize) / originalSize) * 100);

    // Store paths for both versions
    (file as any).originalPath = originalPath;
    (file as any).optimizedPath = outputPath;

    // Update mimetype if converted to WebP
    if (OPTIMIZATION_CONFIG.enableWebP) {
      file.mimetype = 'image/webp';
    }
    
    return file;
  } catch (error) {
    console.error('Image optimization failed:', error);
    // Return original file if optimization fails
    return file;
  }
}

/**
 * Apply watermark to optimized image if studio has watermark enabled
 */
const applyWatermarkToImage = async (file: OptimizedFile, studioId: string) => {
  try {
    // Check if studio has watermark settings
    const studioSettings = await prisma.studioSettings.findUnique({
      where: { studioId },
    });

    if (!studioSettings?.enableWatermark || !studioSettings?.logoUrl) {
      console.log(`Watermark disabled or no logo for studio ${studioId}`);
      return;
    }

    // Check if watermark is available
    const watermarkAvailable = await isWatermarkAvailable(studioId);
    if (!watermarkAvailable) {
      console.log(`Watermark not available for studio ${studioId}`);
      return;
    }

    // Apply watermark with studio settings
    const watermarkOptions = {
      position: studioSettings.watermarkPosition as any,
      opacity: studioSettings.watermarkOpacity,
      scale: studioSettings.watermarkScale,
      margin: studioSettings.watermarkMargin,
    };

    const watermarkedPath = await applyWatermark(file.path, studioId, watermarkOptions);

    // Update file path to watermarked version if different
    if (watermarkedPath !== file.path) {
      // Get stats of watermarked file
      const watermarkedStats = await fs.stat(watermarkedPath);

      // Update file object
      file.path = watermarkedPath;
      file.filename = path.basename(watermarkedPath);
      file.size = watermarkedStats.size;

      console.log(`✨ Watermark applied to: ${file.originalname}`);
    }

  } catch (error) {
    console.error('Error applying watermark:', error);
    // Don't throw error, just log it - watermark failure shouldn't stop upload
  }
};

/**
 * Middleware for optimizing uploaded images
 */
export const optimizeImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.files && !req.file) {
      return next();
    }
    
    const files = req.files as OptimizedFile[] || (req.file ? [req.file as OptimizedFile] : []);
    
    if (files.length === 0) {
      return next();
    }
    
    console.log(`🖼️  Optimizing ${files.length} image(s)...`);
    
    // Process images in parallel (but limit concurrency)
    const BATCH_SIZE = 3; // Process 3 images at a time
    const optimizedFiles: OptimizedFile[] = [];

    for (let i = 0; i < files.length; i += BATCH_SIZE) {
      const batch = files.slice(i, i + BATCH_SIZE);
      const batchResults = await Promise.all(
        batch.map(file => optimizeImage(file))
      );
      optimizedFiles.push(...batchResults);
    }

    // Apply watermark if user is a studio and has watermark enabled
    if (req.user && req.user.role === 'STUDIO') {
      console.log(`🎨 Applying watermarks for studio ${req.user.id}...`);

      for (const file of optimizedFiles) {
        await applyWatermarkToImage(file, req.user.id);
      }
    }
    
    // Update request with optimized files
    if (req.files) {
      req.files = optimizedFiles;
    } else if (req.file) {
      req.file = optimizedFiles[0];
    }
    
    // Log optimization results
    const totalOriginalSize = optimizedFiles.reduce((sum, file) => sum + (file.originalSize || file.size), 0);
    const totalCompressedSize = optimizedFiles.reduce((sum, file) => sum + file.size, 0);
    const totalSavings = Math.round(((totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100);
    
    console.log(`✅ Optimization complete: ${totalSavings}% size reduction`);
    console.log(`📊 ${(totalOriginalSize / 1024 / 1024).toFixed(2)}MB → ${(totalCompressedSize / 1024 / 1024).toFixed(2)}MB`);
    
    next();
  } catch (error) {
    console.error('Image optimization middleware error:', error);
    // Continue without optimization if there's an error
    next();
  }
};

/**
 * Middleware for profile picture optimization
 */
export const optimizeProfilePicture = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.file) {
      return next();
    }
    
    const file = req.file as OptimizedFile;
    
    // Specific settings for profile pictures
    const profileConfig = {
      maxWidth: 400,
      maxHeight: 400,
      quality: 90
    };
    
    const originalSize = file.size;
    const inputPath = file.path;
    const outputPath = inputPath; // Overwrite original
    
    await sharp(inputPath)
      .resize(profileConfig.maxWidth, profileConfig.maxHeight, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: profileConfig.quality, progressive: true })
      .toFile(outputPath + '.tmp');
    
    // Replace original with optimized
    await fs.rename(outputPath + '.tmp', outputPath);
    
    // Update file stats
    const stats = await fs.stat(outputPath);
    file.size = stats.size;
    file.originalSize = originalSize;
    file.compressedSize = stats.size;
    file.compressionRatio = Math.round(((originalSize - stats.size) / originalSize) * 100);
    
    console.log(`🖼️  Profile picture optimized: ${file.compressionRatio}% reduction`);
    
    next();
  } catch (error) {
    console.error('Profile picture optimization error:', error);
    next();
  }
};
