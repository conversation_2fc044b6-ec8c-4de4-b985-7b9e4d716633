import { Request, Response, NextFunction } from 'express';
import * as clientService from '../services/client.functional.service';
import { ApiError } from '../utils/apiError';

// Create new client
export const createClient = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { name, email, phone, uniqueId, password } = req.body;

    if (!name || !email || !uniqueId || !password) {
      throw new ApiError(400, 'Name, email, unique ID, and password are required');
    }

    const client = await clientService.createClient({
      name,
      email,
      phone,
      uniqueId,
      password,
      studioId: req.user.id,
    });

    res.status(201).json({
      success: true,
      message: 'Client created successfully',
      data: client,
    });
  } catch (error) {
    next(error);
  }
};

// Get all studio clients
export const getStudioClients = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const clients = await clientService.getStudioClients(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Clients retrieved successfully',
      data: clients,
    });
  } catch (error) {
    next(error);
  }
};

// Get client by ID
export const getClient = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const client = await clientService.getClientById(clientId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Client retrieved successfully',
      data: client,
    });
  } catch (error) {
    next(error);
  }
};

// Update client
export const updateClient = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const { name, email, phone } = req.body;

    const updatedClient = await clientService.updateClient(
      clientId,
      { name, email, phone },
      req.user.id
    );

    res.status(200).json({
      success: true,
      message: 'Client updated successfully',
      data: updatedClient,
    });
  } catch (error) {
    next(error);
  }
};

// Delete client
export const deleteClient = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const result = await clientService.deleteClient(clientId, req.user.id);

    res.status(200).json({
      success: true,
      message: result.message,
    });
  } catch (error) {
    next(error);
  }
};

// Client login
export const clientLogin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { uniqueId, password } = req.body;

    if (!uniqueId || !password) {
      throw new ApiError(400, 'Unique ID and password are required');
    }

    const result = await clientService.clientLogin({ uniqueId, password });

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

// Get client images (for client portal)
export const getMyImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const images = await clientService.getClientImages(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Images retrieved successfully',
      data: images,
    });
  } catch (error) {
    next(error);
  }
};

// Get client images by client ID (for studio)
export const getClientImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const images = await clientService.getClientImages(clientId);

    res.status(200).json({
      success: true,
      message: 'Client images retrieved successfully',
      data: images,
    });
  } catch (error) {
    next(error);
  }
};

// Search clients
export const searchClients = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { query } = req.query;

    if (!query || typeof query !== 'string') {
      throw new ApiError(400, 'Search query is required');
    }

    const clients = await clientService.searchClients(req.user.id, query);

    res.status(200).json({
      success: true,
      message: 'Client search completed successfully',
      data: clients,
    });
  } catch (error) {
    next(error);
  }
};

// Get client statistics
export const getClientStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const stats = await clientService.getClientStats(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Client statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    next(error);
  }
};
