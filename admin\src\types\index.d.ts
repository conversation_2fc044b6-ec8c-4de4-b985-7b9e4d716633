export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: 'ADMIN' | 'STUDIO' | 'USER';
  createdAt: string;
  studio?: {
    id: string;
    name: string;
  };
}

export interface Studio {
  id: string;
  email: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  updatedAt?: string;
  _count?: {
    users: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface CreateStudioRequest {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
}

export interface UpdateStudioRequest {
  name?: string;
  email?: string;
  description?: string;
  phone?: string;
  address?: string;
}
