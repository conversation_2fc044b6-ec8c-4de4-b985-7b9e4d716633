# Photo Cap Admin Panel

Next.js 14 admin panel for managing studios and users in the Photo Cap application.

## 🚀 Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.local.example .env.local
# Edit .env.local with your API URL
```

3. Start development server:
```bash
npm run dev
```

The admin panel will be available at `http://localhost:3000`

## 🔐 Default Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

## 📋 Features

- **Dashboard** - Overview of studios and users with statistics
- **Studio Management** - Approve/reject studio registrations
- **User Management** - View all registered users
- **Responsive Design** - Works on desktop and mobile devices
- **Authentication** - Secure JWT-based authentication

## 🎨 Tech Stack

- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **React Hook Form** for form handling
- **Axios** for API calls
- **React Hot Toast** for notifications
- **Lucide React** for icons

## 📁 Project Structure

```
src/
├── app/
│   ├── (auth)/          # Authentication pages
│   │   └── login/
│   └── (main)/          # Protected pages
│       ├── dashboard/
│       ├── studios/
│       └── users/
├── components/          # Reusable components
├── lib/                 # Utilities and services
└── types/              # TypeScript type definitions
```

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🌐 Environment Variables

```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```
