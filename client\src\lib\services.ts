import {
  User,
  Client,
  Image,
  DashboardStats,
  RecentActivity,
  StudioProfile,
  StudioSettings,
  CreateClientRequest,
  UpdateClientRequest,
  ApiResponse
} from '@/types';
import api from './api';

export const userService = {
  async getStudioUsers(): Promise<User[]> {
    const response = await api.get<ApiResponse<User[]>>('/users/studio/users');
    return response.data.data || [];
  },
};

export const clientService = {
  // Client management
  async createClient(data: CreateClientRequest): Promise<Client> {
    const response = await api.post<ApiResponse<Client>>('/clients', data);
    return response.data.data!;
  },

  async getStudioClients(): Promise<Client[]> {
    const response = await api.get<ApiResponse<Client[]>>('/clients');
    return response.data.data || [];
  },

  async getClientById(clientId: string): Promise<Client> {
    const response = await api.get<ApiResponse<Client>>(`/clients/${clientId}`);
    return response.data.data!;
  },

  async updateClient(clientId: string, data: UpdateClientRequest): Promise<Client> {
    const response = await api.put<ApiResponse<Client>>(`/clients/${clientId}`, data);
    return response.data.data!;
  },

  async deleteClient(clientId: string): Promise<void> {
    await api.delete(`/clients/${clientId}`);
  },

  async resetClientPassword(clientId: string): Promise<{ newPassword: string }> {
    const response = await api.post<ApiResponse<{ newPassword: string }>>(`/clients/${clientId}/reset-password`);
    return response.data.data!;
  },

  async regenerateQRCode(clientId: string): Promise<Client> {
    const response = await api.post<ApiResponse<Client>>(`/clients/${clientId}/regenerate-qr`);
    return response.data.data!;
  },

  // Image management
  async bulkUploadImages(clientId: string, files: File[], descriptions?: string[]): Promise<Image[]> {
    const formData = new FormData();
    formData.append('clientId', clientId);

    files.forEach((file) => {
      formData.append('images', file);
    });

    if (descriptions) {
      descriptions.forEach((desc, index) => {
        formData.append(`descriptions[${index}]`, desc);
      });
    }

    const response = await api.post<ApiResponse<Image[]>>('/clients/images/bulk-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data || [];
  },

  async getClientImages(clientId: string): Promise<Image[]> {
    const response = await api.get<ApiResponse<Image[]>>(`/clients/${clientId}/images`);
    return response.data.data || [];
  },

  async updateImage(imageId: string, data: { description?: string; tags?: string }): Promise<Image> {
    const response = await api.put<ApiResponse<Image>>(`/clients/images/${imageId}`, data);
    return response.data.data!;
  },

  async deleteImage(imageId: string): Promise<void> {
    await api.delete(`/clients/images/${imageId}`);
  },
};

export const settingsService = {
  // Dashboard
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await api.get<ApiResponse<DashboardStats>>('/settings/dashboard-stats');
    return response.data.data!;
  },

  async getRecentActivity(): Promise<RecentActivity> {
    const response = await api.get<ApiResponse<RecentActivity>>('/settings/recent-activity');
    return response.data.data!;
  },

  // Profile management
  async getStudioProfile(): Promise<StudioProfile> {
    const response = await api.get<ApiResponse<StudioProfile>>('/settings/profile');
    return response.data.data!;
  },

  async updateStudioProfile(data: Partial<StudioProfile>): Promise<StudioProfile> {
    const response = await api.put<ApiResponse<StudioProfile>>('/settings/profile', data);
    return response.data.data!;
  },

  // Settings management
  async getStudioSettings(): Promise<StudioSettings> {
    const response = await api.get<ApiResponse<StudioSettings>>('/settings/studio-settings');
    return response.data.data!;
  },

  async updateProfileSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/profile-settings', data);
    return response.data.data!;
  },

  async updateSecuritySettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/security-settings', data);
    return response.data.data!;
  },

  async updateClientSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/client-settings', data);
    return response.data.data!;
  },

  async updateUploadSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/upload-settings', data);
    return response.data.data!;
  },

  async updateWatermarkSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/watermark-settings', data);
    return response.data.data!;
  },

  // Password management
  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<void> {
    await api.post('/settings/change-password', {
      currentPassword,
      newPassword,
      confirmPassword,
    });
  },

  // File uploads
  async uploadProfilePicture(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('profilePicture', file);

    const response = await api.post<ApiResponse<{ url: string }>>('/settings/upload-profile-picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data!;
  },

  async uploadLogo(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('logo', file);

    const response = await api.post<ApiResponse<{ url: string }>>('/settings/upload-logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data!;
  },
};
