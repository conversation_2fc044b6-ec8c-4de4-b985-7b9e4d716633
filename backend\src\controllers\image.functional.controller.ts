import { Request, Response, NextFunction } from 'express';
import * as imageService from '../services/image.functional.service';
import { ApiError } from '../utils/apiError';

// Upload images
export const uploadImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId, description } = req.body;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      throw new ApiError(400, 'No files uploaded');
    }

    if (!clientId) {
      throw new ApiError(400, 'Client ID is required');
    }

    const uploadedImages = await imageService.uploadImages(
      files,
      clientId,
      req.user.id,
      description
    );

    console.log(`📸 Uploaded ${uploadedImages.length} images for client ${clientId}`);

    res.status(201).json({
      success: true,
      message: `${uploadedImages.length} images uploaded successfully`,
      data: uploadedImages,
    });
  } catch (error) {
    next(error);
  }
};

// Get images by client ID
export const getClientImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { clientId } = req.params;
    const images = await imageService.getImagesByClientId(clientId);

    res.status(200).json({
      success: true,
      message: 'Images retrieved successfully',
      data: images,
    });
  } catch (error) {
    next(error);
  }
};

// Get all studio images
export const getStudioImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const images = await imageService.getImagesByStudioId(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Studio images retrieved successfully',
      data: images,
    });
  } catch (error) {
    next(error);
  }
};

// Get single image
export const getImage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { imageId } = req.params;
    const image = await imageService.getImageById(imageId);

    res.status(200).json({
      success: true,
      message: 'Image retrieved successfully',
      data: image,
    });
  } catch (error) {
    next(error);
  }
};

// Delete image
export const deleteImage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { imageId } = req.params;
    const result = await imageService.deleteImage(imageId, req.user.id);

    res.status(200).json({
      success: true,
      message: result.message,
    });
  } catch (error) {
    next(error);
  }
};

// Delete multiple images
export const deleteMultipleImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { imageIds } = req.body;

    if (!Array.isArray(imageIds) || imageIds.length === 0) {
      throw new ApiError(400, 'Image IDs array is required');
    }

    const result = await imageService.deleteMultipleImages(imageIds, req.user.id);

    res.status(200).json({
      success: true,
      message: result.message,
    });
  } catch (error) {
    next(error);
  }
};

// Update image description
export const updateImageDescription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { imageId } = req.params;
    const { description } = req.body;

    const updatedImage = await imageService.updateImageDescription(
      imageId,
      description,
      req.user.id
    );

    res.status(200).json({
      success: true,
      message: 'Image description updated successfully',
      data: updatedImage,
    });
  } catch (error) {
    next(error);
  }
};

// Get image statistics
export const getImageStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const stats = await imageService.getImageStats(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Image statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    next(error);
  }
};

// Search images
export const searchImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { query, clientId, dateFrom, dateTo, mimeType } = req.query;

    if (!query || typeof query !== 'string') {
      throw new ApiError(400, 'Search query is required');
    }

    const filters: any = {};
    if (clientId) filters.clientId = clientId as string;
    if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
    if (dateTo) filters.dateTo = new Date(dateTo as string);
    if (mimeType) filters.mimeType = mimeType as string;

    const images = await imageService.searchImages(req.user.id, query, filters);

    res.status(200).json({
      success: true,
      message: 'Image search completed successfully',
      data: images,
    });
  } catch (error) {
    next(error);
  }
};

// Get recent images
export const getRecentImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const limit = parseInt(req.query.limit as string) || 10;
    const images = await imageService.getRecentImages(req.user.id, limit);

    res.status(200).json({
      success: true,
      message: 'Recent images retrieved successfully',
      data: images,
    });
  } catch (error) {
    next(error);
  }
};
