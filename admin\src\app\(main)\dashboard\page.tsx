'use client';

import { useEffect, useState } from 'react';
import { Building2, Users, Clock, CheckCircle, TrendingUp, UserCheck, Crown, XCircle } from 'lucide-react';
import { studioService, userService } from '@/lib/services';
import { Studio, User } from '@/types';
import toast from 'react-hot-toast';

interface DashboardStats {
  totalStudios: number;
  pendingStudios: number;
  approvedStudios: number;
  rejectedStudios: number;
  totalUsers: number;
  adminUsers: number;
  studioUsers: number;
  regularUsers: number;
  usersWithStudio: number;
  recentRegistrations: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalStudios: 0,
    pendingStudios: 0,
    approvedStudios: 0,
    rejectedStudios: 0,
    totalUsers: 0,
    adminUsers: 0,
    studioUsers: 0,
    regularUsers: 0,
    usersWithStudio: 0,
    recentRegistrations: 0,
  });
  const [recentStudios, setRecentStudios] = useState<Studio[]>([]);
  const [recentUsers, setRecentUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [studios, users] = await Promise.all([
        studioService.getAllStudios(),
        userService.getAllUsers(),
      ]);

      // Calculate recent registrations (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentRegistrations = users.filter(user =>
        new Date(user.createdAt) > sevenDaysAgo
      ).length;

      setStats({
        totalStudios: studios.length,
        pendingStudios: studios.filter(s => s.status === 'PENDING').length,
        approvedStudios: studios.filter(s => s.status === 'APPROVED').length,
        rejectedStudios: studios.filter(s => s.status === 'REJECTED').length,
        totalUsers: users.length,
        adminUsers: users.filter(u => u.role === 'ADMIN').length,
        studioUsers: users.filter(u => u.role === 'STUDIO').length,
        regularUsers: users.filter(u => u.role === 'USER').length,
        usersWithStudio: users.filter(u => (u as any).studio).length,
        recentRegistrations,
      });

      // Sort by creation date and get recent items
      const sortedStudios = [...studios].sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      const sortedUsers = [...users].sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setRecentStudios(sortedStudios.slice(0, 5));
      setRecentUsers(sortedUsers.slice(0, 5));
    } catch (error) {
      toast.error('Failed to fetch dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStudioAction = async (studioId: string, status: 'APPROVED' | 'REJECTED') => {
    try {
      await studioService.updateStudioStatus(studioId, status);
      toast.success(`Studio ${status.toLowerCase()} successfully`);
      fetchDashboardData();
    } catch (error) {
      toast.error(`Failed to ${status.toLowerCase()} studio`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to Photo Cap Admin Panel</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Studios</p>
              <p className="text-xl font-bold text-gray-900">{stats.totalStudios}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Clock className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Pending</p>
              <p className="text-xl font-bold text-gray-900">{stats.pendingStudios}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Approved</p>
              <p className="text-xl font-bold text-gray-900">{stats.approvedStudios}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-red-100 p-3 rounded-full">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Rejected</p>
              <p className="text-xl font-bold text-gray-900">{stats.rejectedStudios}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Users className="h-5 w-5 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Users</p>
              <p className="text-xl font-bold text-gray-900">{stats.totalUsers}</p>
            </div>
          </div>
        </div>
      </div>

      {/* User Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-indigo-100 p-3 rounded-full">
              <Crown className="h-5 w-5 text-indigo-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Admins</p>
              <p className="text-xl font-bold text-gray-900">{stats.adminUsers}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-emerald-100 p-3 rounded-full">
              <Building2 className="h-5 w-5 text-emerald-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Studio Owners</p>
              <p className="text-xl font-bold text-gray-900">{stats.studioUsers}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-cyan-100 p-3 rounded-full">
              <UserCheck className="h-5 w-5 text-cyan-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Regular Users</p>
              <p className="text-xl font-bold text-gray-900">{stats.regularUsers}</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-full">
              <TrendingUp className="h-5 w-5 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Recent (7 days)</p>
              <p className="text-xl font-bold text-gray-900">{stats.recentRegistrations}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Studios */}
        <div className="card">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Studio Registrations</h2>
          </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Studio
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentStudios.map((studio) => (
                <tr key={studio.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{studio.name}</div>
                      <div className="text-sm text-gray-500">{studio.description}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {studio.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      studio.status === 'APPROVED' 
                        ? 'bg-green-100 text-green-800'
                        : studio.status === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {studio.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {studio.status === 'PENDING' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleStudioAction(studio.id, 'APPROVED')}
                          className="text-green-600 hover:text-green-900"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleStudioAction(studio.id, 'REJECTED')}
                          className="text-red-600 hover:text-red-900"
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {recentStudios.length === 0 && (
          <div className="text-center py-8">
            <Building2 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No studios registered yet</p>
          </div>
        )}
      </div>

      {/* Recent Users */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Recent User Registrations</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Studio
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Joined
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentUsers.map((user) => (
                <tr key={user.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="bg-primary-100 p-2 rounded-full">
                        <Users className="h-4 w-4 text-primary-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'ADMIN'
                        ? 'bg-purple-100 text-purple-800'
                        : user.role === 'STUDIO'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(user as any).studio?.name || 'No studio'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {recentUsers.length === 0 && (
          <div className="text-center py-8">
            <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No users registered yet</p>
          </div>
        )}
      </div>
    </div>
    </div>
  );
}
