// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  STUDIO
  USER
}

enum StudioStatus {
  PENDING
  APPROVED
  REJECTED
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

model Studio {
  id          String        @id @default(cuid())
  email       String        @unique
  password    String
  name        String
  description String?
  phone       String?
  address     String?
  status      StudioStatus  @default(PENDING)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  users       User[]
  clients     Client[]
  images      Image[]
  settings    StudioSettings?

  @@map("studios")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  phone     String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studioId  String?
  studio    Studio?  @relation(fields: [studioId], references: [id], onDelete: SetNull)

  @@map("users")
}

model Client {
  id          String    @id @default(cuid())
  uniqueId    String    @unique
  name        String
  email       String?
  phone       String?
  password    String
  qrCode      String?
  accessLink  String
  isActive    Boolean   @default(true)
  lastLogin   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  studioId    String
  studio      Studio    @relation(fields: [studioId], references: [id], onDelete: Cascade)
  images      Image[]

  @@map("clients")
}

model Image {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  path         String
  size         Int
  mimeType     String
  description  String?
  tags         String?
  uploadedAt   DateTime @default(now())

  // Relations
  clientId     String
  client       Client   @relation(fields: [clientId], references: [id], onDelete: Cascade)
  studioId     String
  studio       Studio   @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("images")
}

model StudioSettings {
  id        String   @id @default(cuid())
  studioId  String   @unique

  // Profile Settings
  profilePicture    String?
  businessAddress   String?
  operatingHours    String?
  socialLinks       Json?
  logoUrl          String?

  // Security Settings
  twoFactorEnabled Boolean @default(false)
  sessionTimeout   Int     @default(30) // minutes

  // Client Settings
  defaultAccessDuration Int     @default(30) // days
  autoGeneratePassword  Boolean @default(true)
  emailNotifications    Boolean @default(true)
  smsNotifications      Boolean @default(false)

  // Upload Settings
  maxFileSize          Int     @default(10) // MB
  allowedFormats       String  @default("jpg,jpeg,png,gif")
  autoResize           Boolean @default(true)
  storageQuota         Int     @default(1000) // MB

  // Watermark Settings
  enableWatermark      Boolean @default(true)
  watermarkPosition    String  @default("bottom-right") // top-left, top-right, bottom-left, bottom-right, center
  watermarkOpacity     Int     @default(70) // 0-100
  watermarkScale       Float   @default(0.15) // 0-1, relative to image size
  watermarkMargin      Int     @default(20) // pixels from edge

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studio    Studio   @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("studio_settings")
}
