'use client';

import { useEffect, useState } from 'react';
import {
  Users,
  Mail,
  Phone,
  Calendar,
  Plus,
  Edit,
  Trash2,
  Eye,
  QrCode,
  Key,
  Upload,
  Download
} from 'lucide-react';
import { clientService } from '@/lib/services';
import { Client } from '@/types';
import toast from 'react-hot-toast';
import CreateClientModal from '@/components/CreateClientModal';
import EditClientModal from '@/components/EditClientModal';
import ViewClientModal from '@/components/ViewClientModal';
import DeleteConfirmModal from '@/components/DeleteConfirmModal';
import BulkUploadModal from '@/components/BulkUploadModal';
import QRCodeModal from '@/components/QRCodeModal';

export default function UsersPage() {
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [showQRCodeModal, setShowQRCodeModal] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      const data = await clientService.getStudioClients();
      setClients(data);
    } catch (error) {
      toast.error('Failed to fetch clients');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateClient = () => {
    setShowCreateModal(true);
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setShowEditModal(true);
  };

  const handleViewClient = (client: Client) => {
    setSelectedClient(client);
    setShowViewModal(true);
  };

  const handleDeleteClient = (client: Client) => {
    setSelectedClient(client);
    setShowDeleteModal(true);
  };

  const handleBulkUpload = (client: Client) => {
    setSelectedClient(client);
    setShowBulkUploadModal(true);
  };

  const handleShowQRCode = (client: Client) => {
    setSelectedClient(client);
    setShowQRCodeModal(true);
  };

  const handleResetPassword = async (client: Client) => {
    try {
      const result = await clientService.resetClientPassword(client.id);
      toast.success(`New password: ${result.newPassword}`);
      // You might want to show this in a modal instead
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to reset password');
    }
  };

  const handleClientCreated = () => {
    setShowCreateModal(false);
    fetchClients();
    toast.success('Client created successfully');
  };

  const handleClientUpdated = () => {
    setShowEditModal(false);
    setSelectedClient(null);
    fetchClients();
    toast.success('Client updated successfully');
  };

  const handleClientDeleted = async () => {
    if (!selectedClient) return;

    try {
      await clientService.deleteClient(selectedClient.id);
      setShowDeleteModal(false);
      setSelectedClient(null);
      fetchClients();
      toast.success('Client deleted successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to delete client');
    }
  };

  const handleImagesUploaded = () => {
    setShowBulkUploadModal(false);
    setSelectedClient(null);
    fetchClients();
    toast.success('Images uploaded successfully');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Clients</h1>
          <p className="text-gray-600">Manage clients and their photo galleries</p>
        </div>
        <button
          onClick={handleCreateClient}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Client</span>
        </button>
      </div>

      {/* Clients Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {clients.map((client) => (
          <div key={client.id} className="card p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="bg-primary-100 p-2 rounded-full">
                  <Users className="h-5 w-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">{client.name}</h3>
                  <p className="text-sm text-gray-500">ID: {client.uniqueId}</p>
                </div>
              </div>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                client.isActive
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {client.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="space-y-2 mb-4">
              {client.email && (
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  {client.email}
                </div>
              )}

              {client.phone && (
                <div className="flex items-center text-sm text-gray-600">
                  <Phone className="h-4 w-4 mr-2" />
                  {client.phone}
                </div>
              )}

              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                {new Date(client.createdAt).toLocaleDateString()}
              </div>

              {client.lastLogin && (
                <div className="flex items-center text-sm text-green-600">
                  <Users className="h-4 w-4 mr-2" />
                  Last login: {new Date(client.lastLogin).toLocaleDateString()}
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-gray-500">
                {client._count?.images || 0} images
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleShowQRCode(client)}
                  className="p-1 text-blue-600 hover:text-blue-800"
                  title="Show QR Code"
                >
                  <QrCode className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleBulkUpload(client)}
                  className="p-1 text-green-600 hover:text-green-800"
                  title="Upload Images"
                >
                  <Upload className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleViewClient(client)}
                  className="p-1 text-purple-600 hover:text-purple-800"
                  title="View Details"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEditClient(client)}
                  className="p-1 text-yellow-600 hover:text-yellow-800"
                  title="Edit Client"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleResetPassword(client)}
                  className="p-1 text-orange-600 hover:text-orange-800"
                  title="Reset Password"
                >
                  <Key className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteClient(client)}
                  className="p-1 text-red-600 hover:text-red-800"
                  title="Delete Client"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <a
                href={client.accessLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-primary-600 hover:text-primary-800 break-all"
              >
                {client.accessLink}
              </a>
            </div>
          </div>
        ))}
      </div>

      {clients.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
          <p className="text-gray-600">Create your first client to get started.</p>
          <button
            onClick={handleCreateClient}
            className="mt-4 btn-primary"
          >
            Add First Client
          </button>
        </div>
      )}

      {/* Modals */}
      {showCreateModal && (
        <CreateClientModal
          onClose={() => setShowCreateModal(false)}
          onClientCreated={handleClientCreated}
        />
      )}

      {showEditModal && selectedClient && (
        <EditClientModal
          client={selectedClient}
          onClose={() => {
            setShowEditModal(false);
            setSelectedClient(null);
          }}
          onClientUpdated={handleClientUpdated}
        />
      )}

      {showViewModal && selectedClient && (
        <ViewClientModal
          client={selectedClient}
          onClose={() => {
            setShowViewModal(false);
            setSelectedClient(null);
          }}
        />
      )}

      {showDeleteModal && selectedClient && (
        <DeleteConfirmModal
          title="Delete Client"
          message={`Are you sure you want to delete "${selectedClient.name}"? This will also delete all their images. This action cannot be undone.`}
          onConfirm={handleClientDeleted}
          onCancel={() => {
            setShowDeleteModal(false);
            setSelectedClient(null);
          }}
        />
      )}

      {showBulkUploadModal && selectedClient && (
        <BulkUploadModal
          client={selectedClient}
          onClose={() => {
            setShowBulkUploadModal(false);
            setSelectedClient(null);
          }}
          onImagesUploaded={handleImagesUploaded}
        />
      )}

      {showQRCodeModal && selectedClient && (
        <QRCodeModal
          client={selectedClient}
          onClose={() => {
            setShowQRCodeModal(false);
            setSelectedClient(null);
          }}
        />
      )}
    </div>
  );
}
