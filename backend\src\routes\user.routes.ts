import { Router } from 'express';
import * as userController from '../controllers/user.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';

const router = Router();

// Public routes (no authentication required)
router.post('/admin/login', userController.loginAdmin);
router.post('/studio/login', userController.loginStudio);
router.post('/studio/register', userController.registerStudio);

// Admin routes (working with functional controllers)
router.get('/admin/studios', authenticate, authorize('ADMIN'), userController.getAllStudios);
router.get('/admin/studios/:studioId', authenticate, authorize('ADMIN'), userController.getStudioById);
router.post('/admin/studios', authenticate, authorize('ADMIN'), userController.createStudio);
router.put('/admin/studios/:studioId', authenticate, authorize('ADMIN'), userController.updateStudio);
router.delete('/admin/studios/:studioId', authenticate, authorize('ADMIN'), userController.deleteStudio);
router.patch('/admin/studios/:studioId/status', authenticate, authorize('ADMIN'), userController.updateStudioStatus);
router.get('/admin/users', authenticate, authorize('ADMIN'), userController.getAllUsers);

// Studio routes (working)
router.get('/studio/users', authenticate, authorize('STUDIO'), userController.getStudioUsers);

export default router;
