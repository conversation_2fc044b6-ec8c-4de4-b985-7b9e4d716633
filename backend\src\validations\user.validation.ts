import Joi from 'joi';

export const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required',
  }),
});

export const registerStudioSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required',
  }),
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Studio name must be at least 2 characters long',
    'string.max': 'Studio name cannot exceed 100 characters',
    'any.required': 'Studio name is required',
  }),
  description: Joi.string().max(500).optional().messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
  address: Joi.string().max(200).optional().messages({
    'string.max': 'Address cannot exceed 200 characters',
  }),
});

export const registerUserSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required',
  }),
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name cannot exceed 100 characters',
    'any.required': 'Name is required',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
  studioId: Joi.string().optional(),
});

export const updateStudioStatusSchema = Joi.object({
  status: Joi.string().valid('APPROVED', 'REJECTED').required().messages({
    'any.only': 'Status must be either APPROVED or REJECTED',
    'any.required': 'Status is required',
  }),
});

export const updateStudioSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional().messages({
    'string.min': 'Studio name must be at least 2 characters long',
    'string.max': 'Studio name cannot exceed 100 characters',
  }),
  email: Joi.string().email().optional().messages({
    'string.email': 'Please provide a valid email address',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().allow('').messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
  address: Joi.string().max(200).optional().allow('').messages({
    'string.max': 'Address cannot exceed 200 characters',
  }),
});

export const createStudioSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required',
  }),
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Studio name must be at least 2 characters long',
    'string.max': 'Studio name cannot exceed 100 characters',
    'any.required': 'Studio name is required',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().allow('').messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
  address: Joi.string().max(200).optional().allow('').messages({
    'string.max': 'Address cannot exceed 200 characters',
  }),
  status: Joi.string().valid('PENDING', 'APPROVED', 'REJECTED').optional().default('PENDING'),
});
