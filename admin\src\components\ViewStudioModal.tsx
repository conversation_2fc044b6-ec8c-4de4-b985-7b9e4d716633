'use client';

import { X, Mail, Phone, MapPin, Calendar, Building2, Users } from 'lucide-react';
import { Studio } from '@/types';

interface ViewStudioModalProps {
  studio: Studio;
  onClose: () => void;
}

export default function ViewStudioModal({ studio, onClose }: ViewStudioModalProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Studio Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center">
              <div className="bg-primary-100 p-3 rounded-full">
                <Building2 className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{studio.name}</h3>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(studio.status)}`}>
                  {studio.status}
                </span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 uppercase tracking-wide">Contact Information</h4>
            
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-sm text-gray-900">{studio.email}</span>
              </div>
              
              {studio.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-sm text-gray-900">{studio.phone}</span>
                </div>
              )}
              
              {studio.address && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-sm text-gray-900">{studio.address}</span>
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          {studio.description && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900 uppercase tracking-wide">Description</h4>
              <p className="text-sm text-gray-600">{studio.description}</p>
            </div>
          )}

          {/* Statistics */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-900 uppercase tracking-wide">Statistics</h4>
            <div className="flex items-center">
              <Users className="h-4 w-4 text-gray-400 mr-3" />
              <span className="text-sm text-gray-900">
                {studio._count?.users || 0} associated users
              </span>
            </div>
          </div>

          {/* Timestamps */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 uppercase tracking-wide">Timeline</h4>
            
            <div className="space-y-3">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                <div>
                  <span className="text-sm text-gray-500">Created: </span>
                  <span className="text-sm text-gray-900">
                    {new Date(studio.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </span>
                </div>
              </div>
              
              {studio.updatedAt && studio.updatedAt !== studio.createdAt && (
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                  <div>
                    <span className="text-sm text-gray-500">Last Updated: </span>
                    <span className="text-sm text-gray-900">
                      {new Date(studio.updatedAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
