import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import QRCode from 'qrcode';
import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import { generateToken } from '../middleware/auth.middleware';

// Generate unique client ID and password
export const generateClientCredentials = () => {
  const uniqueId = uuidv4().substring(0, 8).toUpperCase();
  const password = Math.random().toString(36).slice(-8);
  return { uniqueId, password };
};

// Generate QR code
export const generateQRCode = async (accessLink: string): Promise<string> => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(accessLink, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    return qrCodeDataURL;
  } catch (error) {
    throw new ApiError(500, 'Failed to generate QR code');
  }
};

// Create new client
export const createClient = async (studioId: string, data: {
  name: string;
  email?: string;
  phone?: string;
}) => {
  const { uniqueId, password } = generateClientCredentials();
  const hashedPassword = await bcrypt.hash(password, 12);
  const accessLink = `${process.env.CLIENT_PORTAL_URL || 'http://localhost:3002'}/login?id=${uniqueId}`;
    const qrCode = await generateQRCode(accessLink);

    const client = await prisma.client.create({
      data: {
        uniqueId,
        name: data.name,
        email: data.email,
        phone: data.phone,
        password: hashedPassword,
        qrCode,
        accessLink,
        studioId,
      },
      include: {
        _count: {
          select: {
            images: true,
          },
        },
      },
    });

    return {
      ...client,
      plainPassword: password, // Return plain password for initial sharing
    };
  }

// Duplicate function removed - using export version below

// Get studio clients
export const getStudioClients = async (studioId: string) => {
  return await prisma.client.findMany({
    where: { studioId },
    include: {
      _count: {
        select: {
          images: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};

// Get client by ID
export const getClientById = async (clientId: string, studioId: string) => {
    const client = await prisma.client.findFirst({
      where: { 
        id: clientId,
        studioId,
      },
      include: {
        _count: {
          select: {
            images: true,
          },
        },
      },
    });

    if (!client) {
      throw new ApiError(404, 'Client not found');
    }

    return client;
};

// Update client
export const updateClient = async (clientId: string, studioId: string, data: {
    name?: string;
    email?: string;
    phone?: string;
    isActive?: boolean;
}) => {
    const client = await prisma.client.findFirst({
      where: { id: clientId, studioId },
    });

    if (!client) {
      throw new ApiError(404, 'Client not found');
    }

    return await prisma.client.update({
      where: { id: clientId },
      data,
      include: {
        _count: {
          select: {
            images: true,
          },
        },
      },
    });
};

// Reset client password
export const resetClientPassword = async (clientId: string, studioId: string) => {
    const client = await prisma.client.findFirst({
      where: { id: clientId, studioId },
    });

    if (!client) {
      throw new ApiError(404, 'Client not found');
    }

    const newPassword = Math.random().toString(36).slice(-8);
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    await prisma.client.update({
      where: { id: clientId },
      data: { password: hashedPassword },
    });

    return { newPassword };
};

// Regenerate QR code
export const regenerateQRCode = async (clientId: string, studioId: string) => {
    const client = await prisma.client.findFirst({
      where: { id: clientId, studioId },
    });

    if (!client) {
      throw new ApiError(404, 'Client not found');
    }

    const qrCode = await generateQRCode(client.accessLink);

    return await prisma.client.update({
      where: { id: clientId },
      data: { qrCode },
    });
};

// Duplicate function removed - using export version below

// Delete client
export const deleteClient = async (clientId: string, studioId: string) => {
  const client = await prisma.client.findFirst({
    where: { id: clientId, studioId },
    include: {
      _count: {
        select: {
          images: true,
        },
      },
    },
  });

  if (!client) {
    throw new ApiError(404, 'Client not found');
  }

  // Delete all associated images first
  await prisma.image.deleteMany({
    where: { clientId: clientId },
  });

  // Delete client
  await prisma.client.delete({
    where: { id: clientId },
  });

  return { message: 'Client deleted successfully' };
};

// Client login
export const clientLogin = async (uniqueId: string, password: string) => {
    const client = await prisma.client.findUnique({
      where: { uniqueId },
      include: {
        studio: {
          select: {
            name: true,
            status: true,
          },
        },
      },
    });

    if (!client || !client.isActive) {
      throw new ApiError(401, 'Invalid credentials or account inactive');
    }

    if (client.studio.status !== 'APPROVED') {
      throw new ApiError(403, 'Studio is not approved');
    }

    const isPasswordValid = await bcrypt.compare(password, client.password);
    if (!isPasswordValid) {
      throw new ApiError(401, 'Invalid credentials');
    }

    // Update last login
    await prisma.client.update({
      where: { id: client.id },
      data: { lastLogin: new Date() },
    });

    const token = generateToken({
      id: client.id,
      role: 'CLIENT' as any,
      email: client.email || client.uniqueId,
    });

    return {
      token,
      client: {
        id: client.id,
        uniqueId: client.uniqueId,
        name: client.name,
        email: client.email,
        studioName: client.studio.name,
      },
    };
};

// Get client statistics for studio dashboard
export const getClientStats = async (studioId: string) => {
    const totalClients = await prisma.client.count({
      where: { studioId },
    });

    const activeClients = await prisma.client.count({
      where: { studioId, isActive: true },
    });

    const recentClients = await prisma.client.count({
      where: {
        studioId,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
    });

    const totalImages = await prisma.image.count({
      where: { studioId },
    });

    return {
      totalClients,
      activeClients,
      recentClients,
      totalImages,
    };
};
