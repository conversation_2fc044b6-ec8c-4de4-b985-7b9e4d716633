# Photo Cap Backend

Node.js + Express + TypeScript + Prisma + PostgreSQL backend for the Photo Cap application.

## 🚀 Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

3. Set up database:
```bash
npm run db:generate
npm run db:push
npm run db:seed
```

4. Start development server:
```bash
npm run dev
```

## 📋 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Seed database with initial data
- `npm run db:studio` - Open Prisma Studio

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## 📡 API Endpoints

### Public Endpoints
- `GET /health` - Health check
- `POST /api/users/admin/login` - Admin login
- `POST /api/users/studio/register` - Studio registration
- `POST /api/users/studio/login` - Studio login

### Protected Endpoints (Admin)
- `GET /api/users/admin/studios` - Get all studios
- `PATCH /api/users/admin/studios/:id/status` - Update studio status
- `GET /api/users/admin/users` - Get all users

### Protected Endpoints (Studio)
- `GET /api/users/studio/users` - Get studio users

## 🗄️ Database Schema

See `prisma/schema.prisma` for the complete database schema.

## 🛠️ Environment Variables

```env
DATABASE_URL="postgresql://username:password@localhost:5432/photo_cap_db"
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="7d"
PORT=5000
NODE_ENV="development"
CORS_ORIGIN="http://localhost:3000,http://localhost:3001"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
```
