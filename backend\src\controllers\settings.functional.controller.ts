import { Request, Response, NextFunction } from 'express';
import * as settingsService from '../services/settings.functional.service';
import { ApiError } from '../utils/apiError';
import {
  updateProfileSchema,
  updateProfileSettingsSchema,
  updateSecuritySettingsSchema,
  updateClientSettingsSchema,
  updateUploadSettingsSchema,
  changePasswordSchema,
} from '../validations/settings.validation';

// Get studio profile
export const getStudioProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const settings = await settingsService.getStudioSettings(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Studio profile retrieved successfully',
      data: settings,
    });
  } catch (error) {
    next(error);
  }
};

// Update profile settings
export const updateProfileSettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { error, value } = updateProfileSettingsSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const updatedSettings = await settingsService.updateProfile(req.user.id, value);

    res.status(200).json({
      success: true,
      message: 'Profile settings updated successfully',
      data: updatedSettings,
    });
  } catch (error) {
    next(error);
  }
};

// Update security settings
export const updateSecuritySettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { error, value } = updateSecuritySettingsSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const updatedSettings = await settingsService.updateSecurity(req.user.id, value);

    res.status(200).json({
      success: true,
      message: 'Security settings updated successfully',
      data: updatedSettings,
    });
  } catch (error) {
    next(error);
  }
};

// Update client settings
export const updateClientSettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { error, value } = updateClientSettingsSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const updatedSettings = await settingsService.updateClientSettings(req.user.id, value);

    res.status(200).json({
      success: true,
      message: 'Client settings updated successfully',
      data: updatedSettings,
    });
  } catch (error) {
    next(error);
  }
};

// Update upload settings
export const updateUploadSettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { error, value } = updateUploadSettingsSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    const updatedSettings = await settingsService.updateUploadSettings(req.user.id, value);

    res.status(200).json({
      success: true,
      message: 'Upload settings updated successfully',
      data: updatedSettings,
    });
  } catch (error) {
    next(error);
  }
};

// Change password
export const changePassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { error, value } = changePasswordSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    await settingsService.changePassword(req.user.id, value);

    res.status(200).json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    next(error);
  }
};

// Get dashboard stats
export const getDashboardStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const stats = await settingsService.getDashboardStats(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Dashboard stats retrieved successfully',
      data: stats,
    });
  } catch (error) {
    next(error);
  }
};

// Get recent activity
export const getRecentActivity = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const activity = await settingsService.getRecentActivity(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Recent activity retrieved successfully',
      data: activity,
    });
  } catch (error) {
    next(error);
  }
};
