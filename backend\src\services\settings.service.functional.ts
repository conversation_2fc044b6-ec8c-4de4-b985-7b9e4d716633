import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import bcrypt from 'bcryptjs';

// Get or create studio settings
export const getStudioSettings = async (studioId: string) => {
  let settings = await prisma.studioSettings.findUnique({
    where: { studioId },
  });

  // Create default settings if not exists
  if (!settings) {
    settings = await prisma.studioSettings.create({
      data: { studioId },
    });
  }

  return settings;
};

// Get studio profile
export const getStudioProfile = async (studioId: string) => {
  const studio = await prisma.studio.findUnique({
    where: { id: studioId },
    select: {
      id: true,
      email: true,
      name: true,
      description: true,
      phone: true,
      address: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!studio) {
    throw new ApiError(404, 'Studio not found');
  }

  const settings = await getStudioSettings(studioId);

  return {
    ...studio,
    settings,
  };
};

// Update studio profile
export const updateStudioProfile = async (studioId: string, data: {
  name?: string;
  description?: string;
  phone?: string;
  address?: string;
}) => {
  const studio = await prisma.studio.findUnique({
    where: { id: studioId },
  });

  if (!studio) {
    throw new ApiError(404, 'Studio not found');
  }

  return await prisma.studio.update({
    where: { id: studioId },
    data,
  });
};

// Update profile settings
export const updateProfile = async (studioId: string, data: {
  profilePicture?: string;
  businessAddress?: string;
  operatingHours?: string;
  socialLinks?: any;
  logoUrl?: string;
}) => {
  const settings = await getStudioSettings(studioId);
  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data,
  });
};

// Update security settings
export const updateSecurity = async (studioId: string, data: {
  twoFactorEnabled?: boolean;
  sessionTimeout?: number;
}) => {
  const settings = await getStudioSettings(studioId);
  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data,
  });
};

// Update client settings
export const updateClientSettings = async (studioId: string, data: {
  defaultAccessDuration?: number;
  autoGeneratePassword?: boolean;
  emailNotifications?: boolean;
  smsNotifications?: boolean;
}) => {
  const settings = await getStudioSettings(studioId);
  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data,
  });
};

// Update upload settings
export const updateUploadSettings = async (studioId: string, data: {
  maxFileSize?: number;
  allowedFormats?: string;
  autoResize?: boolean;
  storageQuota?: number;
}) => {
  const settings = await getStudioSettings(studioId);
  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data,
  });
};

// Update watermark settings
export const updateWatermarkSettings = async (studioId: string, data: {
  enableWatermark?: boolean;
  watermarkPosition?: string;
  watermarkOpacity?: number;
  watermarkScale?: number;
  watermarkMargin?: number;
}) => {
  const settings = await getStudioSettings(studioId);

  // Ensure numeric fields are properly converted and validated
  const updateData = {
    ...data,
    ...(data.watermarkOpacity !== undefined && {
      watermarkOpacity: Math.max(0, Math.min(100, Number(data.watermarkOpacity)))
    }),
    ...(data.watermarkScale !== undefined && {
      watermarkScale: Math.max(0.05, Math.min(1, Number(data.watermarkScale)))
    }),
    ...(data.watermarkMargin !== undefined && {
      watermarkMargin: Math.max(0, Number(data.watermarkMargin))
    }),
  };

  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data: updateData,
  });
};

// Change password
export const changePassword = async (studioId: string, currentPassword: string, newPassword: string) => {
  const studio = await prisma.studio.findUnique({
    where: { id: studioId },
  });

  if (!studio) {
    throw new ApiError(404, 'Studio not found');
  }

  const isValidPassword = await bcrypt.compare(currentPassword, studio.password);
  if (!isValidPassword) {
    throw new ApiError(400, 'Current password is incorrect');
  }

  const hashedNewPassword = await bcrypt.hash(newPassword, 12);
  await prisma.studio.update({
    where: { id: studioId },
    data: { password: hashedNewPassword },
  });

  return { message: 'Password changed successfully' };
};

// Get dashboard statistics
export const getDashboardStats = async (studioId: string) => {
  const [
    totalClients,
    activeClients,
    totalImages,
    recentClients,
    recentImages,
    storageUsed,
  ] = await Promise.all([
    prisma.client.count({ where: { studioId } }),
    prisma.client.count({ where: { studioId, isActive: true } }),
    prisma.image.count({ where: { studioId } }),
    prisma.client.count({
      where: {
        studioId,
        createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
      },
    }),
    prisma.image.count({
      where: {
        studioId,
        uploadedAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
      },
    }),
    prisma.image.aggregate({
      where: { studioId },
      _sum: { size: true },
    }),
  ]);

  return {
    totalClients,
    activeClients,
    totalImages,
    recentClients,
    recentImages,
    storageUsed: storageUsed._sum.size || 0,
  };
};

// Get recent activity
export const getRecentActivity = async (studioId: string, limit: number = 10) => {
  const [recentClients, recentImages] = await Promise.all([
    prisma.client.findMany({
      where: { studioId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        name: true,
        email: true,
      },
    }),
    prisma.image.findMany({
      where: { studioId },
      orderBy: { uploadedAt: 'desc' },
      take: limit,
      select: {
        id: true,
        filename: true,
        originalName: true,
        client: {
          select: {
            name: true,
          },
        },
      },
    }),
  ]);

  return {
    recentClients,
    recentImages,
  };
};
